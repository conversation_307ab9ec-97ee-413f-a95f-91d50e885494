from bolfak.storage.core.organisation_record import SubscriptionException
import pytest
from unittest.mock import MagicMock

from bolfak.scripts.subscribe_all_organisation_records_fixup import (
    fetch_and_subscribe_company_ids,
    fetch_and_process_table,
    SUPPORTED_TABLES
)


@pytest.fixture
def mock_company_ids():
    """Create test company IDs"""
    return [
        '00000000-1fba-4ee7-bcd9-eb93c3438edb',
        '00000000-5345-45ec-b765-7cd2e3d295a7',
    ]


@pytest.fixture
def mock_bda_response(mock_company_ids):
    """Create a mock BDA response with company IDs"""
    class MockResponse:
        ok = True
        status_code = 200
        text = ""

        def json(self):
            return {
                'company_ids': mock_company_ids,
                'has_more': False
            }
    return MockResponse()


@pytest.fixture
def mock_storage(mock_bda_response):
    """Create a mock storage object with BDA and Core clients"""
    storage = MagicMock()

    # Mock BDA client
    storage.bda = MagicMock()
    storage.bda.get.return_value = mock_bda_response

    # Mock Core client
    storage.core = MagicMock()

    return storage


def test_fetch_and_process_table(mocker, mock_storage, mock_company_ids):
    """
    Test that fetch_and_process_table correctly processes a table and subscribes to
    organisation records
    """
    # Mock the subscribe_organisationrecords function
    mock_subscribe = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.subscribe_organisationrecords',
        return_value=[MagicMock(), MagicMock()]  # Return some mock results
    )

    # Mock get_org to return companies that are not subscribed
    mock_get_org = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.get_org',
        return_value={'bol_subscription': None}  # Not subscribed
    )

    # Set for storing unique company ids
    unique_company_ids = set()

    # Call the function
    total_processed, success_count, error_count, already_subscribed_count = fetch_and_process_table(
        mock_storage, 'suppliers', batch_size=10, dry_run=False,
        unique_company_ids=unique_company_ids
    )

    # Verify that get_org was called for each company
    assert mock_get_org.call_count == len(mock_company_ids)

    # Verify that subscribe_organisationrecords was called
    assert mock_subscribe.call_count > 0

    # Verify that BDA API was called with the correct table parameter
    mock_storage.bda.get.assert_called_with(
        '/companies/all-ids?table=suppliers&batch_size=10&last_id='
    )

    # Verify the return values
    assert total_processed == len(mock_company_ids)
    assert success_count == len(mock_company_ids)
    assert error_count == 0
    assert already_subscribed_count == 0


def test_fetch_and_process_table_exception_thrown(mocker, mock_storage, mock_company_ids):
    """
    Test that fetch_and_process_table correctly processes handles the case where
    at least one company causes subscribe_organisationrecords to raise an exception.
    """
    # Mock the subscribe_organisationrecords function
    # Simulate that one company causes the function to raise a SubscriptionException
    mock_subscribe = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.subscribe_organisationrecords',
        return_value=[MagicMock()],
        side_effect=SubscriptionException(
            "Failed to subscribe to organisation records",
            [{"organisation": MagicMock(), "error": "error"}])
    )

    # Mock get_org to return companies that are not subscribed
    mock_get_org = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.get_org',
        return_value={'bol_subscription': None}  # Not subscribed
    )

    # Set for storing unique company ids
    unique_company_ids = set()

    # Call the function
    total_processed, success_count, error_count, already_subscribed_count = fetch_and_process_table(
        mock_storage, 'suppliers', batch_size=10, dry_run=False,
        unique_company_ids=unique_company_ids
    )

    # Verify that get_org was called for each company
    assert mock_get_org.call_count == len(mock_company_ids)

    # Verify that subscribe_organisationrecords was called
    assert mock_subscribe.call_count > 0

    # Verify the return values
    assert total_processed == len(mock_company_ids)
    assert success_count == 1
    assert error_count == 1
    assert already_subscribed_count == 0


def test_fetch_and_process_table_dry_run(mocker, mock_storage, mock_company_ids):
    """
    Test that fetch_and_process_table doesn't call subscribe_organisationrecords in dry run mode
    """
    # Mock the subscribe_organisationrecords function
    mock_subscribe = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.subscribe_organisationrecords'
    )

    # Mock get_org to return companies that are not subscribed
    mock_get_org = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.get_org',
        return_value={'bol_subscription': None}  # Not subscribed
    )

    # Set for storing unique company ids
    unique_company_ids = set()

    # Call the function in dry run mode
    total_processed, success_count, error_count, already_subscribed_count = fetch_and_process_table(
        mock_storage, 'projects', batch_size=10, dry_run=True,
        unique_company_ids=unique_company_ids
    )

    # Verify that get_org was called for each company
    assert mock_get_org.call_count == len(mock_company_ids)

    # Verify that subscribe_organisationrecords was not called
    mock_subscribe.assert_not_called()

    # Verify that BDA API was called with the correct table parameter
    mock_storage.bda.get.assert_called_with(
        '/companies/all-ids?table=projects&batch_size=10&last_id='
    )

    # Verify the return values
    assert total_processed == len(mock_company_ids)
    assert success_count == len(mock_company_ids)
    assert error_count == 0
    assert already_subscribed_count == 0


def test_fetch_and_subscribe_company_ids(mocker, mock_storage):
    """
    Test that fetch_and_subscribe_company_ids uses subscribe_organisationrecords
    """
    # Mock the fetch_and_process_table function to return some results
    mock_process = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.fetch_and_process_table',
        # (total_processed, success_count, error_count, already_subscribed)
        return_value=(2, 2, 0, 0)
    )

    # Mock feature_active to return True
    mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.feature_active',
        return_value=True
    )

    # Call the function
    fetch_and_subscribe_company_ids(mock_storage, batch_size=10, dry_run=False)

    # Verify that fetch_and_process_table was called for each table
    assert mock_process.call_count == len(SUPPORTED_TABLES)


def test_fetch_and_subscribe_company_ids_dry_run(mocker, mock_storage):
    """
    Test that fetch_and_subscribe_company_ids passes the dry_run parameter correctly
    """
    # Mock the fetch_and_process_table function
    mock_process = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.fetch_and_process_table',
        # (total_processed, success_count, error_count, already_subscribed)
        return_value=(2, 2, 0, 0)
    )

    # Mock feature_active to return True
    mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.feature_active',
        return_value=True
    )

    # Call the function in dry run mode
    fetch_and_subscribe_company_ids(mock_storage, batch_size=10, dry_run=True)

    # Verify that fetch_and_process_table was called with dry_run=True
    for call in mock_process.call_args_list:
        args, _ = call
        assert args[3] is True  # Check that the dry_run parameter is True


def test_fetch_and_subscribe_company_ids_feature_disabled(mocker, mock_storage):
    """
    Test that fetch_and_subscribe_company_ids exits when the core_mitt_id06 feature is disabled
    """
    # Mock the fetch_and_process_table function
    # (not used in this test but patched to prevent side effects)
    mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.fetch_and_process_table'
    )

    # Mock feature_active to return False for 'core_mitt_id06' specifically
    feature_active_mock = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.feature_active'
    )
    # Configure the mock to return False only when called with 'core_mitt_id06'
    feature_active_mock.side_effect = lambda flag: False if flag == 'core_mitt_id06' else True

    # Mock sys.exit to avoid actually exiting during the test
    mock_exit = mocker.patch('sys.exit')

    # Call the function
    fetch_and_subscribe_company_ids(mock_storage, batch_size=10, dry_run=False)

    # Verify that sys.exit was called
    mock_exit.assert_called_once_with(1)

    # Verify that fetch_and_process_table was not called
    # We don't need to check this since sys.exit is called before any processing happens
    # mock_process.assert_not_called()


def test_fetch_and_process_table_already_subscribed(mocker, mock_storage, mock_company_ids):
    """
    Test that fetch_and_process_table skips companies that are already subscribed
    """
    # Mock the subscribe_organisationrecords function
    mock_subscribe = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.subscribe_organisationrecords',
        return_value=[]  # No subscriptions should happen
    )

    # Mock get_org to return companies that are already subscribed
    mock_get_org = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.get_org',
        return_value={'bol_subscription': {'some': 'data'}}  # Already subscribed
    )

    # Set for storing unique company ids
    unique_company_ids = set()

    # Call the function
    total_processed, success_count, error_count, already_subscribed_count = fetch_and_process_table(
        mock_storage, 'suppliers', batch_size=10, dry_run=False,
        unique_company_ids=unique_company_ids
    )

    # Verify that get_org was called for each company
    assert mock_get_org.call_count == len(mock_company_ids)

    # Verify that subscribe_organisationrecords was not called (all companies already subscribed)
    mock_subscribe.assert_not_called()

    # Verify the return values
    assert total_processed == len(mock_company_ids)
    assert success_count == 0  # No new subscriptions
    assert error_count == 0
    assert already_subscribed_count == len(mock_company_ids)  # All companies already subscribed


def test_fetch_and_process_table_unique_company_ids(mocker, mock_storage):
    """
    Test that fetch_and_process_table correctly handles unique company IDs across tables,
    including overlapping IDs between tables.
    """
    # Define overlapping company IDs for suppliers and projects
    supplier_ids = [
        '00000000-1fba-4ee7-bcd9-eb93c3438edb',
        '00000000-5345-45ec-b765-7cd2e3d295a7',
        '00000000-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
    ]
    project_ids = [
        '00000000-5345-45ec-b765-7cd2e3d295a7',  # Overlap
        '00000000-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
        '00000000-cccc-cccc-cccc-cccccccccccc',
    ]
    all_unique_ids = set(supplier_ids) | set(project_ids)

    # Patch the BDA response to return different IDs for each table
    class MockResponse:
        ok = True
        status_code = 200
        text = ""

        def __init__(self, ids):
            self._ids = ids

        def json(self):
            return {'company_ids': self._ids, 'has_more': False}

    def bda_get_side_effect(url):
        if 'suppliers' in url:
            return MockResponse(supplier_ids)
        elif 'projects' in url:
            return MockResponse(project_ids)
        else:
            return MockResponse([])

    mock_storage.bda.get.side_effect = bda_get_side_effect

    # Mock the subscribe_organisationrecords function
    mock_subscribe = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.subscribe_organisationrecords',
        return_value=[MagicMock() for _ in all_unique_ids]
    )

    # Mock get_org to return companies that are not subscribed
    mock_get_org = mocker.patch(
        'bolfak.scripts.subscribe_all_organisation_records_fixup.get_org',
        return_value={'bol_subscription': None}  # Not subscribed
    )

    unique_company_ids = set()

    # Call the function for both tables
    fetch_and_process_table(
        mock_storage, 'suppliers', batch_size=10, dry_run=False,
        unique_company_ids=unique_company_ids
    )
    fetch_and_process_table(
        mock_storage, 'projects', batch_size=10, dry_run=False,
        unique_company_ids=unique_company_ids
    )

    # Should call get_org for each unique company ID
    assert mock_get_org.call_count == len(all_unique_ids)

    # Collect all uuids passed to subscribe_organisationrecords across all calls
    all_called_uuids = set()
    for call in mock_subscribe.call_args_list:
        called_organisations = call[0][1]
        all_called_uuids.update(org.uuid for org in called_organisations)

    assert all_called_uuids == all_unique_ids

    # The unique_company_ids set should contain all unique IDs
    assert unique_company_ids == all_unique_ids
