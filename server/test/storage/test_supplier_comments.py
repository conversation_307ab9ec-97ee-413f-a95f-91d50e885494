from bolfak.fixtures import factories
from bolfak.storage import supplier_comments


def test_create_and_get_supplier_comment(storage):
    project = factories.create_default_project(storage)
    suppliers_tree = factories.create_suppliers_tree(storage, project,
                                                     [factories.supplier('Supplier1')])
    supplier_id = suppliers_tree[0].id
    person = factories.create_person(storage)
    comment_obj = supplier_comments.create_supplier_comment(
        storage,
        project_id=project["id"],
        supplier_id=supplier_id,
        org_id="some-org-id",
        commentor_person_id=person["id"],
        commentor_org_id="my-org-id",
        comment_text="a test comment",
    )
    assert comment_obj.comment == 'a test comment'
    assert comment_obj.project_id == project['id']
    [comment] = supplier_comments.get_supplier_comments(
        storage, supplier_id, reader_person_id=person["id"]
    )

    assert comment.id == comment_obj.id
    assert comment.project_id == project['id']
    assert comment.supplier_id == supplier_id
    assert comment.org_id == 'some-org-id'
    assert comment.comment == 'a test comment'
    assert comment.created_by_org_id == 'my-org-id'
    assert comment.created_by_person_id == person['id']
    assert comment.created_timestamp is not None
    assert comment.is_deleted is False
    assert comment.deleted_by_org_id is None
    assert comment.deleted_by_person_id is None
    assert comment.deleted_timestamp is None
    assert comment.is_updated is False
    assert comment.updated_by_org_id is None
    assert comment.updated_by_person_id is None
    assert comment.updated_timestamp is None
    assert comment.modified_timestamp is None
    assert comment.is_read is True


def test_get_supplier_comments(storage):
    project = factories.create_default_project(storage)
    suppliers_tree = factories.create_suppliers_tree(
        storage, project, [factories.supplier("Supplier2")]
    )
    supplier_id = suppliers_tree[0].id
    comment_obj = supplier_comments.create_supplier_comment(
        storage,
        project_id=project["id"],
        supplier_id=supplier_id,
        org_id="some-org-id",
        commentor_person_id="person-id-1",
        commentor_org_id="my-org-id",
        comment_text="Read test comment",
    )
    [comment] = supplier_comments.get_supplier_comments(
        storage, supplier_id, reader_person_id="person-id-1"
    )
    assert comment.id == comment_obj.id
    assert comment.project_id == project["id"]
    assert comment.supplier_id == supplier_id
    assert comment.org_id == "some-org-id"
    assert comment.comment == "Read test comment"
    assert comment.created_by_org_id == "my-org-id"
    assert comment.created_by_person_id == "person-id-1"
    assert comment.created_timestamp is not None
    assert comment.is_deleted is False
    assert comment.deleted_by_org_id is None
    assert comment.deleted_by_person_id is None
    assert comment.deleted_timestamp is None
    assert comment.is_updated is False
    assert comment.updated_by_org_id is None
    assert comment.updated_by_person_id is None
    assert comment.updated_timestamp is None
    assert comment.modified_timestamp is None
    assert comment.is_read is True


def test_mark_comment_as_read(storage):
    project = factories.create_default_project(storage)
    org = factories.create_org(storage)
    suppliers_tree = factories.create_suppliers_tree(storage, project,
                                                     [factories.supplier('Supplier2')])
    supplier_id = suppliers_tree[0].id
    comment_obj = supplier_comments.create_supplier_comment(
        storage,
        project_id=project["id"],
        supplier_id=supplier_id,
        org_id=org["id"],
        commentor_person_id="person-id-1",
        commentor_org_id=org["id"],
        comment_text="Read test comment",
    )
    viewer = supplier_comments.mark_comment_as_read(
        storage, comment_obj.id, "person-id-2"
    )
    [read_comment] = supplier_comments.get_supplier_comments_for_project(
        storage, project["id"], reader_person_id="person-id-2"
    )
    [unread_comment] = supplier_comments.get_supplier_comments_for_project(
        storage, project["id"], reader_person_id="person-id-3"
    )
    assert viewer.comment_id == comment_obj.id
    assert viewer.read_by_person_id == 'person-id-2'
    assert read_comment.id == comment_obj.id
    assert read_comment.is_read is True
    assert unread_comment.id == comment_obj.id
    assert unread_comment.is_read is False
