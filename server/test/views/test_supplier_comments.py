import pytest
from bolfak.fixtures import factories
from bolfak.models import (
    MAIN_CONTRACTOR_ROLE,
    Comment,
)
import datetime
from bolfak.storage import supplier_comments


def _setup_client_project_supplier_org(storage):
    client = factories.get_or_create_org(storage, "Cheap Woodworking Ltd.")
    project = factories.create_default_project(
        storage,
        "Comments Test Project",
        org=client,
        pa_form_enabled=True,
        added_client_confirmed=True,
        added_client_can_view=True,
        project_creator_role=MAIN_CONTRACTOR_ROLE,
    )
    org = factories.create_org(storage, name="Coorganisation")
    suppliers = [
        factories.supplier(org),
    ]
    supplier_tree = factories.create_suppliers_tree(
        storage, project, org=client, suppliers=suppliers
    )
    person = factories.create_person(storage, names="Commentor Person")
    return client, project["id"], supplier_tree[0].id, org["id"], person["id"]


def test_get_supplier_comments(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags({"project_supplier_comments": True, "bda_project_suppliers": True})
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client)
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id=person_id,
        commentor_org_id=app.active_org_id,
        text="Read test comment",
    )

    resp = app.get_with_org(f"/api/supplier/{supplier_id}/comments")

    assert resp.status_code == 200
    assert len(resp.json["comments"]) == 1
    assert resp.json["comments"][0] == {
        "id": comment.id,
        "comment": "Read test comment",
        "project_id": project_id,
        "supplier_id": supplier_id,
        "org_id": org_id,
        "author": "Commentor Person, Cheap Woodworking Ltd.",
        "updater": None,
        "deleter": None,
        "created_timestamp": comment.created_timestamp.isoformat(timespec="seconds"),
        "updated_timestamp": None,
        "deleted_timestamp": None,
        "is_read": False,
        "permissions": ["update", "delete"],
    }


def test_get_supplier_comments_access_with_wrong_org(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags({"project_supplier_comments": True, "bda_project_suppliers": True})
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    querying_org = factories.get_or_create_org(storage, "Expensive Woodworking Ltd.")
    app.login(querying_org)
    factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id="some-person-id",
        commentor_org_id=person_id,
        text="Read test comment",
    )
    resp = app.get_with_org(f"/api/supplier/{supplier_id}/comments", expect_errors=True)

    assert resp.status_code == 404


def test_get_supplier_comments_deleted_formatting(AppWithFeatureFlags, storage, mocker):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client)

    # Mocking this since deleting and updating comments are not implemented yet
    now = datetime.datetime.now()
    mocker.patch(
        "bolfak.storage.supplier_comments.get_supplier_comments",
        return_value=[
            Comment(
                id="1234",
                project_id=project_id,
                supplier_id=supplier_id,
                org_id=org_id,
                comment="This is the original text",
                created_by_org_id=app.active_org_id,
                created_by_person_id=person_id,
                created_timestamp=now,
                is_deleted=True,
                deleted_by_org_id=app.active_org_id,
                deleted_by_person_id=person_id,
                deleted_timestamp=now,
                is_updated=True,
                updated_by_org_id=app.active_org_id,
                updated_by_person_id=person_id,
                updated_timestamp=now,
                modified_timestamp=now,
                is_read=True,
            )
        ],
    )

    resp = app.get_with_org(f"/api/supplier/{supplier_id}/comments")
    assert resp.status_code == 200
    assert len(resp.json["comments"]) == 1
    assert resp.json["comments"][0] == {
        "id": "1234",
        "comment": None,
        "project_id": project_id,
        "supplier_id": supplier_id,
        "org_id": org_id,
        "author": "Commentor Person, Cheap Woodworking Ltd.",
        "updater": "Commentor Person, Cheap Woodworking Ltd.",
        "deleter": "Commentor Person, Cheap Woodworking Ltd.",
        "created_timestamp": now.isoformat(timespec="seconds"),
        "updated_timestamp": now.isoformat(timespec="seconds"),
        "deleted_timestamp": now.isoformat(timespec="seconds"),
        "is_read": True,
        "permissions": [],
    }


def test_get_supplier_comments_missing_person(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client)
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id="person-id-that-was-deleted",
        commentor_org_id=app.active_org_id,
        text="Read test comment",
    )

    resp = app.get_with_org(f"/api/supplier/{supplier_id}/comments")

    assert resp.status_code == 200
    assert len(resp.json["comments"]) == 1
    assert resp.json["comments"][0] == {
        "id": comment.id,
        "comment": "Read test comment",
        "project_id": project_id,
        "supplier_id": supplier_id,
        "org_id": org_id,
        "author": "Cheap Woodworking Ltd.",
        "updater": None,
        "deleter": None,
        "created_timestamp": comment.created_timestamp.isoformat(timespec="seconds"),
        "updated_timestamp": None,
        "deleted_timestamp": None,
        "is_read": False,
        "permissions": ["update", "delete"],
    }


def test_get_supplier_comments_missing_org(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client)
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id=person_id,
        commentor_org_id="org-id-that-was-deleted",
        text="Read test comment",
    )

    resp = app.get_with_org(f"/api/supplier/{supplier_id}/comments")

    assert resp.status_code == 200
    assert len(resp.json["comments"]) == 1
    assert resp.json["comments"][0] == {
        "id": comment.id,
        "comment": "Read test comment",
        "project_id": project_id,
        "supplier_id": supplier_id,
        "org_id": org_id,
        "author": "Commentor Person",
        "updater": None,
        "deleter": None,
        "created_timestamp": comment.created_timestamp.isoformat(timespec="seconds"),
        "updated_timestamp": None,
        "deleted_timestamp": None,
        "is_read": False,
        "permissions": [],
    }


def test_add_supplier_comment(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client, "Erik Erikssen")
    resp = app.post_json_with_org(
        f"/api/supplier/{supplier_id}/add-comment",
        {"comment": "Some important comment"},
    )
    comment = resp.json["comment"]
    db_comment = supplier_comments.get_supplier_comment(app.storage, comment["id"])
    assert resp.status_code == 200
    assert comment == {
        "id": comment["id"],
        "comment": "Some important comment",
        "project_id": project_id,
        "supplier_id": supplier_id,
        "org_id": org_id,
        "author": "Erik Erikssen, Cheap Woodworking Ltd.",
        "updater": None,
        "deleter": None,
        "created_timestamp": comment["created_timestamp"],
        "updated_timestamp": None,
        "deleted_timestamp": None,
        "is_read": True,
        "permissions": ["update", "delete"],
    }
    assert db_comment.id == comment["id"]
    assert db_comment.comment == "Some important comment"
    assert db_comment.project_id == project_id
    assert db_comment.supplier_id == supplier_id
    assert db_comment.org_id == org_id
    assert db_comment.created_by_org_id == app.active_org_id
    assert db_comment.created_by_person_id == app.active_person_id
    assert db_comment.created_timestamp is not None


def test_add_supplier_comment_empty_comment(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client, "Erik Erikssen")
    resp = app.post_json_with_org(
        f"/api/supplier/{supplier_id}/add-comment",
        {"comment": ""},
    )
    assert resp.status_code == 200
    assert resp.json == {
        "ok": False,
        "errors": {"comment": ["String value is too short."]},
    }


def test_update_supplier_comment(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client, "Erik Erikssen")
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id=person_id,
        commentor_org_id=app.active_org_id,
        text="Some important comment",
    )
    resp1 = app.post_json_with_org(
        f"/api/supplier/{supplier_id}/comments/{comment.id}/update",
        {"comment": "Updated comment1"},
    )
    comment = supplier_comments.get_supplier_comment(app.storage, comment.id)

    assert resp1.status_code == 200
    assert resp1.json["comment"]["updater"] == "Erik Erikssen, Cheap Woodworking Ltd."
    assert comment.comment == "Updated comment1"
    assert comment.project_id == project_id
    assert comment.supplier_id == supplier_id
    assert comment.org_id == org_id
    assert comment.created_by_org_id == app.active_org_id
    assert comment.created_by_person_id == person_id
    assert comment.is_updated is True
    assert comment.updated_by_org_id == app.active_org_id
    assert comment.updated_by_person_id == app.active_person_id
    assert comment.updated_timestamp is not None


def test_update_supplier_comment_consecutive_updates(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client, "Erik Erikssen")
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id=person_id,
        commentor_org_id=app.active_org_id,
        text="Some important comment",
    )
    resp1 = app.post_json_with_org(
        f"/api/supplier/{supplier_id}/comments/{comment.id}/update",
        {"comment": "Updated comment1"},
    )
    app.login(client, "Not Erik Erikssen")
    resp2 = app.post_json_with_org(
        f"/api/supplier/{supplier_id}/comments/{comment.id}/update",
        {"comment": "Updated comment2"},
    )
    comment = supplier_comments.get_supplier_comment(app.storage, comment.id)

    assert resp1.status_code == 200
    assert resp2.status_code == 200
    assert resp1.json["comment"]["updater"] == "Erik Erikssen, Cheap Woodworking Ltd."
    assert (
        resp2.json["comment"]["updater"] == "Not Erik Erikssen, Cheap Woodworking Ltd."
    )
    assert comment.comment == "Updated comment2"
    assert comment.project_id == project_id
    assert comment.supplier_id == supplier_id
    assert comment.org_id == org_id
    assert comment.created_by_org_id == app.active_org_id
    assert comment.created_by_person_id == person_id
    assert comment.is_updated is True
    assert comment.updated_by_org_id == app.active_org_id
    assert comment.updated_by_person_id == app.active_person_id
    assert comment.updated_timestamp is not None


@pytest.mark.parametrize(
    ["field", "value"],
    [
        ("project_id", "Updated project1"),
        ("supplier_id", "Updated supplier1"),
        ("org_id", "Updated org1"),
    ],
)
def test_update_supplier_comment_cannot_update_other_fields(
    AppWithFeatureFlags, storage, field, value
):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client, "Erik Erikssen")
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id=person_id,
        commentor_org_id=app.active_org_id,
        text="Some important comment",
    )
    resp1 = app.post_json_with_org(
        f"/api/supplier/{supplier_id}/comments/{comment.id}/update",
        {
            "comment": "Updated comment1",
            field: value,
        },
    )
    comment = supplier_comments.get_supplier_comment(app.storage, comment.id)

    assert resp1.status_code == 200
    assert resp1.json["ok"] is False
    assert resp1.json["errors"][field] == ["Rogue field"]
    assert comment.comment == "Some important comment"
    assert comment.project_id == project_id
    assert comment.supplier_id == supplier_id
    assert comment.org_id == org_id
    assert comment.created_by_org_id == app.active_org_id
    assert comment.created_by_person_id == person_id
    assert comment.is_updated is False
    assert comment.updated_by_org_id is None
    assert comment.updated_by_person_id is None
    assert comment.updated_timestamp is None
    assert comment.deleted_by_org_id is None
    assert comment.deleted_by_person_id is None
    assert comment.deleted_timestamp is None


def test_mark_supplier_comment_deleted(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client, project_id, supplier_id, org_id, person_id = (
        _setup_client_project_supplier_org(storage)
    )
    app.login(client, "Erik Erikssen")
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=org_id,
        commentor_person_id=person_id,
        commentor_org_id=app.active_org_id,
        text="Some important comment",
    )
    resp1 = app.post_with_org(
        f"/api/supplier/{supplier_id}/comments/{comment.id}/mark-deleted"
    )
    resp2 = app.post_with_org(
        f"/api/supplier/{supplier_id}/comments/{comment.id}/mark-deleted",
        expect_errors=True,
    )
    comment = supplier_comments.get_supplier_comment(app.storage, comment.id)

    assert resp1.status_code == 200
    assert resp1.json["ok"] is True
    assert resp1.json["comment"]["deleter"] == "Erik Erikssen, Cheap Woodworking Ltd."
    assert resp1.json["comment"]["deleted_timestamp"] is not None
    assert resp2.status_code == 403
    assert comment.is_deleted is True
    assert comment.comment == "Some important comment"
    assert comment.deleted_by_org_id == app.active_org_id
    assert comment.deleted_by_person_id == app.active_person_id
    assert comment.deleted_timestamp is not None
    assert comment.project_id == project_id
    assert comment.supplier_id == supplier_id
    assert comment.org_id == org_id
    assert comment.created_by_org_id == app.active_org_id
    assert comment.created_by_person_id == person_id
    assert comment.created_timestamp is not None
    assert comment.is_updated is False
    assert comment.updated_by_org_id is None
    assert comment.updated_by_person_id is None
    assert comment.updated_timestamp is None


def test_mark_supplier_comment_deleted_supplier_org(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {"project_supplier_comments": True, "bda_project_suppliers": True}
    )
    client = factories.get_or_create_org(storage, "Cheap Woodworking Ltd.")
    supplier = factories.get_or_create_org(storage, "Expensive Woodworking Ltd.")
    project_id = factories.create_default_project(
        storage,
        "Comments Test Project",
        org=client,
        pa_form_enabled=True,
        added_client_confirmed=True,
        added_client_can_view=True,
        project_creator_role=MAIN_CONTRACTOR_ROLE,
    )["id"]
    person = factories.create_person(storage, names="Commentor Person")
    suppliers = [
        factories.supplier(supplier),
    ]
    supplier_tree = factories.create_suppliers_tree(
        storage, "Comments Test Project", org=client, suppliers=suppliers
    )
    supplier_id = supplier_tree[0].id
    comment = factories.create_supplier_comment(
        storage,
        project_id=project_id,
        supplier_id=supplier_id,
        org_id=supplier["id"],
        commentor_person_id=person["id"],
        commentor_org_id=client["id"],
        text="Some important comment",
    )
    app.login(supplier)
    resp1 = app.post_with_org(
        f"/api/supplier/{supplier_id}/comments/{comment.id}/mark-deleted",
        expect_errors=True,
    )
    comment = supplier_comments.get_supplier_comment(app.storage, comment.id)

    assert resp1.status_code == 404
    assert comment.is_deleted is False
    assert comment.comment == "Some important comment"
    assert comment.deleted_by_org_id is None
    assert comment.deleted_by_person_id is None
    assert comment.deleted_timestamp is None
