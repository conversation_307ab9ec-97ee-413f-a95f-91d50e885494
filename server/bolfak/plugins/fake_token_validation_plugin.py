from functools import wraps
import jwt
import bottle
import logging

logger = logging.getLogger(__name__)


class FakeTokenValidationPlugin():
    name = 'fake_token_validation'
    api = 2

    def apply(self, callback, route):
        if getattr(callback, '_validate_token', False):
            @wraps(callback)
            def wrapper(*args, **kwargs):
                fake_access_token = bottle.request.headers['Authorization']
                jwtToken = jwt.decode(fake_access_token.split(
                    " ")[1], algorithms='HS256', key='NO SECRET')
                bottle.request.environ["scopes"] = jwtToken["scopes"]
                bottle.request.environ["client_id"] = 'doweneedthis'
                bottle.request.environ["user_id"] = jwtToken["sub"]
                return callback(*args, **kwargs)
            return wrapper
        else:
            return callback
