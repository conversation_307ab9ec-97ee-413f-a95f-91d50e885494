"""TypedDict definitions for Qvarn entities.

This module defines specific TypedDict classes to replace the generic QvarnResultDict
with more precise type definitions for various Qvarn resources.
"""

from typing import Any, Dict, NotRequired, TypedDict, Union, Optional

from qvarnclient.qvarnapi import QvarnResultDict as QvarnClientResultDict


# Common structures used across multiple entity types
class _GovOrgId(TypedDict):
    country: str
    gov_org_id: str
    org_id_type: str


class _QvarnContact(TypedDict):
    contact_type: NotRequired[str]
    service_provider: NotRequired[str]
    contact_person: NotRequired[str]
    email_address: NotRequired[str]
    phone_number: NotRequired[str]


class _Permission(TypedDict):
    permission_name: str


class _ProjectId(TypedDict):
    project_id_type: str
    project_id: str


class _SupplierContact(TypedDict):
    contact_type: str
    person_id: NotRequired[str]
    email: NotRequired[str]
    phone: NotRequired[str]
    name: NotRequired[str]
    # Links supplier contact to a person record in views/helpers/project_tree.py:_get_tree_persons
    supplier_contact_person_id: NotRequired[Optional[str]]


class _PersonName(TypedDict):
    full_name: str
    given_names: list[str]
    surnames: list[str]
    titles: NotRequired[list[str]]
    sort_key: NotRequired[str]


class _ContractParty(TypedDict):
    type: str  # 'person' or 'org'
    resource_id: str
    role: str  # 'user', 'target', etc.
    user_role: NotRequired[str]
    username: NotRequired[str]
    global_permissions: NotRequired[list[_Permission]]
    permissions: NotRequired[list[_Permission]]


class QvarnOrgResultDict(TypedDict):
    """TypedDict for Qvarn Organization/Company resources."""
    # Fields that are always present
    id: str
    names: list[str]
    country: Optional[str]  # Still Optional as it might be None
    gov_org_ids: list[_GovOrgId]

    # Fields that may not be present in all cases
    type: NotRequired[str]  # Could be "org", created by Qvarn API in qvarnclient/qvarnapi.py
    revision: NotRequired[str]  # Created by Qvarn API in qvarnclient/qvarnapi.py
    legal_entity_type: NotRequired[Optional[str]]
    bol_subscription: NotRequired[Optional[dict]]
    organisation_uuid: NotRequired[Optional[str]]
    external_id: NotRequired[Optional[str]]  # Present in external_org_record_to_org
    contacts: NotRequired[list[_QvarnContact]]
    is_luotettava_kumppani_member: NotRequired[bool]  # Vastuu Group "Trusted Partner" (in Finnish)


class QvarnPersonResultDict(TypedDict):
    """TypedDict for Qvarn Person resources."""
    id: str
    type: str  # Usually "person"
    revision: Optional[str]
    names: list[_PersonName]
    gluu_user_id: NotRequired[Optional[str]]
    private: NotRequired[Dict[str, Any]]  # Contains contacts, gov_ids, etc.


class QvarnProjectResultDict(TypedDict):
    """TypedDict for Qvarn Project resources."""
    id: str
    type: NotRequired[str]  # Usually "project"
    revision: NotRequired[str]
    names: list[str]
    project_responsible_org: str
    project_responsible_person: NotRequired[str]
    project_ids: list[_ProjectId]
    state: NotRequired[str]
    start_date: NotRequired[str]  # Date string, "yyyy-mm-dd"
    end_date: NotRequired[str]  # Date string, "yyyy-mm-dd"
    pa_form_enabled: NotRequired[bool]
    created_by_org_id: NotRequired[str]
    client_contact_person_email: NotRequired[str]
    client_contact_person_id: NotRequired[str]
    added_client_confirmed: NotRequired[bool]
    project_creator_role: NotRequired[str]
    added_client_can_view: NotRequired[bool]


class QvarnContractResultDict(TypedDict):
    """TypedDict for Qvarn Contract resources."""
    id: str
    type: str  # Usually "contract"
    revision: NotRequired[str]
    contract_type: str
    contract_parties: list[_ContractParty]
    contract_state: str
    service_provider: NotRequired[str]
    preferred_language: NotRequired[str]


class QvarnSupplierResultDict(TypedDict):
    """TypedDict for Qvarn Supplier resources."""
    id: str
    type: str  # Usually "supplier"
    revision: NotRequired[str]
    project_resource_id: str
    supplier_type: str
    parent_org_id: NotRequired[str]
    supplier_org_id: str
    materialized_path: list[str]
    supplier_role: str
    supplier_contacts: NotRequired[list[_SupplierContact]]
    contract_start_date: NotRequired[str]
    contract_end_date: NotRequired[str]
    contract_work_areas: NotRequired[str]
    parent_supplier_id: NotRequired[str]
    first_visited: NotRequired[str]
    last_visited: NotRequired[str]
    contract_type: NotRequired[str]
    visitor_type: NotRequired[str]
    is_one_man_company: NotRequired[bool]
    has_collective_agreement: NotRequired[Optional[bool]]
    collective_agreement_name: NotRequired[Optional[str]]
    pa_id: NotRequired[str]  # Preannouncement ID linking supplier to PA workflow


class QvarnStatusReportResultDict(TypedDict):
    """TypedDict for Qvarn Status Report resources."""
    id: str
    type: NotRequired[str]  # Usually "report"
    revision: NotRequired[str]
    org: str  # Company/organization ID
    report_type: NotRequired[str]  # Usually "bolagsfakta.company_report"
    generated_timestamp: str
    tilaajavastuu_status: str  # Status code stored in DB
    interested_org_id: NotRequired[str]  # Interested organization ID

    # Fields from the original model - may be mapped differently in practice
    organization_id: NotRequired[str]  # Mapped to 'org' in many cases
    status: NotRequired[str]  # May be mapped to 'tilaajavastuu_status'
    timestamp: NotRequired[str]  # May be mapped to 'generated_timestamp'
    created_at: NotRequired[str]
    interested_organization_id: NotRequired[str]  # May be mapped to 'interested_org_id'
    source: NotRequired[str]
    report_access_id: NotRequired[str]
    format: NotRequired[str]

    # Report content fields
    json: NotRequired[dict]  # Raw report data
    clreports_interpretation: NotRequired[str]  # Overall status interpretation
    charge_reference: NotRequired[str]  # Reference for billing
    used_providers: NotRequired[list[str]]  # Data providers used for this report
    report_version_id06: NotRequired[str]  # Version of the ID06 report format

    # Additional fields used in code but not consistently defined in the model
    pdf: NotRequired[Dict[str, str]]  # PDF content with body and content_type


class QvarnCardResultDict(TypedDict):
    """TypedDict for Qvarn Card resources."""
    id: str
    type: str  # Usually "card"
    revision: NotRequired[str]
    org: str
    current_status: str
    card_id: NotRequired[str]
    card_type: NotRequired[str]
    person_id: NotRequired[str]


class QvarnUserAccountResultDict(TypedDict):
    """TypedDict for Qvarn User Account resources."""
    id: str
    email: NotRequired[str]
    person_id: str
    representations: NotRequired[list[Dict[str, Any]]]  # Organizations with permissions
    contract_parties: NotRequired[list[Dict[str, Any]]]  # Also, same as representations above?!
    revision: NotRequired[str]
    preferred_language: NotRequired[str]


# Legacy type, for compatibility
QvarnResultDict = Union[
    QvarnOrgResultDict,
    QvarnPersonResultDict,
    QvarnProjectResultDict,
    QvarnContractResultDict,
    QvarnSupplierResultDict,
    QvarnStatusReportResultDict,
    QvarnCardResultDict,
    QvarnUserAccountResultDict,
    QvarnClientResultDict  # Fallback for unspecified types
]
