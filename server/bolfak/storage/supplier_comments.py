from typing import List, Optional
from bolfak.clients.composite import Storage
from bolfak.models import Comment, CommentViewer


def get_supplier_comment(storage: Storage, comment_id: str) -> Comment:
    response = storage.bda.get(f'/project_supplier_comments/{comment_id}')
    comment_dict = response.json()
    return Comment(**comment_dict)


def get_supplier_comments_for_project(
    storage: Storage, project_id: str, reader_person_id: Optional[str] = None
) -> List[Comment]:
    url = f'/projects/{project_id}/comments'
    if reader_person_id:
        url += f'?read_by={reader_person_id}'
    response = storage.bda.get(url)
    comment_dicts = response.json()['resources']
    return [Comment(**comment) for comment in comment_dicts]


def get_supplier_comments(
    storage: Storage, supplier_id: str, reader_person_id: Optional[str] = None
) -> List[Comment]:
    url = f"/bol_suppliers/{supplier_id}/comments"
    if reader_person_id:
        url += f"?read_by={reader_person_id}"
    response = storage.bda.get(url)
    comment_dicts = response.json()["resources"]
    return [Comment(**comment) for comment in comment_dicts]


def create_supplier_comment(
    storage: Storage,
    project_id: str,
    supplier_id: str,
    org_id: str,
    commentor_person_id: str,
    commentor_org_id: str,
    comment_text: str,
) -> Comment:
    response = storage.bda.post('/project_supplier_comments', json={
        'project_id': project_id,
        'supplier_id': supplier_id,
        'org_id': org_id,
        'comment': comment_text,
        'created_by_person_id': commentor_person_id,
        'created_by_org_id': commentor_org_id,
    })
    comment_dict = response.json()
    return Comment(**comment_dict)


def update_supplier_comment(
    storage: Storage,
    comment_id: str,
    updated_by_person_id: str,
    updated_by_org_id: str,
    comment_text: str,
) -> Comment:
    response = storage.bda.patch(
        f"/project_supplier_comments/{comment_id}",
        json={
            "comment": comment_text,
            "updating_org_id": updated_by_org_id,
            "updating_person_id": updated_by_person_id,
        },
    )
    comment_dict = response.json()
    return Comment(**comment_dict)


def mark_supplier_comment_as_deleted(
    storage: Storage, comment_id: str, deleted_by_person_id: str, deleted_by_org_id: str
) -> Comment:
    response = storage.bda.patch(
        f"/project_supplier_comments/{comment_id}",
        json={
            "is_deleted": True,
            "updating_org_id": deleted_by_org_id,
            "updating_person_id": deleted_by_person_id,
        },
    )
    comment_dict = response.json()
    return Comment(**comment_dict)


def mark_comment_as_read(
    storage: Storage, comment_id: str, read_by_person_id: str
) -> CommentViewer:
    response = storage.bda.post('/project_comment_viewers', json={
        'comment_id': comment_id,
        'read_by_person_id': read_by_person_id
    })
    viewer_dict = response.json()
    return CommentViewer(**viewer_dict)
