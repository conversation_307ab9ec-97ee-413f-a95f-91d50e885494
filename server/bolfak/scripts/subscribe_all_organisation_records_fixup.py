#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to subscribe all organisation records from the BOL data API.

This script connects to the BOL data API, fetches all company IDs
from multiple tables using the /companies/all-ids endpoint, and subscribes
each organisation record using the subscribe_organisationrecords function.

IMPORTANT: This script is specifically designed for BOL CORE configurations and will
exit immediately if the 'core_mitt_id06' feature flag is not active.
"""

import argparse
import logging
import sys

from core_models import (
    InternalOrganisationSearchResult,
)

from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.sentry import setup_sentry
from bolfak.storage.core.organisation_record import (
    subscribe_organisationrecords,
    SubscriptionException
)
from bolfak.storage.company import get_org
from typing import Set

logger = logging.getLogger(__name__)

# List of supported tables to fetch company IDs from.
# Note: we limit only to these as required in BOL-6544.
# BDA might support more.
SUPPORTED_TABLES = [
    'suppliers', 'projects', 'status_reports',
    'status_reports_history', 'internal_project_ids',
    'creditsafe_account'
]


def fetch_and_process_table(storage, table_name, batch_size, dry_run, unique_company_ids):
    """
    Fetch company IDs from a specific table and subscribe to each organisation record.
    Only subscribes companies that are not already subscribed.

    Args:
        storage: The storage client to use for API requests
        table_name: The name of the table to fetch IDs from
        batch_size: The number of IDs to fetch in each request
        dry_run: If True, don't actually subscribe, just log what would be done
        unique_company_ids: The set of unique company ids across all tables

    Returns:
        tuple: (total_processed, success_count, error_count, already_subscribed_count)
    """
    last_id = ''
    has_more = True
    total_processed = 0
    success_count = 0
    error_count = 0
    already_subscribed_count = 0
    subscription_batch_size = 100  # Process subscriptions in smaller batches

    logger.info(f"Starting to fetch and subscribe company IDs from table '{table_name}'")

    # Fetch and process company IDs in batches
    while has_more:
        # Fetch a batch of company IDs
        response = storage.bda.get(
            f"/companies/all-ids?table={table_name}&batch_size={batch_size}&last_id={last_id}"
        )

        # Check if the response is successful before processing
        if not response.ok:
            logger.error(
                f"BDA API returned error status {response.status_code}: {response.text}"
            )
            break

        data = response.json()
        if 'company_ids' not in data:
            logger.error(f"BDA API returned unexpected response format: {data}")
            break

        company_ids = data['company_ids']

        # Update pagination info
        has_more = data.get('has_more', False)
        if has_more and 'next_cursor' in data:
            last_id = data['next_cursor']
        elif has_more:
            logger.error("BDA API returned has_more=True but no next_cursor")
            has_more = False

        # Process this batch of IDs
        for i in range(0, len(company_ids), subscription_batch_size):
            batch = [c_id for c_id in company_ids[i:i+subscription_batch_size]
                     if c_id not in unique_company_ids]
            total_processed += len(batch)
            unique_company_ids.update(batch)

            # Check subscription status and filter unsubscribed companies
            unsubscribed_companies = []
            for company_id in batch:
                try:
                    # Check if company is already subscribed
                    org = get_org(storage, company_id)
                    bol_subscription = org.get('bol_subscription')

                    if not bol_subscription:
                        # Company is not subscribed (bol_subscription is None or falsy)
                        logger.info(f"Company {company_id} is not subscribed - will subscribe")
                        unsubscribed_companies.append(company_id)
                    else:
                        # Company is already subscribed
                        logger.info(f"Company {company_id} is already subscribed - skipping")
                        already_subscribed_count += 1

                except Exception as e:
                    # Handle cases where get_org fails (company not found, etc.)
                    logger.warning(
                        f"Could not check subscription status for company {company_id}: {str(e)}. "
                        f"Will attempt to subscribe anyway."
                    )
                    unsubscribed_companies.append(company_id)

            # Skip processing if dry run
            if dry_run:
                for company_id in unsubscribed_companies:
                    logger.info(
                        f"[DRY RUN] Would subscribe to organisation record: {company_id}"
                    )
                    success_count += 1
                continue

            # Create InternalOrganisationSearchResult objects for unsubscribed companies only
            organisations = [InternalOrganisationSearchResult(uuid=cid)
                             for cid in unsubscribed_companies]

            # Subscribe to the unsubscribed organisations
            success_count_batch = 0
            if organisations:
                try:
                    # Use subscribe_organisationrecords instead of direct API calls
                    results = subscribe_organisationrecords(storage, organisations)
                    success_count_batch = len(results)
                except SubscriptionException as e:
                    # Handle subscription errors
                    for error in e.errors:
                        org = error["organisation"]
                        err = error["error"]
                        organisation_identifier = getattr(org, 'uuid', org.get(
                            'external_identifier', 'unknown'
                        ))
                        logger.error(
                            f"Error subscribing to organisation record: {organisation_identifier}. "
                            f"Error: {str(err)}"
                        )
                        error_count += 1
                    success_count_batch = (len(organisations) - len(e.errors))
                except Exception as e:
                    # Handle unexpected errors
                    logger.error(f"Unexpected error during subscription: {str(e)}")
                    error_count += len(organisations)

                if (success_count_batch > 0):
                    logger.info(
                        f"Successfully subscribed to {success_count_batch} organisation records"
                    )
                    success_count += success_count_batch

        # Log progress after each API fetch
        logger.info(
            f"Table '{table_name}': Processed {total_processed} company IDs so far. "
            f"Success: {success_count}, Errors: {error_count}, "
            f"Already subscribed: {already_subscribed_count}"
        )

    logger.info(f"Completed processing {total_processed} company IDs from table '{table_name}'")
    logger.info(
        f"Success: {success_count}, Errors: {error_count}, "
        f"Already subscribed: {already_subscribed_count}"
    )

    return total_processed, success_count, error_count, already_subscribed_count


def fetch_and_subscribe_company_ids(storage, batch_size: int = 1000, dry_run: bool = False) -> None:
    """
    Fetch unique company IDs from all supported tables in the BOL data API and subscribe to each
    organisation record only once.

    Args:
        storage: The storage client to use for API requests
        batch_size: The number of IDs to fetch in each request
        dry_run: If True, don't actually subscribe, just log what would be done
    """
    if not feature_active('core_mitt_id06'):
        logger.error("This script is only intended to run for BOL CORE configurations")
        sys.exit(1)

    if storage.core is None:
        logger.error("Core API client is not configured")
        sys.exit(1)

    total_stats = {
        'total_processed': 0,
        'success_count': 0,
        'error_count': 0,
        'already_subscribed_count': 0
    }

    unique_company_ids: Set[str] = set()

    # Process each table separately
    for table in SUPPORTED_TABLES:
        try:
            processed, success, errors, already_subscribed = fetch_and_process_table(
                storage, table, batch_size, dry_run, unique_company_ids
            )
            total_stats['total_processed'] += processed
            total_stats['success_count'] += success
            total_stats['error_count'] += errors
            total_stats['already_subscribed_count'] += already_subscribed
        except Exception as e:
            logger.error(f"Error processing table {table}: {str(e)}")
            logger.exception("Exception details:")

    logger.info(f"Overall summary - Total processed: {total_stats['total_processed']}")
    logger.info(
        f"Success: {total_stats['success_count']}, Errors: {total_stats['error_count']}, "
        f"Already subscribed: {total_stats['already_subscribed_count']}"
    )
    logger.info(f"Unique company ids count: {len(unique_company_ids)}")


def setup_storage_for_script(config):
    """
    Set up the storage client for the script.

    Args:
        config: The configuration to use

    Returns:
        The configured storage client
    """
    logger.info('Connecting to Qvarn...')
    qvarn = setup_qvarn_api(config, reason_getter=lambda: 'subscribing to organisation records')

    logger.info('Connecting to BDA...')
    bda = setup_bda_client(config)

    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(config)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    storage = setup_storage(qvarn=qvarn, bda=bda, core=core)
    return storage


def main():
    global SUPPORTED_TABLES

    parser = argparse.ArgumentParser(
        description='Subscribe to all organisation records from the BOL Data API'
    )
    parser.add_argument('-c', '--config', required=True, help='Path to the configuration file')
    parser.add_argument('-b', '--batch-size', type=int, default=1000,
                        help='Number of IDs to fetch in each request from BOL Data API')
    parser.add_argument('--dry-run', action='store_true',
                        help='Do not actually subscribe, just log what would be done')
    # Note: let's add 'all' option to allow the script to fetch ids from all tables
    # supported in BDA. Might be usefull if somebody needs to fetch and subscribe to company ids
    # from all supported tables in BDA
    parser.add_argument('--tables', nargs='+', choices=SUPPORTED_TABLES + ['all'],
                        help='Specific tables to process'
                             '(default: all supported tables in the script '
                             'as listed in SUPPORTED_TABLES).'
                             '`all` - specifies that all tables supported in BDA will be processed')

    args = parser.parse_args()

    # Set up configuration
    config = set_config(args.config)

    # Set up Azure AI and Sentry
    setup_azure_ai(config, 'bol-subscribe-all-organisation-records-fixup')
    setup_sentry(config, tags={'service': 'bol-subscribe-all-organisation-records-fixup'})

    # Set up storage
    storage = setup_storage_for_script(config)

    # Override SUPPORTED_TABLES if specific tables are provided
    if args.tables:
        SUPPORTED_TABLES = args.tables
        logger.info(f"Processing only these tables: {', '.join(SUPPORTED_TABLES)}")

    # Fetch company IDs and subscribe to organisation records
    fetch_and_subscribe_company_ids(storage, args.batch_size, args.dry_run)


if __name__ == '__main__':
    main()
