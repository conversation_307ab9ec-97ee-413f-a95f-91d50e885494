*** Settings ***
Library         DateTime
Library         SeleniumLibrary

*** Keywords ***
Verify Project Detail Status
    [Arguments]  ${status}
    Verify Element Text Contains    //*[@id='project-status-chart-legend']  ${status}

Close Project Detail View
    Click Element When Visible     //*[@class='modal-header']//*[@id='close_view_button_title']

Open Project Tree From Diagram Status
    [Arguments]  ${status}
    ${status}=  Convert To Lowercase    ${status}
    Run Keyword If    '${status}' == 'warning!'
    ...  Click Element When Visible    //*[@id='project-status-chart']//*[contains(concat(' ', @class, ' '),' c3-arcs-stop ')]
    ...  ELSE
    ...  Click Element When Visible    //*[@id='project-status-chart']//*[contains(concat(' ', @class, ' '),' c3-arcs-${status} ')]

Open Full Project Tree
    Click Element When Visible    project-tree-popup-source

Suppliers With Status
    [Arguments]  ${status}
    ${run_result}  ${value} =  Run Keyword And Ignore Error    GetElementText    //*[@id='project-status-chart-legend']//*[contains(text(),'${status}')]/preceding-sibling::td[1]
    ${number_suppliers} =  Set Variable If   '${run_result}' == 'FAIL'  0  ${value}
    RETURN    ${number_suppliers}

Mouse Over Diagram Status
    [Arguments]  ${status}
    ${status}=                  Convert To Lowercase    ${status}
    MouseOverElement            //*[@id='project-status-chart']//*[contains(concat(' ', @class, ' '),' c3-arcs-${status} ')]

Verify Diagram Tooltip Contains
    [Arguments]  ${status}  ${text}
    ${status}=  Convert To Lowercase    ${status}
    Verify Element Text Contains   //*[@id='project-status-chart']//*[@class='c3-tooltip-container']//*[@class='c3-tooltip-name--${status}']  ${text}

Verify Status Chart Legend Contains
    [Arguments]  ${text}
    Verify Element Text Contains    project-status-chart-legend    ${text}

Verify Data updated
   [Arguments]   ${number1}  ${number2}
   ${number1} =  Convert To Integer  ${number1}
   ${number2} =  Convert To Integer  ${number2}
   ${delta_num} =  Evaluate  ${number1} - ${number2}
   Should Be Equal As Integers  ${delta_num}  1

Add First Supplier
    [Arguments]  ${company id}  ${company}
    Verify Element Text Contains    //*[@id='project_details_View']   Add the first supplier
    Click Element When Visible      //*[@id='project_details_View']//*[@class='action']/span
    Type Text                       //input[@id='search_id']          ${company id}
    Click Element When Visible      company_find_button

Click On Add Supplier
    Click Element When Visible      //*[@id='project_details_View']//*[@class='action']/span

Open Project Report
    [Arguments]     ${project_name}
    ${project_report_selector}              Set Variable     xpath=//*[@id="projectDetails.projectReportIcon"]/..
    ${url}=     Get Element Attribute       ${project_report_selector}       href
    Click Element When Visible              projectDetails.projectReportIcon
    Wait Until Succeeds                     Switch Window    url=${url}
    Wait Until Page Contains                ${project_name}

Click Project Members Tab
    Click Element When Visible   css=.project-users-tab.nav-link

Wait Until Members Table Contains
    [Arguments]    ${value}
    Wait Until Keyword Succeeds    3x    5s    Table Should Contain    css=.project-users>table>tbody    ${value}

Check Project Members Labels
    Verify Project Details Header Contains  Role
    Verify Project Details Header Contains  User information
    Verify Project Details Header Contains  Company
    Verify Project Details Header Contains  Notify
    Verify Project Details Header Contains  Actions

Verify Project Details Header Contains
    [Arguments]  ${text}
    Verify Element Text Contains   //*[@class='tab-pane active']//table  ${text}

Verify Project Details Table Contains
    [Arguments]  ${text}
    Verify Element Text Contains   //*[@class='tab-pane active']//*/table  ${text}

Verify Project Member Role
    [Arguments]  ${member}  ${role}
    Verify Element Text Contains   //*[@class='tab-pane active']//*[contains(text(),'${member}')]/../../../preceding-sibling::td[1]   ${role}

Click Add Project Member Button
    Click Element When Visible  //*[@class='tab-pane active']//*[@class='text-right']//span

Select Member
    [Arguments]  ${member}
    Type Text                   //*[@id='add_user_select_user']//input    ${member}
    Click Element When Visible  //*[@id='typeahead-add_user_select_user']//mark[contains(text(),'${member}')]

Select Role
    [Arguments]  ${role}
    ${role_lower} =	    Replace String	        ${role.lower()}     ${SPACE}    _
    ${role_upper} =     Replace String	        ${role.upper()}     ${SPACE}    _
    Click Visible Element       //*[@id='add_user_role']
    Click Visible Element       //button[contains(concat(' ', @class, ' '), ' dropdown-role-${role_lower} ') or contains(concat(' ', @class, ' '), ' dropdown-role-${role_upper} ')]

Save Project Member
    Click Element When Visible  //*[@id='add_user_save_button']

Add Project New Member
    [Arguments]  ${member}  ${role}
    Click Add Project Member Button
    Select Member  ${member}
    Select Role    ${role}
    Save Project Member

Try To Add Nonexistent Project Member
    [Arguments]     ${member}
    Click Add Project Member Button
    Type Text                       //*[@id='add_user_select_user']//input    ${member}
    Wait until page contains        List is empty
    Element should not be visible   //*[@id='typeahead-add_user_select_user']//mark[contains(text(),'${member}')]

Delete Project Member
    [Arguments]  ${member}
    Click Element When Visible     //*[@class='tab-pane active']//*[contains(text(),'${member}')]/../../..//a//span[text()='Delete']
    Click Element When Visible     //*[@id='confirm_remove']

Verify Project Details Table Not Contains
    [Arguments]  ${text}
    Verify Element Does Not Contain Text  //*[@class='tab-pane active']//*/table  ${text}

Verify Project Member Information
    [Arguments]  ${text}
    Verify Element Text Contains   //*[@class='tabs-view']    ${text}

Get Count Of Project Members
    ${count_members} =  Get Element Count   //*[@class='tab-pane active']//tbody/tr
    RETURN  ${count_members}

Click Project Suppliers Tab
    Scroll Page To Top
    Click Element When Visible   css=.related-suppliers-tab.nav-link

Check Project Suppliers Labels
    Verify Project Details Header Contains  Status
    Verify Project Details Header Contains  Supplier
    Verify Project Details Header Contains  Buyer
    Verify Project Details Header Contains  Contact Person
    Verify Project Details Header Contains  Report

Click Edit Project Button
    Click Element When Visible    //*[@class='modal-header']//*[@id='edit_project_button_title']

Edit Project State
    [Arguments]      ${state}
    SelectTextFromDropDown      state   ${state}

Edit Project Start Date
    [Arguments]      ${date}
    Click Element When Visible        //*[@id='start_date']
    Type Text      //*[@id='start_date']   ${date}
    Click Element When Visible        //*[@id='form_content']//span[text()='State']

Edit Project End Date
    [Arguments]      ${date}
    Click Element When Visible        //*[@id='end_date']
    Type Text      //*[@id='end_date']   ${date}
    Click Element When Visible        //*[@id='form_content']//span[text()='State']

Edit Project Name
    [Arguments]     ${name}
    Type Text    //*[@id='project_name']     ${name}

Edit Project ID
    [Arguments]     ${project_id}
    Type Text    //*[@id='internal_project_id']     ${project_id}

Save Project
    Click Element When Visible    //*[@id='project_form_save_button']

Edit Internal Project ID Inline
    Click Element When Visible    //*[@id='edit_internal_id_inline']

Clear Internal Project ID Inline
    Clear Element Text    //*[@id='internal_id']

Save Internal Project ID Inline
    Click Element When Visible    //*[@id='internal_id_save']

Input Internal Project ID Inline
    [Arguments]     ${project_id}
    Type Text        //*[@id='internal_id']     ${project_id}

Verify Project In Active State
    VerifyElementText   //*[@class='state']     Active

Verify No Construction site ID
    VerifyElementIsNotVisible                  //*[@class='tax-id']

Verify Contract Type Option
    [Arguments]     ${selector}         ${option_name}          ${description}
    ${selector}=    Replace String      ${selector.lower()}     ${SPACE}    _
    Click Element When Visible          contract_types_dropdown
    Element Should Be Visible           css:.dropdown-contract-type-${selector}
    Element Text Should Be              css:.dropdown-contract-type-${selector}     ${option_name}\n${description}
    Click Visible Element               css:.dropdown-contract-type-${selector}
    Element Text Should Be              add_contract_type       ${option_name}

Select Contract Type Option
    [Arguments]     ${selector}         ${option_name}
    ${selector}=    Replace String      ${selector.lower()}     ${SPACE}    _
    Click Element When Visible          contract_types_dropdown
    Click Visible Element               css:.dropdown-contract-type-${selector}
    Element Text Should Be              add_contract_type       ${option_name}

Select 1 week timeframe
    ${start_date}=	Get Current Date	    result_format=%Y-%m-%d
    ${end_date}=    Add Time To Date	    ${start_date}	    7 days      result_format=%Y-%m-%d
    Element Should Be Visible               contract_start_date
    Element Should Be Visible               contract_end_date
    Input Date      contract_start_date     ${start_date}
    Input Date      contract_end_date       ${end_date}
    RETURN        ${start_date}           ${end_date}

Verify Contract Type List Length
    [Arguments]     ${selector_id}      ${expected_length}
    Click Element When Visible          ${selector_id}
    Element Should Be Visible           //*[@id="${selector_id}"]/div[2]
    ${count}=	                        Get Element Count	    xpath://*[@id="${selector_id}"]/div[2]/button
    Should Be True	                    ${count} == ${expected_length}
    Click Element When Visible          ${selector_id}
