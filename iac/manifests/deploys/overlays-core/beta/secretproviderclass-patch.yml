apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: bol-core-spc
  labels:
    org: ID06
    vertical: BOL
spec:
  provider: azure
  secretObjects:
  - secretName: bol-core-creds
    data:
    - key: creditsafe_username
      objectName: creditsafe-username
    - key: creditsafe_password
      objectName: creditsafe-password
    - key: creditsafe_symmetric_secret_key
      objectName: creditsafe-symmetric-secret-key
    - key: rabbitmq-user
      objectName: rabbitmq-user
    - key: rabbitmq-password
      objectName: rabbitmq-password
    - key: RABBITMQ_ERLANG_COOKIE
      objectName: rabbitmq-erlang-cookie
    - key: rabbitmq-definitions
      objectName: rabbitmq-definitions
    - key: APPLICATIONINSIGHTS_CONNECTION_STRING
      objectName: application-insight-beta-connection-string
    - key: CELERY_BROKER_URL
      objectName: celery-broker-url
    - key: client_id
      objectName: client-id-core
    - key: client_secret
      objectName: client-secret-core
    - key: creditsafe_ggs_wsdl_password
      objectName: creditsafe-ggs-wsdl-password
    - key: creditsafe_ggs_wsdl_username
      objectName: creditsafe-ggs-wsdl-username
    - key: autoaccount_username
      objectName: autoaccount-username
    - key: autoaccount_password
      objectName: autoaccount-password
    - key: redis_dsn
      objectName: redis-dsn
    - key: session_encrypt_key
      objectName: session-encrypt-key
    - key: session_validate_key
      objectName: session-validate-key
    - key: sendgrid_api_key
      objectName: sendgrid-api-key
    type: Opaque
  parameters:
    clientID: 7a68386e-a778-434e-bbeb-90dd8183c290
    keyvaultName: kv-bol-beta-we-01
    objects: |
      array:
        - |
          objectName: creditsafe-username
          objectType: secret
        - |
          objectName: creditsafe-password
          objectType: secret
        - |
          objectName: creditsafe-symmetric-secret-key
          objectType: secret
        - |
          objectName: rabbitmq-user
          objectType: secret
        - |
          objectName: rabbitmq-password
          objectType: secret
        - |
          objectName: rabbitmq-erlang-cookie
          objectType: secret
        - |
          objectName: rabbitmq-definitions
          objectType: secret
        - |
          objectName: application-insight-beta-connection-string
          objectType: secret
        - |
          objectName: celery-broker-url
          objectType: secret
        - |
          objectName: client-id-core
          objectType: secret
        - |
          objectName: client-secret-core
          objectType: secret
        - |
          objectName: creditsafe-ggs-wsdl-password
          objectType: secret
        - |
          objectName: creditsafe-ggs-wsdl-username
          objectType: secret
        - |
          objectName: autoaccount-username
          objectType: secret
        - |
          objectName: autoaccount-password
          objectType: secret
        - |
          objectName: redis-dsn
          objectType: secret
        - |
          objectName: session-encrypt-key
          objectType: secret
        - |
          objectName: session-validate-key
          objectType: secret
        - |
          objectName: sendgrid-api-key
          objectType: secret
