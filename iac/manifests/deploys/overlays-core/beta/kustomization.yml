resources:
  - ../../base-core
  - ./cronjobs/boladdtomonitoring.yml
  - ./cronjobs/boladdvisitors-1-day.yml
  - ./cronjobs/boladdvisitors-2-days.yml
  - ./cronjobs/bolremovefrommonitoring.yml
  - ./cronjobs/bolsendnotifications-daily.yml
  - ./cronjobs/bolsendnotifications-weekly.yml
  - ./cronjobs/bolstatusreports-swedish-fixup.yml
  - ./cronjobs/bolstatusreports-foreign-outdated-1.yml
  - ./cronjobs/bolstatusreports-foreign-outdated-2.yml
  - ./cronjobs/bolstatusreports-swedish-update.yml
  - ./cronjobs/move-old-reports-to-history.yml
  - ./cronjobs/move-reports-to-history.yml
  - ./cronjobs/boladdprojectusers.yml
  - ./cronjobs/bolstatusreports-update-ab-emptax.yml
  - ./cronjobs/bolstatusreports-update-ab-emptax-fixup.yml
  - ./cronjobs/bolsupcontactprojuserfixup.yml
  - ./cronjobs/bol-subscribe-all-organisation-records-fixup.yml

patches:
  - target:
      kind: ConfigMap
      name: bol-core-configmap
    path: configMap-patch.yml
  - target:
      kind: Deployment
      name: bol-core-deployment
    path: bol-deployment-patch.yml
  - target:
      kind: Deployment
      name: bol-core-celery-deployment
    path: bol-celery-deployment-patch.yml
  - target:
      kind: Ingress
      name: bol-core-ingress-googledns
    path: ingress-googledns-patch.yml
  - target:
      kind: SecretProviderClass
      name: bol-core-spc
    path: secretproviderclass-patch.yml
