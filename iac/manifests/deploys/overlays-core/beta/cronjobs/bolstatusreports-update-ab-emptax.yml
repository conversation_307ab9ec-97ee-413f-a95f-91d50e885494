# Update SWE reports every month
# for all AB and companies registered for employer tax.
apiVersion: batch/v1
kind: CronJob
metadata:
  name: bol-core-statusreports-update-ab-emptax
  labels:
    cost-center: xxxx
    env: beta
    org: ID06
    vertical: BOL
spec:
  schedule: "0 20 * * 6"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          nodeSelector:
            agentpool: npuser01
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          containers:
          - name: bol-core-statusreports-update-ab-emptax
            image: crvaultitprod.azurecr.io/bol/bol
            imagePullPolicy: Always
            command:
              - /bin/sh
              - -c
              - if [ $(date +\%d) -le 07 ]; then ./run.sh bolstatusreports -c /etc/bolfak/statusreports.cfg --update-cs-swe-batched-emptax-orgs 1; fi
            resources:
              requests:
                memory: 256Mi
                cpu: 2m
              limits:
                memory: 1Gi
                cpu: 1000m
            env:
              - name: app
                value: bolagsdeklaration
            envFrom:
              - secretRef:
                  name: bol-core-creds
              - configMapRef:
                  name: bol-core-configmap
            securityContext:
              allowPrivilegeEscalation: false
