# Script to fixup Creditsafe SWE missing reports.
# Adjust value for --changed after first run
apiVersion: batch/v1
kind: CronJob
metadata:
  name: bol-core-statusreports-swedish-fixup-cronjob
  labels:
    cost-center: xxxx
    env: beta
    org: ID06
    vertical: BOL
spec:
  schedule: "30 00 * * Tue-Sat"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      activeDeadlineSeconds: 3600  # 1 hour
      template:
        spec:
          restartPolicy: OnFailure
          nodeSelector:
            agentpool: npuser01
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          containers:
          - name: bol-core-statusreports-swedish-fixup-cronjob
            image: crvaultitprod.azurecr.io/bol/bol
            imagePullPolicy: Always
            command:
              - /bin/sh
              - -c
              - ./run.sh; bolstatusreports -c /etc/bolfak/statusreports.cfg --changed 200
            resources:
              requests:
                memory: 256Mi
                cpu: 2m
              limits:
                memory: 1Gi
                cpu: 1000m
            env:
              - name: app
                value: bolagsdeklaration
            envFrom:
              - secretRef:
                  name: bol-core-creds
              - configMapRef:
                  name: bol-core-configmap
            securityContext:
              allowPrivilegeEscalation: false
