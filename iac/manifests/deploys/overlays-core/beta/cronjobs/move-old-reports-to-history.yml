# Move old status reports to history.
apiVersion: batch/v1
kind: CronJob
metadata:
  name: bol-core-move-old-reports-to-history-cronjob
  labels:
    cost-center: xxxx
    env: beta
    org: ID06
    vertical: BOL
spec:
  schedule: "00 6 * * Mon-Fri"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      activeDeadlineSeconds: 3600  # 1 hour
      template:
        spec:
          restartPolicy: OnFailure
          nodeSelector:
            agentpool: npuser01
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          containers:
          - name: bol-core-move-old-reports-to-history-cronjob
            image: crvaultitprod.azurecr.io/bol/bol
            imagePullPolicy: Always
            command:
              - /bin/sh
              - -c
              - ./run.sh; bol_move_old_status_reports_to_history --days-ago 3650 -c /etc/bolfak/bolfak.cfg
            resources:
              requests:
                memory: 256Mi
                cpu: 2m
              limits:
                memory: 1Gi
                cpu: 1000m
            env:
              - name: app
                value: bolagsdeklaration
            envFrom:
              - secretRef:
                  name: bol-core-creds
              - configMapRef:
                  name: bol-core-configmap
            securityContext:
              allowPrivilegeEscalation: false
