# Retrieve BOL-STAMP visitors from STAMP
apiVersion: batch/v1
kind: CronJob
metadata:
  name: bol-core-addvisitors-2-days-cronjob
  labels:
    cost-center: xxxx
    env: beta
    org: ID06
    vertical: BOL
spec:
  schedule: "0 8 * * Mon-Fri"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      activeDeadlineSeconds: 3600  # 1 hour
      template:
        spec:
          restartPolicy: OnFailure
          nodeSelector:
            agentpool: npuser01
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          containers:
          - name: bol-core-addvisitors-cronjob
            image: crvaultitprod.azurecr.io/bol/bol
            imagePullPolicy: Always
            command:
              - /bin/sh
              - -c
              - ./run.sh; boladdvisitors -c /etc/bolfak/bolfak.cfg --days-ago 1,2
            resources:
              requests:
                memory: 256Mi
                cpu: 2m
              limits:
                memory: 1Gi
                cpu: 1000m
            env:
              - name: app
                value: bolagsdeklaration
            envFrom:
              - secretRef:
                  name: bol-core-creds
              - configMapRef:
                  name: bol-core-configmap
            securityContext:
              allowPrivilegeEscalation: false
