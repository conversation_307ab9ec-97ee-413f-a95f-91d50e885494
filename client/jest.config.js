module.exports = {
  testEnvironment: 'jsdom',

  transform: {
    '^.+\\.[tj]sx?$': 'babel-jest',
    '^.+\\.mjs$': 'babel-jest',
    '^.+\\.(jpg|jpeg|png|gif|webp|svg)$': './fileTransformer.js',
  },

  // Automatically clear mock calls and instances between every test
  clearMocks: true,

  setupFiles: ['./jest-env.js'],
  setupFilesAfterEnv: ['@testing-library/jest-dom'],

  moduleNameMapping: {
    '^host/axiosInstance$': '<rootDir>/__mocks__/host.js',
  },

  testMatch: [
    '**/__tests__/**/*.[j]s?(x)',
  ]
}
