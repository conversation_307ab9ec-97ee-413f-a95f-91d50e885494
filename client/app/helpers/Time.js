import { defineMessages } from 'react-intl';
import moment from 'moment';

const messages = defineMessages({
  now: {
    id: 'time.now',
    description: 'Label for something that happened recently',
    defaultMessage: 'just now',
  },
});

export function formatTime (timestamp, intl, relative=true) {
  if (!timestamp)
    return '';
  const timestampTime = new Date(timestamp);
  if (!relative) {
    return moment(timestampTime).format('YYYY-MM-DD HH:mm');
  }
  const rtf = new Intl.RelativeTimeFormat(intl.locale, { numeric: 'auto' });
  const now = new Date();
  let secondsDifference = (now - timestampTime) / 1000;
  const nowDate = new Date()
  nowDate.setFullYear(now.getFullYear(), now.getMonth(), now.getDate());
  const timestampDate = new Date()
  timestampDate.setFullYear(
    timestampTime.getFullYear(),
    timestampTime.getMonth(),
    timestampTime.getDate()
  );
  const dayDifference = (nowDate - timestampDate) / (24 * 60 * 60 * 1000);
  if (dayDifference >= 2){
    return moment(timestampTime).format('YYYY-MM-DD HH:mm');
  }
  if (dayDifference >= 1) {
    return `${rtf.format(-1, 'day')} ${moment(timestampTime).format('HH:mm')}`
  }
  if (secondsDifference <= 15) {
    return intl.formatMessage(messages.now)
  }
  if (secondsDifference <= 60) {
    // Exception: if 15-60s old, treat as one minute old
    // https://id06.atlassian.net/wiki/spaces/VC/pages/1291452422/BOL+FM9+Project+comment+list
    secondsDifference = 60;
  }
  const units = [
    ['hour', 60 * 60],
    ['minute', 60],
  ];
  for (const [unit, seconds] of units) {
    const delta = Math.round(secondsDifference / seconds);
    if (delta >= 1) {
      return rtf.format(-delta, unit);
    }
  }
}
