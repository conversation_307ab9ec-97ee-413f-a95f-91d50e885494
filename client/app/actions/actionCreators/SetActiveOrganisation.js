import dispatcher from '../../Dispatcher';
import ActionTypes from '../ActionTypes';
import { ACTIVE_ORG_ID_STORAGE_KEY, SELECTED_MITTID06_ORG_ID_STORAGE_KEY } from '../../Constants';

import reloadApplication from './ReloadApplication';
import { featureActive } from '../../helpers/FeatureFlags';

export const setActiveOrganisation = (organisationId, reload = true) => {
  if (featureActive('core_mitt_id06')) {
    window.localStorage.setItem(SELECTED_MITTID06_ORG_ID_STORAGE_KEY, organisationId);
  } else {
    window.localStorage.setItem(ACTIVE_ORG_ID_STORAGE_KEY, organisationId);
  }

  dispatcher.dispatch({
    type: ActionTypes.SET_ACTIVE_ORGANISATION,
    organisationId,
  });

  if (reload) {
    reloadApplication('/');
  }
};

export default setActiveOrganisation;
