import projectUsersChange from './ProjectUsersChange';
import { featureActive } from '../../helpers/FeatureFlags';

const projectUsersAdd = (projectId, userContractId, orgId, role, personId) => {
  if (featureActive('person_id_for_project_users')) {
    projectUsersChange(
      projectId,
      {
        person_id: personId,
        org_id: orgId,
        role,
      },
      '/create-user'
    );
  } else {
    projectUsersChange(
      projectId,
      {
        user_contract_id: userContractId,
        org_id: orgId,
        role,
      },
      '/create-user'
    );
  }
};

export default projectUsersAdd;
