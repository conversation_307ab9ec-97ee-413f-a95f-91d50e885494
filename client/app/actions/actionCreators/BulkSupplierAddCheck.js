import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import pollStatus from './BulkSupplierAddPoll';

function handleCheckErrors(error, result) {
  let errors = [error];
  if (!error) {
    ({ errors } = result.data);
  }
  if (result.data && result.data.error) {
    errors = [result.data.error];
  }

  dispatcher.dispatch({
    type: ActionTypes.BULK_SUPPLIER_ADD_CHECK_FAILED,
    checkErrors: errors,
  });
}

async function bulkSupplierAddCheck(projectId, govOrgIds) {
  dispatcher.dispatch({
    type: ActionTypes.BULK_SUPPLIER_ADD_CHECK,
    supplierGovOrgIdsToCheck: govOrgIds,
  });
  const checkUrl = `/api/project/${projectId}/bulk-import/check`;
  const payload = {
    companies: govOrgIds,
  };

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(checkUrl, true, payload);
    if (result.data.ok === false) {
      handleCheckErrors(null, result);
      return;
    }

    const jobId = result.data.job_id;
    dispatcher.dispatch({
      type: ActionTypes.BULK_SUPPLIER_ADD_CHECK_IN_PROGRESS,
      jobId,
      companies: result.data.companies,
    });
    pollStatus(projectId, jobId);
  } catch (error) {
    handleCheckErrors(error, {});
  }
}

export default bulkSupplierAddCheck;
