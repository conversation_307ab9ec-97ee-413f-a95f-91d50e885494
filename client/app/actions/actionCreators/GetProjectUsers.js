import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';

const getProjectUsers = async projectId => {
  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(`/api/project/${projectId}/users`, true);
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_USERS_LOADED,
      users: res.data.users,
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_USERS_FAILED_TO_LOAD,
    });
  }
};

export default getProjectUsers;
