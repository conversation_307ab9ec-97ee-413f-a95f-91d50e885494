import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';

const companySearch = async filter => {
  if (!filter.freeText) {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_SEARCH_CLEAR,
    });
    return;
  }

  dispatcher.dispatch({
    type: ActionTypes.COMPANY_SEARCH,
    filter,
  });

  const url = '/api/company-search';

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(url, true, { country: filter.country, free_text: filter.freeText });
    const data = res.data;

    dispatcher.dispatch({
      type: ActionTypes.COMPANY_SEARCH_LOADED,
      companies: data.companies,
      tooManyResults: data.too_many_results,
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_SEARCH_FAILED_TO_LOAD,
      error: error.toString(),
    });
  }
};

export default companySearch;
