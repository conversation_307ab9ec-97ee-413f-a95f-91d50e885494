import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getProjectUsers from './GetProjectUsers';

import getAPIClient from '../../helpers/ApiClient';

const subcontractorEditSave = async (
  projectId,
  supplierData,
  shouldCloseModalContainerOnSaveSuccess = false
) => {
  const endPoint = `/api/project/${projectId}/update-supplier`;
  dispatcher.dispatch({
    type: ActionTypes.SUBCONTRACTOR_EDIT_SAVE_STARTED,
  });

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(endPoint, true, supplierData);
    const data = result.data;

    if (!data.ok) {
      const errors = data.errors || { general: 'Unknown error' };
      dispatcher.dispatch({
        type: ActionTypes.SUBCONTRACTOR_EDIT_SAVE_FAILED,
        errors,
      });
    } else {
      dispatcher.dispatch({
        type: ActionTypes.SUBCONTRACTOR_EDIT_SAVE_SUCCESS,
        projectId: data.id,
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_TREE_RELOAD,
      });
      dispatcher.dispatch({
        type: ActionTypes.SUBCONTRACTOR_FORM_CLOSE_CLICKED,
      });
      if (shouldCloseModalContainerOnSaveSuccess) {
        dispatcher.dispatch({
          type: ActionTypes.CONTEXT_MODAL_CONTAINER_CLOSE,
        });
      }
      getProjectUsers(projectId);
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.SUBCONTRACTOR_EDIT_SAVE_FAILED,
      errors: { general: error.toString() },
    });
  }
};

export default subcontractorEditSave;
