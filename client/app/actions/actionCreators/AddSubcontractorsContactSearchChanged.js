import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const addSubcontractorsContactSearchChange = event => {
  const contactPerson = {};
  if (event.length > 0) {
    Object.assign(contact<PERSON>erson, {
      contract_id: event[0].contract_resource_id,
      org_id: event[0].org_id,
      personal_id: event[0].personal_id,
    });
  }

  dispatcher.dispatch({
    type: ActionTypes.ADD_SUBCONTRACTOR_CONTACT_OPTION_SELECTED,
    contact_person: contact<PERSON>erson,
  });
};

export default addSubcontractorsContactSearchChange;
