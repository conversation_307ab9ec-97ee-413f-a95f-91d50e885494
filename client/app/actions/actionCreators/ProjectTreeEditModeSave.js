import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import { projectTreeEditStore } from '../../stores/Stores';
import getAPIClient from '../../helpers/ApiClient';
import getProjectUsers from './GetProjectUsers';

const serializeMoveActions = moveActions => Object.keys(moveActions).map(key => moveActions[key]);

const projectTreeEditModeSave = async (continueEditing = false) => {
  const editState = projectTreeEditStore.getState();
  const { projectId, moveActions } = editState;
  const actions = serializeMoveActions(moveActions);

  if (actions.length) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_TREE_SAVE_STARTED,
    });

    const payload = actions.map(action => ({
      supplier_id: action.supplier_id,
      new_parent_id: action.new_parent_id,
      new_supplier_type: action.new_supplier_type,
      supplier_revision: action.supplier_revision,
    }));
    const url = `/api/project/${projectId}/move-suppliers`;

    try {
      const apiClient = getAPIClient();
      const result = await apiClient.post(url, true, { moves: payload });
      const data = result.data;

      if (!data.ok) {
        const errors = data.errors.moves ? data.errors.moves : data.errors;
        dispatcher.dispatch({
          type: ActionTypes.PROJECT_TREE_SAVE_FAILED,
          errors,
        });
      } else {
        dispatcher.dispatch({
          type: ActionTypes.PROJECT_TREE_SAVE_SUCCESS,
          continueEditing,
        });
        dispatcher.dispatch({
          type: ActionTypes.PROJECT_TREE_RELOAD,
        });

        getProjectUsers(projectId);
      }
    } catch (error) {
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_TREE_SAVE_FAILED,
        errors: [error.toString()],
      });
    }
  } else if (!continueEditing) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_TREE_EXIT_EDIT_MODE,
    });
  }
};

export default projectTreeEditModeSave;
