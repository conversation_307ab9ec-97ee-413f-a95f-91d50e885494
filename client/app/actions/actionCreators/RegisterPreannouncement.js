import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const registerPreannouncement = (
  preannouncementId,
  status,
  companyGovOrgId,
  companyIdType,
  companyCountry,
  companyName,
  buyerGovOrgId,
  buyerIdType,
  buyerCountry,
  buyerName,
  hasPermEstablishment,
  onemanBusiness,
  hasCollectiveAgreement,
  collectiveAgreementName,
  foremanOnSite,
  foremanName,
  foremanLastname,
  foremanPhone,
  foremanEmail,
  contractStartDate,
  contractEndDate,
  contractType,
  contractWorkAreas
) => {
  dispatcher.dispatch({
    type: ActionTypes.REGISTER_PREANNOUNCEMENT,
    preannouncement_id: preannouncementId,
    status,
    company_gov_org_id: companyGovOrgId,
    company_id_type: companyIdType,
    company_country: companyCountry,
    company_name: companyName,
    buyer_gov_org_id: buyerGovOrgId,
    buyer_id_type: buyerIdType,
    buyer_country: buyerCountry,
    buyer_name: buyerName,
    has_permanent_establishment: hasPermEstablishment,
    is_one_man_company: onemanBusiness,
    has_collective_agreement: hasCollectiveAgreement,
    collective_agreement_name: collectiveAgreementName,
    foreman_is_on_site: foremanOnSite,
    foreman_first_name: foremanName,
    foreman_last_name: foremanLastname,
    foreman_phone_number: foremanPhone,
    foreman_email: foremanEmail,
    contract_start_date: contractStartDate,
    contract_end_date: contractEndDate,
    contract_type: contractType,
    contract_work_areas: contractWorkAreas,
  });
};
export default registerPreannouncement;
