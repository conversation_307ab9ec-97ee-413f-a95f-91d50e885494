import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';

const projectUsersAvailable = async projectId => {
  const url = `/api/project/${projectId}/available-users`;

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(url, true);
    const data = res.data;

    if (data.ok === false) {
      const error = data.error || data.errors;
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_USERS_AVAILABLE_FAILED_TO_LOAD,
        error,
      });
    } else {
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_USERS_AVAILABLE_LOADED,
        users: data.users,
      });
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_USERS_AVAILABLE_FAILED_TO_LOAD,
      error: error.toString(),
    });
  }
};

export default projectUsersAvailable;
