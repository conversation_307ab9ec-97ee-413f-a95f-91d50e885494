import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import { bulkSupplierImportStore } from '../../stores/Stores';
import { SOURCE_CORE_EXTERNAL } from '../../Constants';
async function bulkSupplierAddReviewSubmit(projectId) {
  dispatcher.dispatch({
    type: ActionTypes.BULK_SUPPLIER_ADD_REVIEW_SUBMIT,
  });

  const bulkImportState = bulkSupplierImportStore.getState();
  const url = `/api/project/${projectId}/bulk-import/submit`;
  const payload = {
    job: bulkImportState.jobId,
    companies: bulkImportState.companiesToSubmit
      .filter(c => c.source !== SOURCE_CORE_EXTERNAL)
      .map(c => c.org_id),
    external_companies: bulkImportState.companiesToSubmit
      .filter(c => c.source === SOURCE_CORE_EXTERNAL)
      .map(c => c.external_id),
  };

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(url, true, payload);
    const data = result.data;

    if (data.ok === false) {
      const errors = data.error ? [data.error] : data.errors;
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_REVIEW_SUBMIT_FAILED,
        errors,
      });
    } else {
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_REVIEW_SUBMIT_SUCCESS,
        addedCompanies: data.companies.filter(c => c.action === 'add'),
        skippedCompanies: data.companies.filter(c => c.action !== 'add'),
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_TREE_RELOAD,
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_LIST_RELOAD,
      });
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.BULK_SUPPLIER_ADD_REVIEW_SUBMIT_FAILED,
      errors: [error.message],
    });
  }
}

export default bulkSupplierAddReviewSubmit;
