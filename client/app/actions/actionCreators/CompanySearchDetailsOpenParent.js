import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const companySearchDetailsOpenParent = (countryAlpha2, countryAlpha3, govOrgId, navigate) => {
  dispatcher.dispatch({
    type: ActionTypes.COMPANY_SEARCH_DETAILS_OPEN_PARENT,
    filter: { country: countryAlpha2, freeText: govOrgId },
  });

  navigate(`/search/parent/${countryAlpha3}%2F${govOrgId}`);
};

export default companySearchDetailsOpenParent;
