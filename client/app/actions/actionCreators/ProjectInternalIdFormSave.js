import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import selectProject from '../../actions/actionCreators/SelectProject';

const projectInternalIdFormSave = async (projectId, formData) => {
  dispatcher.dispatch({
    type: ActionTypes.PROJECT_INTERNAL_ID_SAVE_STARTED,
  });
  const url = `/api/project/${projectId}/update-id`;

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(url, true, formData);
    const data = result.data;

    if (!data.ok) {
      const errors = data.errors || { general: 'Unknown error' };
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_INTERNAL_ID_SAVE_FAILED,
        errors,
        errorsMetaData: formData,
      });
    } else {
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_INTERNAL_ID_SAVE_SUCCESS,
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_INTERNAL_ID_SAVE_FORM_CLOSE,
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_LIST_LOADING,
      });

      selectProject({ id: projectId });
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_INTERNAL_ID_SAVE_FAILED,
      errors: { general: error.toString() },
      errorsMetaData: formData,
    });
  }
};

export default projectInternalIdFormSave;
