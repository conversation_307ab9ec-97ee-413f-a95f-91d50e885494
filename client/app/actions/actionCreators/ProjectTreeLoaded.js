import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const projectTreeLoaded = projectDetails => {
  dispatcher.dispatch({
    type: ActionTypes.PROJECT_TREE_LOADED,
    root: projectDetails.root,
    name: projectDetails.name,
    project_id: projectDetails.project_id,
    tax_id: projectDetails.tax_id,
    state: projectDetails.state,
    start_date: projectDetails.start_date,
    end_date: projectDetails.end_date,
    permissions: projectDetails.permissions,
    pa_form_enabled: projectDetails.pa_form_enabled,
    project_creator_role: projectDetails.project_creator_role,
    added_client_name: projectDetails.added_client_name,
    added_client_company_id: projectDetails.added_client_org_id,
    added_client_gov_id: projectDetails.added_client_gov_id,
    added_client_country: projectDetails.added_client_country,
    added_client_contact_person_email: projectDetails.added_client_contact_person_email,
    added_client_contact_person_id: projectDetails.added_client_contact_person_id,
    added_client_contact_person_full_name: projectDetails.added_client_contact_person_full_name,
    added_client_blocked: projectDetails.added_client_blocked,
    created_by_org_name: projectDetails.created_by_org_name,
    added_client_can_view: projectDetails.added_client_can_view,
  });
};

export default projectTreeLoaded;
