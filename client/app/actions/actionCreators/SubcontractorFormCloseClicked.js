/* eslint-disable max-len */
import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import searchFieldClearClicked from '../../actions/actionCreators/SearchFieldClearClicked';
/* eslint-enable max-len */

const subcontractorFormCloseClicked = () => {
  searchFieldClearClicked('add_subcontractor_company_search');
  searchFieldClearClicked('contact_person_search_field');

  dispatcher.dispatch({
    type: ActionTypes.SUBCONTRACTOR_FORM_CLOSE_CLICKED,
  });
};

export default subcontractorForm<PERSON><PERSON>Clicked;
