import ActionTypes from '../ActionTypes';
import DialogTypes from '../DialogTypes';
import dispatcher from '../../Dispatcher';

/* eslint-disable max-len */
import getAPIClient from '../../helpers/ApiClient';
import companyArchivedReportDeleteSuccess from './CompanyArchivedReportDeleteSuccess';
import companyArchivedReportDeleteFailed from './CompanyArchivedReportDeleteFailed';
/* eslint-enable max-len */

const companyArchivedReportDelete = (targetElement, country, govOrgId, accessId) => {
  const onCancel = () => {
    dispatcher.dispatch({
      type: ActionTypes.CONFIRMATION_DIALOG_CANCEL,
    });
  };

  const onConfirm = async () => {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_ARCHIVED_REPORT_DELETE,
      accessId,
    });

    // eslint-disable-next-line max-len
    const url = `/api/company/${country}/${govOrgId}/archived-reports/${accessId}`;

    try {
      const apiClient = getAPIClient();
      await apiClient.delete(url, true);
      companyArchivedReportDeleteSuccess(accessId);
    } catch (error) {
      companyArchivedReportDeleteFailed(error.toString());
    }
  };

  dispatcher.dispatch({
    type: ActionTypes.CONFIRMATION_DIALOG_SHOW,
    dialogType: DialogTypes.CONFIRM_REMOVE_ARCHIVED_REPORT,
    callback: onConfirm,
    targetElement,
    dialogParams: {
      isRemoveAll: false,
      handleCancel: onCancel,
    },
  });
};

export default companyArchivedReportDelete;
