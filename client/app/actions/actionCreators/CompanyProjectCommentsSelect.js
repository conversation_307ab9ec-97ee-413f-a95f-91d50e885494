import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';

const companyProjectCommentsSelect = async (supplierId) => {

  dispatcher.dispatch({
    type: ActionTypes.COMPANY_PROJECTS_COMMENTS_SELECT,
    supplier_id: supplierId,
  });

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(`/api/supplier/${supplierId}/comments`, true);
    const data = res.data;

    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_LOADED,
      comments: data.comments,
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_FAILED_TO_LOAD,
      error,
    });
  }
};

export default companyProjectCommentsSelect;
