import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const projectFormAddClient = (
  clientName,
  clientGovId,
  clientCompanyId,
  clientCompanyExternalId,
  clientCountry,
  contactEmail,
  contactPersonId,
  contactPersonFullName
) => {
  dispatcher.dispatch({
    type: ActionTypes.SUBCONTRACTOR_FORM_ADD_PROJECT_CLIENT,
    added_client_name: clientName,
    added_client_gov_id: clientGovId,
    added_client_company_id: clientCompanyId,
    added_client_company_external_id: clientCompanyExternalId,
    added_client_country: clientCountry,
    added_client_contact_person_id: contactPersonId,
    added_client_contact_person_email: contactEmail,
    added_client_contact_person_full_name: contactPersonFullName,
  });
};

export default projectFormAddClient;
