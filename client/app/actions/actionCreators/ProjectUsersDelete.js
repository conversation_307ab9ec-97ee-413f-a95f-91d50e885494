import ActionTypes from '../ActionTypes';
import DialogTypes from '../DialogTypes';
import dispatcher from '../../Dispatcher';

import projectUsersChange from './ProjectUsersChange';

const projectUsersDelete = (
  targetElement,
  projectId,
  projectUserContractId,
  userEmail,
  userRole,
  company
) => {
  const onConfirm = () => {
    projectUsersChange(
      projectId,
      {
        project_user_contract_id: projectUserContractId,
      },
      '/delete-user'
    );
  };

  const onCancel = () => {
    dispatcher.dispatch({
      type: ActionTypes.CONFIRMATION_DIALOG_CANCEL,
    });
  };

  dispatcher.dispatch({
    type: ActionTypes.CONFIRMATION_DIALOG_SHOW,
    dialogType: DialogTypes.CONFIRM_REMOVE_PROJECT_USER,
    callback: onConfirm,
    targetElement,
    dialogParams: {
      handleCancel: onCancel,
      userEmail,
      userRole,
      company,
    },
  });
};

export default projectUsersDelete;
