import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import companyReportsSelect from './CompanyReportsSelect';
import { featureActive } from '../../helpers/FeatureFlags';

const selectCompanyDetails = async (companyId, supplierId=null) => {
  dispatcher.dispatch({
    type: ActionTypes.COMPANY_DETAILS_SELECT,
    companyId,
  });

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(`/api/company/${companyId}?supplierId=${supplierId}`, true);
    const data = res.data;

    if (featureActive('archived_reports')) {
      // load archived reports list in company details for BOLFIN
      companyReportsSelect(companyId);
    }

    dispatcher.dispatch({
      type: ActionTypes.COMPANY_DETAILS_LOADED,
      company_details: data,
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_DETAILS_FAILED_TO_LOAD,
      error,
    });
  }
};

export default selectCompanyDetails;
