import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const subcontractorEditMenuClicked = (
  targetRef,
  supplierData,
  paRestarting = false,
  clickedFromRelatedSuppliers = false
) => {
  dispatcher.dispatch({
    type: ActionTypes.SUBCONTRACTOR_EDIT_MENU_CLICKED,
    targetRef,
    supplierData,
    paRestarting,
    clickedFromRelatedSuppliers,
  });
};

export default subcontractorEditMenuClicked;
