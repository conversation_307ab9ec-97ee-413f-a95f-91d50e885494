import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const projectFormStateChanged = (state, endDate, autoEndDate) => {
  if (state === 'closed' && !endDate) {
    // Auto insert current date
    endDate = new Date().toISOString().slice(0, 10);
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_STATE_CHANGED,
      auto_end_date: endDate,
      end_date: endDate,
    });
  } else if (state !== 'closed' && autoEndDate) {
    // Clear auto-inserted end date
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_STATE_CHANGED,
      auto_end_date: null,
      end_date: null,
    });
  }
};

export default projectFormStateChanged;
