import authorizationFailed from './AuthorizationFailed';
import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';
import authSignIn from './AuthSignIn';
import authCompleteSignIn from './AuthCompleteSignIn';
import authSignInFailed from './AuthSignInFailed';
import {
  checkBolPermission,
  getOrgRepresentation,
  checkActiveBolProduct,
  checkActiveCreditsafeAccount,
  checkPendingCreditsafeAccount,
  checkCreditsafeContract,
  chooseActiveOrganisation,
} from '../../helpers/Authentication';
import { featureActive } from '../../helpers/FeatureFlags';
import Config from '../../Config';
import { ACTIVE_ORG_ID_STORAGE_KEY, SELECTED_MITTID06_ORG_ID_STORAGE_KEY } from '../../Constants';

window.retryAuthInfo = false;

const getIndexRedirectURL = (hasPendingCreditsafeAccount, hasActiveCreditsafeAccount) => {
  const createAndActivateCsAccounts = featureActive('create_and_activate_cs_accounts');
  const searchActive = featureActive('search');

  if (createAndActivateCsAccounts && hasPendingCreditsafeAccount && !hasActiveCreditsafeAccount) {
    return '/activate-cs-account';
  }

  if (createAndActivateCsAccounts && !hasActiveCreditsafeAccount) {
    return '/create-cs-account';
  }

  if (searchActive) {
    return '/search';
  }

  return '/projects';
};

export const fetchAuthState = async (organisationId) => {
  const userInfo = await fetchAuthenticationInformation(organisationId)
    .catch(() => null);

  if(!userInfo) {
    return null;
  }
  const {signedIn, selectedOrganisationId, authInfo} = userInfo;

  if(!signedIn || !selectedOrganisationId)
    return {authInfo, signedIn, profile: null, authorized: false};

  const {authInfo: {topMenuParameters, userProfile} } = userInfo;

  const { selectedRepresentationIndex: index } = topMenuParameters;
  const isAdminMode = topMenuParameters.representationsOpenedInAdminMode
    .map(r => topMenuParameters.allRepresentationCodes[r])
    .some(r => r === selectedOrganisationId);

  /* eslint-disable indent */
  const orgInfo =
    featureActive('allow_sp_admin_access') && isAdminMode
      ? {
        organizationId: selectedOrganisationId,
        organizationName: topMenuParameters.allRepresentations[index],
        globalPermissions: [],
        userRole: 'main',
      }
      : getOrgRepresentation(userProfile, selectedOrganisationId);


  return {
    authInfo,
    signedIn,
    authorized: hasNeededPermissions(userInfo.authInfo),
    profile: {
      first_name: authInfo.userProfile.firstName,
      last_name: authInfo.userProfile.lastName,
      full_name: authInfo.userProfile.fullName,
      email: authInfo.userProfile.username,
      organisation_id: orgInfo.organizationId,
      organisation_name: orgInfo.organizationName,
      global_permissions: orgInfo.globalPermissions,
      userRole: orgInfo.userRole,
    }
  }
}
const authorize = async (organisationId) => {
  const apiClient = getAPIClient();
  const response = await apiClient.post('/api/authorize', null, {
    selected_org_id: organisationId,
  });
  return response.data;
}

export const fetchAuthenticationInformation = async (organisationId) => {
  const apiClient = getAPIClient();
  let orgId = organisationId ? organisationId : 'undefined';
  if (featureActive('core_mitt_id06')) {
      orgId = window.localStorage.getItem(SELECTED_MITTID06_ORG_ID_STORAGE_KEY);
  }

  try {
    const response = await apiClient.get(`/api/${orgId}/authentication/info`);
    const {data, data: {topMenuParameters, userProfile}} = response;
    if (featureActive('core_mitt_id06')) {
      if (response.data?.userProfile === undefined) {
        try {
          const authResponse = await authorize(orgId);
          if (authResponse.selectedOrgId) {
            window.localStorage.setItem(
              ACTIVE_ORG_ID_STORAGE_KEY, authResponse.selectedOrgId
            );
          }
          return {
            signedIn: authResponse !== null,
            authInfo: authResponse,
            selectedOrganisationId: authResponse.selectedOrgId,
          };
        } catch(error) {
          console.error('Failed to authorize:', error);
          authorizationFailed(error);
          return {
            signedIn: false,
            authInfo: null,
            selectedOrganisationId: null,
          };
        }
      }
    }
    if (!userProfile) {
      return { signedIn: false, authInfo: data, selectedOrganisationId: null };
    }

    let selectedOrganisationId = topMenuParameters.selectedRepresentedOrganizationId;

    if (selectedOrganisationId) {
      return { signedIn: true, authInfo: data, selectedOrganisationId };
    }

    selectedOrganisationId = chooseActiveOrganisation(userProfile);

    if (!selectedOrganisationId) {
      return { signedIn: true, authInfo: data, selectedOrganisationId: null };
    }

    const newResponse = await apiClient.get(`/api/${selectedOrganisationId}/authentication/info`);
    return { signedIn: true, authInfo: newResponse.data, selectedOrganisationId };

  } catch (error) {
    console.error('Failed to fetch authentication information:', error);
  }
};

export const hasNeededPermissions = (authInfo) => {
  const {topMenuParameters, userProfile} = authInfo;
  if(!topMenuParameters)
    return false;

  const organisationId = topMenuParameters.selectedRepresentedOrganizationId
  const isAdminMode = topMenuParameters.representationsOpenedInAdminMode
    .map(r => topMenuParameters.allRepresentationCodes[r])
    .some(r => r === organisationId);

  if (!checkBolPermission(userProfile, organisationId)
    && (!featureActive('allow_sp_admin_access') || !isAdminMode)) {
    console.log('User does not have permission to access ID06 Bolagsdeklaration');
    return false;
  } else if (featureActive('require_creditsafe_contract') && !checkCreditsafeContract(authInfo)) {
    console.log('User does not have a Creditsafe account');
    return false;
  } else if (featureActive('core_mitt_id06') && !checkActiveBolProduct(authInfo)) {
    console.log('User does not have an active BOL product');
    return false;
  }
  return true;
}


const authSignInRedirectBack = async (redirectToGluu, route) => {
  const apiClient = getAPIClient();
  let activeOrganisationId = window.localStorage.getItem(ACTIVE_ORG_ID_STORAGE_KEY);

  // expecting activeOrganisationId to be null/undefined here from time to time
  try {
    const res = await apiClient.get(`/api/${activeOrganisationId}/authentication/info`)
    const data = res.data;
    const { topMenuParameters, userProfile } = data
    activeOrganisationId = topMenuParameters.selectedRepresentedOrganizationId
    // We expect to get active org from SP Framework topMenuParameters.
    // But when user logs into BOL directly on a clean browser session
    // he will not get active org id. In order to make BOL running correctly
    // within organization context we need to choose it and repeat auth info
    // call to receive all the new context. This is not the case when user
    // comes to BOL from SP directly.
    if (
      !window.retryAuthInfo &&
      !activeOrganisationId &&
      userProfile &&
      userProfile.representations
    ) {
      window.retryAuthInfo = true;
      activeOrganisationId = chooseActiveOrganisation(userProfile);
      console.log(
        'No active org selection. Chosen first org with Bol Permision: ',
        activeOrganisationId
      );
      // If we get some org id here, we want to restart the call
      // and get auth info within chosen org context
      if (activeOrganisationId) {
        console.log('Call again authentication/info within chosen org: ', activeOrganisationId);
        window.localStorage.setItem(ACTIVE_ORG_ID_STORAGE_KEY, activeOrganisationId);
        authSignInRedirectBack(false);
        return;
      }
      console.log('No active org with permission to access ID06 Bolagsdeklaration');
      document.location = Config.noPermissionURL;
    }
    window.retryAuthInfo = false;
    const isAdminMode = topMenuParameters.representationsOpenedInAdminMode
      .map(r => topMenuParameters.allRepresentationCodes[r])
      .some(r => r === activeOrganisationId)
    if (!userProfile) {
      console.log('Sign in failed: no user profile')
      if (redirectToGluu) {
        authSignIn(true);
      } else {
        authSignInFailed(data);
      }
    } else if (!activeOrganisationId) {
      console.log('Sign in failed: no active organization');
      authSignInFailed(data);
    } else if (!checkBolPermission(userProfile, activeOrganisationId)
      && (!featureActive('allow_sp_admin_access') || !isAdminMode)) {
      console.log('User does not have permission to access ID06 Bolagsdeklaration');
      document.location = Config.noPermissionURL;
    } else if (featureActive('require_creditsafe_contract') && !checkCreditsafeContract(data)) {
      console.log('User does not have a Creditsafe account');
      document.location = Config.noPermissionURL;
    } else {
      window.localStorage.setItem(ACTIVE_ORG_ID_STORAGE_KEY, activeOrganisationId)
      const { selectedRepresentationIndex: index } = topMenuParameters;
      /* eslint-disable indent */
      const orgInfo =
        featureActive('allow_sp_admin_access') && isAdminMode
          ? {
              organizationId: activeOrganisationId,
              organizationName: topMenuParameters.allRepresentations[index],
              globalPermissions: [],
              userRole: 'main',
            }
          : getOrgRepresentation(data.userProfile, activeOrganisationId)
      authCompleteSignIn(data, {
        first_name: data.userProfile.firstName,
        last_name: data.userProfile.lastName,
        full_name: data.userProfile.fullName,
        email: data.userProfile.username,
        organisation_id: orgInfo.organizationId,
        organisation_name: orgInfo.organizationName,
        global_permissions: orgInfo.globalPermissions,
        userRole: orgInfo.userRole,
      })
      const hasPendingCreditsafeAccount = checkPendingCreditsafeAccount(data);
      const hasActiveCreditsafeAccount = checkActiveCreditsafeAccount(data)
      if (!route || route.location.pathname === '/') {
        const indexRedirectURL = getIndexRedirectURL(
          hasPendingCreditsafeAccount,
          hasActiveCreditsafeAccount
        );
        // eslint-disable-next-line prefer-template
        document.location = '/#' + indexRedirectURL;
      }
      /* eslint-enable indent */
    }
  } catch (error) {
    console.log('get authentication/info failed:', error);
    authSignInFailed();
  }
  dispatcher.dispatch({
    type: ActionTypes.AUTH_SIGNIN_REDIRECT_BACK,
  });
};

export default authSignInRedirectBack;
export { getIndexRedirectURL };
