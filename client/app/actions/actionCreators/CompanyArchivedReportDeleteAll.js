import ActionTypes from '../ActionTypes';
import DialogTypes from '../DialogTypes';
import dispatcher from '../../Dispatcher';

/* eslint-disable max-len */
import getAPIClient from '../../helpers/ApiClient';
import companyArchivedReportDeleteAllSuccess from './CompanyArchivedReportDeleteAllSuccess';
import companyArchivedReportDeleteAllFailed from './CompanyArchivedReportDeleteAllFailed';
/* eslint-enable max-len */

const companyArchivedReportDeleteAll = (targetElement, country, govOrgId, archiveIdPrefix) => {
  const onCancel = () => {
    dispatcher.dispatch({
      type: ActionTypes.CONFIRMATION_DIALOG_CANCEL,
    });
  };

  const onConfirm = async () => {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_ARCHIVED_REPORT_DELETE_ALL,
    });

    // eslint-disable-next-line max-len
    const url = `/api/company/${country}/${govOrgId}/archived-reports`;

    try {
      const apiClient = getAPIClient();
      await apiClient.delete(url, true);
      companyArchivedReportDeleteAllSuccess(archiveIdPrefix);
    } catch (error) {
      companyArchivedReportDeleteAllFailed(error.toString());
    }
  };

  dispatcher.dispatch({
    type: ActionTypes.CONFIRMATION_DIALOG_SHOW,
    dialogType: DialogTypes.CONFIRM_REMOVE_ARCHIVED_REPORT,
    callback: onConfirm,
    targetElement,
    dialogParams: {
      isRemoveAll: true,
      handleCancel: onCancel,
    },
  });
};

export default companyArchivedReportDeleteAll;
