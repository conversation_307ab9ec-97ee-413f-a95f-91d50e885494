import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';
import getProjectUsers from './GetProjectUsers';
import projectUsersAvailable from './ProjectUsersAvailable';

const projectUsersChange = async (projectId, projectUserData, urlSuffix) => {
  /* A helper to handle different changes: create-user, update-user,
   * delete-user. */

  const url = `/api/project/${projectId}${urlSuffix}`;

  dispatcher.dispatch({
    type: ActionTypes.PROJECT_USERS_ADD_STARTED,
  });

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(url, true, projectUserData);
    const data = result.data;

    if (!data.ok) {
      const errors = data.errors || { general: 'Unknown error' };
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_USERS_ADD_FAILED,
        errors,
        errorsMetaData: projectUserData,
      });
    } else {
      const payload = {
        type: ActionTypes.PROJECT_USERS_ADD_SUCCESS,
      };
      if (data.hasOwnProperty('id')) {
        payload.projectUserId = data.id;
      }
      dispatcher.dispatch(payload);

      getProjectUsers(projectId);
      projectUsersAvailable(projectId);
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_USERS_ADD_FAILED,
      errors: { general: error.toString() },
      errorsMetaData: projectUserData,
    });
  }
};

export default projectUsersChange;
