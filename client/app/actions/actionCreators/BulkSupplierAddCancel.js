import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import { bulkSupplierImportStore } from '../../stores/Stores';

const bulkSupplierAddCancel = async projectId => {
  dispatcher.dispatch({
    type: ActionTypes.BULK_SUPPLIER_ADD_CANCEL,
  });

  const { jobId } = bulkSupplierImportStore.getState();
  const cancelUrl = `/api/project/${projectId}/bulk-import/cancel`;
  const payload = {
    job: jobId,
  };

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(cancelUrl, true, payload);
    if (result.data.ok === false) {
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_CANCEL_FAILED,
        errors: result.data.errors,
      });
    } else {
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_CANCEL_SUCCESS,
      });
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.BULK_SUPPLIER_ADD_CANCEL_FAILED,
      errors: [error],
    });
  }
};

export default bulkSupplierAddCancel;
