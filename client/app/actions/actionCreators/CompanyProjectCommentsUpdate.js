import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';
import { defineMessages } from 'react-intl';

const messages = defineMessages({
  generalAddFailed: {
    id: 'comments.saveFailed',
    description: 'Text for unspecified comment app/update failure',
    defaultMessage: 'Could not save comment',
  }
});

const companyProjectCommentsUpdate = async (supplierId, commentId, comment) => {

  dispatcher.dispatch({
    type: ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATE,
    commentId: commentId,
  });

  try {
    const client = getAPIClient();
    const result = await client.post(
      `/api/supplier/${supplierId}/comments/${commentId}/update`,
      true,
      {comment}
    );
    if (result.data.ok === true) {
      dispatcher.dispatch({
        type: ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATED,
        comment: result.data.comment,
      });
      return true;
    }
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATE_FAILED,
      errors: result.data?.errors ? Object.values(result.data?.errors).flat()
        : [messages.generalAddFailed],
      commentId: commentId,
    });
    return false;
  } catch (error) {
    // Unexpected error
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATE_FAILED,
      errors: [messages.generalAddFailed],
      commentId: commentId,
    });
    return false;
  }
};

export default companyProjectCommentsUpdate;
