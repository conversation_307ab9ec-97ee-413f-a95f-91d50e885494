import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import selectCompanyDetails from './CompanyDetailsSelect';
import companyProjectCommentsSelect
  from './CompanyProjectCommentsSelect';

const companyDetailsTreeMenuClicked = (targetRef, companyId, companyName, supplierId) => {
  dispatcher.dispatch({
    type: ActionTypes.COMPANY_DETAILS_TREE_MENU_CLICKED,
    targetRef,
    companyId,
    companyName,
  });

  selectCompanyDetails(companyId, supplierId);
  companyProjectCommentsSelect(supplierId);
};

export default companyDetailsTreeMenuClicked;
