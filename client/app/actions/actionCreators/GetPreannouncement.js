import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';

const getPreannouncement = async pid => {
  const url = `/api/preannouncement/${pid}`;

  dispatcher.dispatch({
    type: ActionTypes.PREANNOUNCEMENT_LOADING_STARTED,
  });

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(url, true);
    dispatcher.dispatch({
      type: ActionTypes.PREANNOUNCEMENT_GET_OK,
      preannouncement: res.data,
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PREANNOUNCEMENT_GET_ERROR,
    });
  }
};

export default getPreannouncement;
