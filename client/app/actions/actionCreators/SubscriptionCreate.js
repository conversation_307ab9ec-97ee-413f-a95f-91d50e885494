import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import subscriptionCreateFailed from './SubscriptionCreateFailed';
import subscriptionCreated from './SubscriptionCreated';

const subscriptionCreate = async orderReference => {
  dispatcher.dispatch({
    type: ActionTypes.SUBSCRIPTION_CREATE,
  });

  const url = '/api/subscription';

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.post(url, true, { order_reference: orderReference });
    subscriptionCreated(res.data.subscription);
  } catch (error) {
    subscriptionCreateFailed(error.toString());
  }
};

export default subscriptionCreate;
