import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import { bulkSupplierImportStore } from '../../stores/Stores';
import { STATUS_DONE, STATUS_FAILED } from '../../Constants';

async function pollStatus(projectId, jobId) {
  const statusUrl = `/api/project/${projectId}/bulk-import/status/${jobId}`;
  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(statusUrl, true);
    const data = res.data;

    if (data.status === STATUS_DONE) {
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_CHECK_SUCCESS,
        companies: data.companies,
      });
    } else if (data.status === STATUS_FAILED) {
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_CHECK_FAILED,
        jobErrors: [`JOB_ERROR: ${jobId}`],
        companies: data.companies,
      });
    } else if (bulkSupplierImportStore.getState().supplierCheckInProgress) {
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_CHECK_IN_PROGRESS,
        jobId,
        companies: data.companies,
      });
      setTimeout(() => pollStatus(projectId, jobId), 1000);
    }
  } catch (error) {
    console.error('Polling status failed:', error);
  }
}

export default pollStatus;
