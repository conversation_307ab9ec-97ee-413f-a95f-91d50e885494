import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const addSubcontractorsSave = (
  projectId,
  parentId,
  subcontractorCompanyId,
  addAnother,
  supplierType,
  supplierRole,
  contractType,
  companyContactData,
  companyContactEmail,
  contractStartDate,
  contractEndDate,
  contractWorkAreas,
  isOneManCompany = null,
  hasCollectiveAgreement = null,
  collectiveAgreementName = null,
  subcontractorCompanyExternalId = null
) => {
  dispatcher.dispatch({
    type: ActionTypes.ADD_SUBCONTRACTOR_SAVE,
    project_id: projectId,
    parent_id: parentId,
    subcontrator_company_id: subcontractorCompanyId,
    add_another: addAnother,
    supplier_type: supplierType,
    company_contact_data: companyContactData,
    company_contact_email: companyContactEmail,
    supplier_role: supplierRole,
    contract_type: contractType,
    contract_start_date: contractStartDate,
    contract_end_date: contractEndDate,
    contract_work_areas: contractWorkAreas,
    is_one_man_company: isOneManCompany,
    has_collective_agreement: hasCollectiveAgreement,
    collective_agreement_name: collectiveAgreementName,
    subcontractor_company_external_id: subcontractorCompanyExternalId,
  });
};

export default addSubcontractorsSave;
