import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';

const getProjectReportSuppliersDetails = async suppliers => {
  const idPairs = suppliers.map(supplier => ({
    supplier_id: supplier.supplier_id,
    company_id: supplier.organization.company_id
  }));

  if (idPairs.length === 0) {
    console.log('No suppliers ids provided. /api/companies-details will not be called.');
    return;
  }
  dispatcher.dispatch({
    type: ActionTypes.PROJECT_REPORT_SUPPLIERS_DETAILS_LOADING,
  });

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.post('/api/companies-details', true, { id_pairs: idPairs });
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_REPORT_SUPPLIERS_DETAILS_LOADED,
      suppliers: res.data.companies,
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_REPORT_SUPPLIERS_DETAILS_FAILED,
      error,
    });
  }
};

export default getProjectReportSuppliersDetails;
