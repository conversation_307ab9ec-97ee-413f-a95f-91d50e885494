import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import companyProjectsLoaded from './CompanyProjectsLoaded';
import companyProjectsFailedToLoad from './CompanyProjectsFailedToLoad';

const companyProjectsSelect = async companyId => {
  dispatcher.dispatch({
    type: ActionTypes.COMPANY_PROJECTS_SELECT,
    companyId,
  });

  const url = `/api/company/${companyId}/related-projects`;

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(url, true);
    const data = res.data;

    if (data.ok === false) {
      const error = data.errors;
      companyProjectsFailedToLoad(error);
    } else {
      companyProjectsLoaded(data.related_projects);
    }
  } catch (error) {
    companyProjectsFailedToLoad(error.toString());
  }
};

export default companyProjectsSelect;
