import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const updatePreannouncementFormErrors = (
  errors,
  hasPermanentEstablishment,
  hasCollectiveAgreement,
  oneManBusiness,
  foremanOnSite,
  foremanFirstName,
  foremanLastName,
  foremanEmail,
  foremanPhone,
  collectiveAgreementType
) => {
  dispatcher.dispatch({
    type: ActionTypes.UPDATE_PA_FORM_ERRORS,
    errors,
    hasPermanentEstablishment,
    hasCollectiveAgreement,
    oneManBusiness,
    foremanOnSite,
    foremanFirstName,
    foremanLastName,
    foremanEmail,
    foremanPhone,
    collectiveAgreementType,
  });
};

export default updatePreannouncementFormErrors;
