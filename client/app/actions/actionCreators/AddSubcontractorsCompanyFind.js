/* eslint-disable max-len */
import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import { getCountryCodeAlpha3 } from '../../helpers/Subsidiaries';
import {
  ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON,
  ERROR_TYPE_SUBCONTRACTOR_ADD_COMPANY_NOT_FOUND_IN_REGISTERS,
  ERROR_TYPE_SUBCONTRACTOR_ADD_HTTP_ERROR,
  ERROR_TYPE_SUBCONTRACTOR_ADD_SAME_AS_MAIN_CONTRACTOR,
} from '../../Constants';
import searchFieldNotFound from './SearchFieldNotFound';
import addSubcontractorsCompanyNotFound from './AddSubcontractorsCompanyNotFound';
/* eslint-enable max-len */

const handleError = (error, result, key, errorType) => {
  dispatcher.dispatch({
    type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS_STOP,
    key: 'add_subcontractor_company_search',
  });

  if (error) {
    dispatcher.dispatch({
      type: ActionTypes.ADD_SUBCONTRACTOR_SAVE_FAILURE,
      errors: { general: error.toString() },
    });
  } else {
    addSubcontractorsCompanyNotFound(result.data.errors, errorType);
    searchFieldNotFound(key);
  }
};

const addSubcontractorsCompanyFind = async (
  projectId,
  govOrgId,
  countryCode,
  key,
  isProjectClient = false
) => {
  dispatcher.dispatch({
    type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS,
    query: govOrgId,
    country: countryCode,
    key,
  });

  let importCompanyEndpoint = `/api/project/${projectId}/import-company`;

  if (isProjectClient) {
    importCompanyEndpoint = '/api/project/import-company';
  }

  try {
    const apiClient = getAPIClient();
    const findRes = await apiClient.get('/api/find-company', true, {
      params: {
        query: encodeURI(govOrgId),
        country: countryCode,
        is_project_client: isProjectClient,
      }
    });

    if (findRes.data.results.length === 0 || findRes.data.ok === false) {
      if (findRes.data.errors && findRes.data.errors.error) {
        handleError(null, findRes, key, ERROR_TYPE_SUBCONTRACTOR_ADD_SAME_AS_MAIN_CONTRACTOR);
      } else if (findRes.data.ok === false) {
        handleError(null, findRes, key, ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON);
      } else {
        const importRes = await apiClient.post(
          importCompanyEndpoint,
          true,
          { gov_org_id: govOrgId, country: getCountryCodeAlpha3(countryCode) }
        );
        if (importRes.data.ok === false) {
          handleError(null, importRes, key,
            ERROR_TYPE_SUBCONTRACTOR_ADD_COMPANY_NOT_FOUND_IN_REGISTERS);
        } else {
          dispatcher.dispatch({
            type: ActionTypes.SEARCH_FIELD_FOUND,
            results: [importRes.data.company],
            key,
          });
        }
      }
    } else {
      dispatcher.dispatch({
        type: ActionTypes.SEARCH_FIELD_FOUND,
        results: findRes.data.results,
        key,
      });
      if (findRes.data.results.length > 1) {
        dispatcher.dispatch({
          type: ActionTypes.ADD_SUBCONTRACTOR_COMPANY_FOUND,
        });
      }
    }
  } catch (findErr) {
    handleError(findErr, null, key, ERROR_TYPE_SUBCONTRACTOR_ADD_HTTP_ERROR);
  }
};

export default addSubcontractorsCompanyFind;
