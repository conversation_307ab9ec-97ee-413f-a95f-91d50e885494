import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import DialogTypes from '../DialogTypes';
import projectTreeCancelCurrentMove from './ProjectTreeCancelCurrentMove';
import { MAIN_CONTRACTOR, SUPERVISOR, SUPPLIER } from '../../components/projects/SupplierRoles';

const placeSupplierMenuClicked = (
  targetSupplier,
  supplierType,
  movedSupplierRole,
  dialogTargetElement
) => {
  const proceedPlaceAction = () => {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_TREE_PLACE_SUPPLIER_MENU_CLICKED,
      targetSupplier,
      supplierType,
      movedSupplierOldRole: movedSupplierRole,
      movedSupplierNewRole: SUPPLIER,
    });
  };

  const onCancel = () => {
    dispatcher.dispatch({
      type: ActionTypes.CONFIRMATION_DIALOG_CANCEL,
    });
    projectTreeCancelCurrentMove();
  };

  if (targetSupplier && [MAIN_CONTRACTOR, SUPERVISOR].includes(movedSupplierRole)) {
    dispatcher.dispatch({
      type: ActionTypes.CONFIRMATION_DIALOG_SHOW,
      dialogType: DialogTypes.CONFIRM_SUPPLIER_ROLE_DOWNGRADE,
      targetElement: dialogTargetElement,
      callback: proceedPlaceAction,
      dialogParams: {
        handleCancel: onCancel,
        movedSupplierRole,
      },
    });
  } else {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_TREE_PLACE_SUPPLIER_MENU_CLICKED,
      targetSupplier,
      supplierType,
    });
  }
};

export default placeSupplierMenuClicked;
