import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';
import statusChangeReportLoaded from './StatusChangeReportLoaded';
import statusChangeReportFailedToLoad from './StatusChangeReportFailedToLoad';

const loadStatusChangeReport = async reportId => {
  dispatcher.dispatch({
    type: ActionTypes.LOAD_STATUS_CHANGE_REPORT,
    report_id: reportId,
  });

  try {
    const apiClient = getAPIClient();
    const response = await apiClient.get(`/api/status-change-report/${reportId}`, true);
    statusChangeReportLoaded(reportId, response.data);
  } catch (error) {
    statusChangeReportFailedToLoad(error);
  }
};

export default loadStatusChangeReport;
