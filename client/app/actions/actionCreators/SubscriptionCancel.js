import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import subscriptionCancelFailed from './SubscriptionCancelFailed';
import subscriptionCancelled from './SubscriptionCancelled';

const subscriptionCancel = async () => {
  dispatcher.dispatch({
    type: ActionTypes.SUBSCRIPTION_CANCEL,
  });

  const url = '/api/subscription';

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.delete(url, true);
    subscriptionCancelled(res.data.subscription);
  } catch (error) {
    subscriptionCancelFailed(error.toString());
  }
};

export default subscriptionCancel;
