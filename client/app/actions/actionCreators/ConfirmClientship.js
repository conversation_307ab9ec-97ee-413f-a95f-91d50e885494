import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import projectPageOpen from './ProjectPageOpen';

import getAPIClient from '../../helpers/ApiClient';

const confirmClientship = async (projectId, navigate) => {
  const endPoint = `/api/project/${projectId}/confirm-client`;
  dispatcher.dispatch({
    type: ActionTypes.PROJECT_SAVE_STARTED,
  });

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(endPoint, true, {});
    const data = result.data;

    if (!data.ok) {
      const errors = data.errors || { general: 'Unknown error' };
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_SAVE_FAILED,
        errors,
      });
    } else {
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_SAVE_SUCCESS,
        projectId: data.id,
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_LIST_LOADING,
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_TREE_RELOAD,
      });

      projectPageOpen(navigate, projectId);
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PROJECT_SAVE_FAILED,
      errors: { general: error.toString() },
    });
  }
};

export default confirmClientship;
