import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';
import { bulkSupplierImportStore } from '../../stores/Stores';
import { SkipEnterStatuses } from '../../Constants';
import pollStatus from './BulkSupplierAddPoll';

const handleCheckErrors = (err, result) => {
  let errors = [err];
  if (result.data && result.data.error) {
    errors = [result.data.error];
  }

  dispatcher.dispatch({
    type: ActionTypes.BULK_SUPPLIER_ADD_CHECK_FAILED,
    checkErrors: errors,
  });
};

const openBulkSupplierImport = async projectId => {
  if (bulkSupplierImportStore.getState().supplierPreCheckInProgress) {
    return;
  }

  dispatcher.dispatch({
    type: ActionTypes.BULK_SUPPLIER_ADD_PRECHECK_IN_PROGRESS,
  });

  const checkUrl = `/api/project/${projectId}/bulk-import/status`;
  try {
    const apiClient = getAPIClient();
    const result = await apiClient.get(checkUrl, true);

    dispatcher.dispatch({
      type: ActionTypes.BULK_SUPPLIER_ADD_OPEN,
    });

    const data = result.data;
    if (!data.canceled && !data.imported && SkipEnterStatuses.includes(data.status)) {
      const jobId = data.job_id;
      dispatcher.dispatch({
        type: ActionTypes.BULK_SUPPLIER_ADD_CHECK_IN_PROGRESS,
        jobId,
        companies: data.companies,
      });
      pollStatus(projectId, jobId);
    }
  } catch (error) {
    handleCheckErrors(error);
  }
};

export default openBulkSupplierImport;
