import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';

const selectCompanySearchDetails = async (country, govOrgId) => {
  const companyId = `${country}/${govOrgId}`;

  dispatcher.dispatch({
    type: ActionTypes.COMPANY_DETAILS_SELECT,
    companyId,
  });

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(`/api/company/${companyId}`, true);
    const data = res.data;

    dispatcher.dispatch({
      type: ActionTypes.COMPANY_DETAILS_LOADED,
      company_details: data,
    });

    dispatcher.dispatch({
      type: ActionTypes.COMPANY_SEARCH_DETAILS_LOADED,
      company_details: data,
    });
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_DETAILS_FAILED_TO_LOAD,
      error,
    });
  }
};

export default selectCompanySearchDetails;
