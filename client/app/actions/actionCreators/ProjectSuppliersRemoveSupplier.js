import ActionTypes from '../ActionTypes';
import DialogTypes from '../DialogTypes';
import dispatcher from '../../Dispatcher';
import getProjectUsers from './GetProjectUsers';

import getAPIClient from '../../helpers/ApiClient';

const projectSuppliersRemoveSupplier = (
  targetElement,
  projectId,
  supplierId,
  supplierRev,
  supplierName,
  childrenCount,
  removableSuppliers
) => {
  const onCancel = () => {
    dispatcher.dispatch({
      type: ActionTypes.CONFIRMATION_DIALOG_CANCEL,
    });
  };

  const onConfirm = async () => {
    const url = `/api/project/${projectId}/remove-supplier`;
    try {
      const apiClient = getAPIClient();
      await apiClient.post(url, true, {
        supplier_id: supplierId,
        supplier_revision: supplierRev,
      });
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_TREE_RELOAD,
      });
      // Reload project list because statuses might have changed
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_LIST_RELOAD,
      });
      getProjectUsers(projectId);
    } catch (error) {
      console.error('Failed to remove supplier:', error);
    }
  };

  dispatcher.dispatch({
    type: ActionTypes.CONFIRMATION_DIALOG_SHOW,
    dialogType: DialogTypes.CONFIRM_REMOVE_FLAT_SUPPLIER,
    callback: onConfirm,
    targetElement,
    dialogParams: {
      supplierId,
      supplierName,
      childrenCount,
      removableSuppliers,
      handleCancel: onCancel,
    },
  });
};

export default projectSuppliersRemoveSupplier;
