import deepcopy from 'deepcopy';

import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import { projectViewStore } from '../../stores/Stores';

const moveSupplierMenuClicked = (supplierId, supplierRev, supplierType, supplierRole) => {
  const projectViewState = projectViewStore.getState();

  dispatcher.dispatch({
    type: ActionTypes.PROJECT_TREE_ENTER_EDIT_MODE,
    projectTree: deepcopy(projectViewState.project_tree),
    projectId: projectViewState.selected_project_id,
  });
  dispatcher.dispatch({
    type: ActionTypes.PROJECT_TREE_MOVE_SUPPLIER_MENU_CLICKED,
    supplierId,
    supplierRev,
    supplierType,
    supplierRole,
  });
};

export default moveSupplierMenuClicked;
