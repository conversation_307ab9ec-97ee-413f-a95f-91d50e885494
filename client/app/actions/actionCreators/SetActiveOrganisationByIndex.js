import { authStore } from '../../stores/Stores';

import setActiveOrganisation from './SetActiveOrganisation';

export const setActiveOrganisationByIndex = organisationIdx => {
  const authStoreState = authStore.getState();
  const organisationId =
    authStoreState.auth_info.topMenuParameters.allRepresentationCodes[organisationIdx];

  setActiveOrganisation(organisationId);
};

export default setActiveOrganisationByIndex;
