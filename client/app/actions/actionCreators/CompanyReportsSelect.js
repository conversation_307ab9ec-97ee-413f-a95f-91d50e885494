import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';
import companyReportsLoaded from './CompanyReportsLoaded';
import companyReportsFailedToLoad from './CompanyReportsFailedToLoad';

const companyReportsSelect = async companyId => {
  dispatcher.dispatch({
    type: ActionTypes.COMPANY_REPORTS_SELECT,
    companyId,
  });

  const url = `/api/company/${companyId}/archived-reports`;

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(url, true);
    const data = res.data;

    companyReportsLoaded(data);
  } catch (error) {
    companyReportsFailedToLoad(error.toString());
  }
};

export default companyReportsSelect;
