import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import selectCompanyDetails from './CompanyDetailsSelect';

const companyDetailsMenuClicked = (targetRef, companyId, companyName) => {
  dispatcher.dispatch({
    type: ActionTypes.COMPANY_DETAILS_MENU_CLICKED,
    targetRef,
    companyId,
    companyName,
  });

  selectCompanyDetails(companyId);
};

export default companyDetailsMenuClicked;
