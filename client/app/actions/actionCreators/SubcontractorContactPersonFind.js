import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';

const subcontractorContactPersonFind = async (email, orgId, key) => {
  if (!orgId) {
    dispatcher.dispatch({
      type: ActionTypes.SEARCH_FIELD_NOT_FOUND,
      key,
    });
    return;
  }


  dispatcher.dispatch({
    type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS,
    query: email,
    key,
  });

  const payload = { email };
  const endPoint = `/api/company/${orgId}/check-user`;

  try {
    const apiClient = getAPIClient();
    const result = await apiClient.post(endPoint, true, payload);
    const data = result.data;

    if (!data.ok) {
      const errors = data.errors || { general: 'Unknown error' };
      dispatcher.dispatch({
        type: ActionTypes.SEARCH_FIELD_FAILED,
        errors,
        key,
      });
    } else if (data.user_info) {
      dispatcher.dispatch({
        type: ActionTypes.SEARCH_FIELD_FOUND,
        results: [data.user_info],
        key,
      });
    } else {
      dispatcher.dispatch({
        type: ActionTypes.SEARCH_FIELD_NOT_FOUND,
        key,
      });
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.SEARCH_FIELD_FAILED,
      errors: { general: error.toString() },
      key,
    });
  }
};

export default subcontractorContactPersonFind;
