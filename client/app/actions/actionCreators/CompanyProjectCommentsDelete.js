import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import companyProjectCommentsSelect
  from '../../actions/actionCreators/CompanyProjectCommentsSelect';
import getAPIClient from '../../helpers/ApiClient';

const companyProjectCommentsDelete = async (supplierId, commentId) => {

  dispatcher.dispatch({
    type: ActionTypes.COMPANY_PROJECTS_COMMENTS_DELETE,
    commentId: commentId,
  });

  try {
    const apiClient = getAPIClient();
    const response = await apiClient.post(
      `/api/supplier/${supplierId}/comments/${commentId}/mark-deleted`, true
    );
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_UPDATED,
      comment: response.data.comment,
    });
  } catch (error) {
    // Something unexpected went wrong - maybe someone else was deleting at the same time,
    // or a network issue. Show a generic error message and reload the comments.
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_DELETE_FAILED
    })
    companyProjectCommentsSelect(supplierId);
  }
};

export default companyProjectCommentsDelete;
