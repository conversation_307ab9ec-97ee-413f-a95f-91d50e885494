import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';

const authCompleteSignIn = (authInfo, profile) => {
  dispatcher.dispatch({
    type: ActionTypes.AUTH_SIGNIN_COMPLETE,
    auth_info: authInfo,
    profile,
  });

  if (window.sessionStorage.getItem('sign_in_location')) {
    // Go back to the page we were looking at before
    // we were forced to hit the Sign in button.
    const location = window.sessionStorage.getItem('sign_in_location');
    window.sessionStorage.removeItem('sign_in_location');
    window.location.hash = location;
  }
};

export default authCompleteSignIn;
