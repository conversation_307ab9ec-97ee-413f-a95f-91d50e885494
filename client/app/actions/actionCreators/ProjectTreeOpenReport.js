import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import { buildApiURL } from '../../helpers/ApiClient';

const projectTreeOpenReport = companyId => {
  dispatcher.dispatch({
    type: ActionTypes.PROJECT_TREE_OPEN_COMPANY_REPORT,
    companyId,
  });

  const url = buildApiURL(`/api/company/${companyId}/report`, true);
  const win = window.open(url, '_blank');
  win.focus();
};

export default projectTreeOpenReport;
