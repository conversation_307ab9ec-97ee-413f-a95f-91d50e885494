import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';
import { defineMessages } from 'react-intl';

const messages = defineMessages({
  generalAddFailed: {
    id: 'comments.saveFailed',
    description: 'Text for unspecified comment app/update failure',
    defaultMessage: 'Could not save comment',
  }
});

const companyProjectCommentsAdd = async (supplierId, comment) => {

  dispatcher.dispatch({
    type: ActionTypes.COMPANY_PROJECTS_COMMENTS_ADD,
  });

  try {
    const client = getAPIClient();
    const result = await client.post(
      `/api/supplier/${supplierId}/add-comment`,
      true,
      {comment}
    );
    if (result.data.ok === true) {
      dispatcher.dispatch({
        type: ActionTypes.COMPANY_PROJECTS_COMMENTS_ADD_SUCCESS,
        comment: result.data.comment,
      });
      return true;
    }
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_ADD_FAILED,
      errors: result.data?.errors ? Object.values(result.data?.errors).flat()
        : [messages.generalAddFailed]
    });
    return false;
  } catch (error) {
    // Unexpected error
    dispatcher.dispatch({
      type: ActionTypes.COMPANY_PROJECTS_COMMENTS_ADD_FAILED,
      errors: [messages.generalAddFailed],
    });
    return false;
  }
};

export default companyProjectCommentsAdd;
