import ActionTypes from '../ActionTypes';
import dispatcher from '../../Dispatcher';
import getAPIClient from '../../helpers/ApiClient';
import selectCompanyDetails from './CompanyDetailsSelect';
import { PA_CONFIRM_STATE_ALLOW } from '../../Constants';

async function canConfirmPreannouncement(pid, companyId) {
  const url = `/api/preannouncement/${pid}/can_confirm`;

  dispatcher.dispatch({
    type: ActionTypes.PREANNOUNCEMENT_CAN_CONFIRM_LOADING_STARTED,
  });

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.get(url, true);
    const data = res.data;

    dispatcher.dispatch({
      type: ActionTypes.PREANNOUNCEMENT_CAN_CONFIRM_SUCCESS,
      response: data,
    });

    if (data.confirmation_state === PA_CONFIRM_STATE_ALLOW) {
      // BOL-5582 reload company details in order to show updated company status
      // in PA form when report has been fetched while PA form is open
      selectCompanyDetails(companyId);
    }
  } catch (error) {
    dispatcher.dispatch({
      type: ActionTypes.PREANNOUNCEMENT_CAN_CONFIRM_ERROR,
      error,
    });
  }
}

export default canConfirmPreannouncement;
