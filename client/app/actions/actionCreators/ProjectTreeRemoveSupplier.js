import ActionTypes from '../ActionTypes';
import DialogTypes from '../DialogTypes';
import dispatcher from '../../Dispatcher';

import getAPIClient from '../../helpers/ApiClient';

const projectTreeRemoveSupplier = (
  targetElement,
  projectId,
  supplierId,
  supplierRev,
  projectName,
  supplierName,
  childrenCount
) => {
  const onCancel = () => {
    dispatcher.dispatch({
      type: ActionTypes.CONFIRMATION_DIALOG_CANCEL,
    });
  };

  const onConfirm = async () => {
    const url = `/api/project/${projectId}/remove-supplier`;

    try {
      const apiClient = getAPIClient();
      await apiClient.post(url, true, {
        supplier_id: supplierId,
        supplier_revision: supplierRev,
      });

      dispatcher.dispatch({
        type: ActionTypes.PROJECT_TREE_RELOAD,
      });
      // Reload project list because statuses might have changed
      dispatcher.dispatch({
        type: ActionTypes.PROJECT_LIST_RELOAD,
      });
    } catch (error) {
      console.error('Failed to remove supplier:', error);
    }
  };

  dispatcher.dispatch({
    type: ActionTypes.CONFIRMATION_DIALOG_SHOW,
    dialogType: DialogTypes.CONFIRM_REMOVE_SUPPLIER,
    callback: onConfirm,
    targetElement,
    dialogParams: {
      supplierId,
      projectName,
      supplierName,
      childrenCount,
      handleCancel: onCancel,
    },
  });
};

export default projectTreeRemoveSupplier;
