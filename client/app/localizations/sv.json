{"CSAccountActivate.active_exists_error": "Det finns redan ett aktivt Creditsafe-konto för den här organisationen. Gå till Hem och sedan tillbaka till ID06 Bolagsdeklaration för att fortsätta.", "CSAccountActivate.button": "<PERSON>ra <PERSON>", "CSAccountActivate.creditsafeLinkTitle": "www.creditsafe.com", "CSAccountActivate.description": "F<PERSON><PERSON>j anvisningarna nedan för att aktivera ert Creditsafe-konto och för att ansluta det till ID06 Bolagsdeklaration.", "CSAccountActivate.forgotPassword.link": "https://login.creditsafe.com/password-reset?lang=sv-SE", "CSAccountActivate.forgotPassword.title": "Återställ lösenord", "CSAccountActivate.goToBolService": "Gå till ID06 Bolagsdeklaration", "CSAccountActivate.headerMainTitle": "Aktivera och anslut ert Creditsafe-konto", "CSAccountActivate.headerSuccessTitle": "<PERSON><PERSON>-konto är nu anslutet", "CSAccountActivate.invalid": "Det angivna lösenordet är ogiltigt", "CSAccountActivate.legalNote": "Uppgifter som presenteras i ID06 Bolagsdeklaration har tagits fram av Creditsafe i Sverige AB (556514-4408) som är ett kreditupplysningsföretag med tillstånd enligt kreditupplysningslagen", "CSAccountActivate.needHelp": "Behöver du hjälp? Kontakta {email}", "CSAccountActivate.password.placeholder": "<PERSON><PERSON>l<PERSON>", "CSAccountActivate.passwordField": "Creditsafe-lösenord", "CSAccountActivate.step1.description": "Creditsafe kommer att skicka aktiveringsmejl till den e-postadress som du har angivit till ditt ID06-konto. Mejlet innehåller en länk för att aktivera kontot. Klicka på länken och följ instruktionerna.", "CSAccountActivate.step1.header": "Ta emot aktiveringsmejl frå<PERSON>", "CSAccountActivate.step2.description": "V<PERSON><PERSON><PERSON> ett lösenord och svara på Creditsafes säkerhetsfrågor. Notera lösenordet då det ska anges i nästa steg.", "CSAccountActivate.step2.header": "Aktivera Creditsafe-konto", "CSAccountActivate.step3.description": "<PERSON><PERSON> ditt Creditsafe-l<PERSON>senord nedan och spara för att slutföra anslutningen av kontot till ID06 Bolagsdeklaration.", "CSAccountActivate.step3.header": "Anslut Creditsafe-kontot till ID06 Bolagsdeklaration", "CSAccountActivate.success": "Ett Creditsafe-konto har skapats och är nu anslutet till ID06 Bolagsdeklaration. Kontot ingår kostnadsfritt i tjänsten.", "CSAccountActivate.validation.field.required": "Obligatoriskt fält", "addProjectClient.searchMainContractorCannotAddAsProjectClient": "En huvudentreprenör kan inte lägga till sig själv som projektets beställare. Om ni är projektets beställare så välj det alternativet och lägg sedan till er själva som huvudentreprenör efter att projektet har skapats.", "addSubcontractorForm.contactPersonSearch.emailNotValid": "<PERSON>e en giltig e-postadress.", "addSupplier.addClientCountryHelpText": "ID06 Bolagsdeklaration får bara användas i projekt som utförs i Sverige", "addSupplier.cannot_add_error": "Det här företaget är redan en leverantör på den här nivån", "addSupplier.clear_search_tooltip": "<PERSON><PERSON><PERSON>", "addSupplier.contactPersonFindAdd": "Sök kontaktperson el<PERSON> lägg till e-postadress", "addSupplier.contactPersonFindAddPaProject": "Sök  kontaktperson el<PERSON> lägg till e-postadress för föranmälningar", "addSupplier.contactPersonSelect": "<PERSON><PERSON><PERSON><PERSON>ontakt<PERSON>", "addSupplier.contact_email_label": "E-postadress till leverantörens kontaktperson", "addSupplier.contact_person_find_add_for_client_label": "Kontaktperson som ska ges åtkomst till projektet", "addSupplier.contact_person_find_add_for_client_placeholder": "Fyll i e-postadressen till projektets beställare", "addSupplier.contact_person_found": "Kontaktperson funnen", "addSupplier.contact_person_found_not_active": "Användaren är inaktiverad och har inte tillgång till tjänsten.", "addSupplier.contact_person_found_without_permission": "Anvä<PERSON>ren har inte tillgång till tjänsten.", "addSupplier.contact_person_not_found": "Kontaktpersonen finns inte registrerad hos ID06. Användaren kommer inte få tillgång till tjänsten.", "addSupplier.contact_person_waiting_for_supplier_confirmation": "Ett e-postmeddelande kommer att skickas till e-postadressen när leverantören är bekräftad.", "addSupplier.enter_company_business_id_label_for_add_client": "Organisationsnummer eller VAT", "addSupplier.enter_company_business_id_or_vat_placeholder": "Organisationsnummer eller MOMS", "addSupplier.enter_company_business_id_placeholder": "Organisationsnummer", "addSupplier.enter_company_business_id_placeholder_for_add_client": "Organisationsnummer eller VAT", "addSupplier.save_and_add_action": "Spara och lägg till en annan", "addSupplier.save_button": "Spara", "addSupplier.search_by_vat_info_not_supported_countries": "Sökning med momsnummer stöds inte för Sverige, Litauen, Nederländerna och Norge", "addSupplier.search_cannot_add_private person": "Kan inte lägga till en privatperson som inte är enskild näringsidkare", "addSupplier.search_error": "Det här företaget existerar inte. Vänligen kontrollera organisationsnummer.", "addSupplier.search_error_check_id_or_vat": "Det här företaget existerar inte. Vänligen kontrollera organisationsnummer eller MOMS.", "addSupplier.search_in_progress_tooltip": "Sökning pågår", "add_user.addProjectUserLink": "Lägg till projektmedlem", "archivedReports.accessTimeLabel": "Arkiverad", "archivedReports.archiveIdLabel": "Arkiveringsnummer", "archivedReports.confirmDelete": "Är du säker på att du vill radera den här arkiverade rapporten?", "archivedReports.confirmDeleteAll": "Är du säker på att du vill radera ALLA arkiverade rapporter för det här företaget?", "archivedReports.confirmRemoval": "Bekräfta att du vill radera", "archivedReports.deleteAllReports": "Radera alla arkiverade rapporter", "archivedReports.deleteFailed": "Raderingen av arkiverade rapporter misslyckades.", "archivedReports.deleteReportLabel": "<PERSON><PERSON><PERSON>", "archivedReports.disabledHint": "Några av dina arkiverade rapporter är otillgängliga eftersom de hämtades innan din nuvarande beställning började. Vänligen kontakta vår kundtjänst om du har frågor.", "archivedReports.disabledNoSubscriptionHint": "De arkiverade rapporterna är otillgängliga, eftersom Rapport PRO-licensen är avslutad. Vänligen kontakta vår kundtjänst om du har frågor.", "archivedReports.disabledReportTooltip": "Den här rapporten är inte tillgänglig, eftersom den var hämtad före din nuvarande prenumeration började", "archivedReports.empty": "Det finns ingen information att visa", "archivedReports.endDate": "Slut<PERSON><PERSON>", "archivedReports.orderNowButton": "Beställ nu!", "archivedReports.orderNowWithoutPermission": "Be ditt företags huvudsakliga användare att beställa Rapport PRO-tjänsten.", "archivedReports.removeDialog.cancelButton": "<PERSON><PERSON><PERSON>", "archivedReports.removeDialog.removeButton": "<PERSON><PERSON><PERSON>", "archivedReports.reportIconLabel": "Rapport", "archivedReports.salesPitch": "<p><PERSON><PERSON> företag har laddat ner rapporter{allReports, number} gå<PERSON>, inkulsive {currentReports, number} gånger för företaget du ser på just nu. <b>Kom du ihåg att arkivera rapporterna?</b></p><p>Genom att beställa Rapport PRO-tjänsten blir rapporterna du laddar ner automatiskt arkiverade.</p>", "archivedReports.startDate": "Startdatum", "archivedReports.statusHidden": "Inte användbar", "archivedReports.statusLabel": "Status", "authorizationFailed.title": "Åtkomst Nekad: ID06 Bolagsdeklaration", "basepage.ID06": "ID06", "basepage.STV": "Vastuu Group Oy", "basepage.id06.page_title": "Välkommen till ID06 Bolagsdeklaration", "basepage.id06.welcome_text": "ID06 Bolagsdeklaration är en företagskontrolltjänst tillhandahållen av {provider}.", "basepage.register_new_company_body": "Om du inte har ett konto, {register_new_company}.", "basepage.register_new_company_link": "registrera en organisation", "basepage.sign_in_button": "Logga in", "basepage.stv.page_title": "Välkommen till Rapport-tjänsten", "basepage.stv.welcome_text": "I Rapport-tjänsten kan du söka Pålitlig Partner-rapporter. Tjänsten tillhandahålls av {provider}.", "basepage.licenseNeeded.header": "Åtkomst Nekad: Licens krävs", "basepage.licenceNeeded.body": "<PERSON><PERSON>r att kunna använda ID06 Bolagsdeklaration måste din organisation köpa en licens. Besök {orderBolUrl} för att lära dig hur du beställer en licens och hur du får tillgång till alla fördelar med denna tjänst.", "basepage.orderBolLinkText": "Beställ ID06 Bolagsdeklaration", "bulkImport.companyAlreadyInProject": "Finns redan i leverantörskedjan", "bulkImport.companyNotFound": "Organisationsnumret finns inte i register", "bulkImport.subscriptionFailed": "Fel uppstod vid tillägg till bevakning", "bulkImport.companyPending": "Avvaktande", "bulkImport.privatePerson": "Person, inte ett företag", "comments.addHelpText": "Kommentarer är enbart synliga för huvudentreprenören, projektets beställare och ID06 administratörer. Övriga leverantörer i projektet kan inte se eller skriva kommentarer.", "comments.addPlaceholder": "Skriv en kommentar om leverantören i projektet", "comments.by": "av", "comments.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comments.delete": "<PERSON> bort", "comments.deleted": "Borttagen", "comments.deleteFailed": "Kunde inte ta bort kommentaren.", "comments.deleteWarning": "Borttagna kommentarer kan inte återställas.", "comments.deleteWarningTitle": "Är du säker på att du vill ta bort kommentaren?", "comments.edit": "<PERSON><PERSON>", "comments.emptyList": "Det finns inga kommentarer om den här leverantören i det här projektet.", "comments.loadingFailed": "Kunde inte hämta kommentarer", "comments.save": "Spara", "comments.saveFailed": "Kunde inte spara kommentaren", "comments.title": "<PERSON><PERSON><PERSON><PERSON>", "comments.updated": "<PERSON><PERSON><PERSON>", "companies.ProjectsColumn": "Antal projekt", "companies.VATColumn": "MOMS", "companies.businessIdColumn": "Organisationsnummer", "companies.companyDetailsLink": "Info", "companies.countryColumn": "Land", "companies.filter.clearFilter": "Töm filter", "companies.filter.compSearchPlaceholder": "Företagsnamn eller org. nummer", "companies.filter.countryLabel": "Land", "companies.filter.countryTitle.ee": "Estland", "companies.filter.countryTitle.fi": "Finland", "companies.filter.countryTitle.lt": "Li<PERSON>uen", "companies.filter.countryTitle.lv": "Lettland", "companies.filter.countryTitle.pl": "<PERSON><PERSON>", "companies.filter.countryTitle.se": "Sverige", "companies.filter.queryTooShort": "<PERSON>e minst två tecken", "companies.filter.statusAny": "All status", "companies.filter.statusNotOK": "Inte OK", "companies.latestStatusColumn": "Senaste status", "companies.nameColumn": "Företag", "companies.nameunavailable": "Namnet hittas inte", "companies.pageheader": "Sök företag bland tidigare sökta företag", "companies.reportColumn": "Rapport", "companies.reportLangSw.inEnglish": "På engelska", "companies.reportLangSw.inFinish": "<PERSON><PERSON> fins<PERSON>", "companies.reportLangSw.inSwedish": "På svenska", "companies.status": "Status", "companies.statusColumn": "Status", "companies.tip.noSubscription": "<PERSON><PERSON> ser du ditt företags rapportsökningshistoria. Du har inte tillgång till de nedladdade rapporterna och kan inte heller öppna de rapporter som du tidigare laddat ned i tjänsten. Beställ <a href=\"/#/subscription\">Rapport PRO</a> för att arkivera rapporterna automatiskt i framtiden.", "companies.tip.subscribed": "Du använder tjänsten Rapport PRO. Välj företag för att se de arkiverade rapporterna.", "companyDetails.archivedReports": "Arkiverade rapporter", "companyDetails.businessIdLabel": "Organisationsnummer:", "companyDetails.certificate_publication": "Information nedan omfattas inte av utgivningsbeviset", "companyDetails.checkReportReminder": "<PERSON>dda ner, kontrollera och spara rapporten", "companyDetails.comments": "<PERSON><PERSON><PERSON><PERSON>", "companyDetails.companyLatestReportLink": "Visa senaste rapport", "companyDetails.companyNameLabel": "Företagsnamn:", "companyDetails.companyReportLink": "Se fullständig rapport", "companyDetails.companyReportUpdatedAt": "Rapport uppdaterad: {updatedAt}", "companyDetails.companyStatusHeading": "Status:", "companyDetails.companyStatusLabel": "Status:", "companyDetails.contextual_title": "Företagsinformation", "companyDetails.countryLabel": "Land:", "companyDetails.defaultModalTitle": "<PERSON><PERSON><PERSON>", "companyDetails.detailsHeading": "Företagsinformation", "companyDetails.finnishDetailsHeading": "Information om det finska företaget", "companyDetails.loadingErrorMessage": "<PERSON><PERSON>", "companyDetails.loadingFailedMessage": "Laddning misslyckades", "companyDetails.noSubscription": "Denna rapport kommer inte att vara tillgänglig för nedladdning i Sökhistorik, eftersom din Rapport PRO-prenumeration inte är aktiv.", "companyDetails.nonArchivable": "Denna rapport blir inte arkiverad. Endast finska och estniska företags rapporter arkiveras.", "companyDetails.notFoundMessage": "Uppgifter om företaget är inte tillgängliga. Vänligen kontakta vår kundtjänst vid behov.", "companyDetails.pdfsOnly": "OBS! Oberoende språkval, arkiveras rapporterna enbart på finska.", "companyDetails.relatedProject": "Relaterade projekt", "companyDetails.rpDetailsHeading": "Pålitlig Partner-information", "companyDetails.status.terminated": "<PERSON><PERSON><PERSON><PERSON>", "companyDetails.table.noDataToDisplay": "Det finns ingen information att visa", "companyDetails.terminated.tooltip": "Källa: Asiakastieto", "companyDetails.vatNumberLabel": "MOMS:", "companyReportMissingIconLabel": "Rapporten finns inte tillgänglig", "companyStatusChangeReport.failedMessage": "Det gick inte att ladda rapporten: <PERSON><PERSON><PERSON>g länk eller <PERSON>l", "companyStatusChangeReport.title": "Företag som har ändrat sin status till {status} ({dates})", "companyStatusChangeReport.unauthorizedMessage": "Kunde inte ladda rapporten: obeh<PERSON>rig", "confirmClientshipModal.bodyText1": "{project_creator} har angivit att er organisation är beställare av detta projekt och ska ha full insyn i projektet.", "confirmClientshipModal.bodyText2": "Om {project_client} bekr<PERSON>ftar nedan, kommer samtliga kreditrapporter visas och {project_client} kommer framgå som frågeställare på eventuella omfrågekopior.", "confirmClientshipModal.bodyText3": "Som beställare ges ni möjlighet att lägga till eventuella sidoentreprenörer.", "confirmClientshipModal.cancelAction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmClientshipModal.confirmAction": "Bekräfta", "confirmClientshipModal.headerText": "Bekräfta full insyn enligt avtal", "country.abw": "Aruba", "country.afg": "Afghanistan", "country.ago": "Angola", "country.aia": "<PERSON><PERSON><PERSON>", "country.ala": "Åland", "country.alb": "Albanien", "country.and": "Andorra", "country.are": "Förenade Arabemiraten", "country.arg": "Argentina", "country.arm": "Armenien", "country.asm": "Amerikanska Samoa", "country.ata": "An<PERSON><PERSON><PERSON>", "country.atf": "Franska sydterritorierna", "country.atg": "Antigua och Barbuda", "country.aus": "Australien", "country.aut": "Österrike", "country.aze": "Azerbajdzjan", "country.bdi": "Burundi", "country.bel": "Belgien", "country.ben": "Benin", "country.bes": "Bonaire, Sint Eustatius och Saba", "country.bfa": "Burkina Faso", "country.bgd": "Bangladesh", "country.bgr": "Bulgarien", "country.bhr": "Bahrain", "country.bhs": "Bahamas", "country.bih": "Bosnien-Hercegovina", "country.blm": "Saint-Bart<PERSON><PERSON><PERSON><PERSON>", "country.blr": "Vitryssland", "country.blz": "Belize", "country.bmu": "Bermuda", "country.bol": "Bolivia, Mångnationella staten", "country.bra": "Brasilien", "country.brb": "Barbados", "country.brn": "Brunei", "country.btn": "Bhutan", "country.bvt": "Bouvetön", "country.bwa": "Botswana", "country.caf": "Centralafrikanska republiken", "country.can": "Ka<PERSON><PERSON>", "country.cck": "Ko<PERSON><PERSON><PERSON><PERSON>", "country.che": "Schweiz", "country.chl": "Chile", "country.chn": "<PERSON><PERSON>", "country.civ": "Elfenbenskusten", "country.cmr": "Kamerun", "country.cod": "Kongo, demokratiska republiken", "country.cog": "Kong<PERSON>", "country.cok": "<PERSON><PERSON><PERSON><PERSON>", "country.col": "Colombia", "country.com": "<PERSON><PERSON><PERSON>", "country.cpv": "Cabo Verde", "country.cri": "Costa Rica", "country.cub": "Ku<PERSON>", "country.cuw": "Curaçao", "country.cxr": "<PERSON><PERSON><PERSON>", "country.cym": "Cayman<PERSON><PERSON><PERSON>", "country.cyp": "<PERSON><PERSON><PERSON>", "country.cze": "Czechia", "country.deu": "Tyskland", "country.dji": "Djibouti", "country.dma": "Dominica", "country.dnk": "Danmark", "country.dom": "Dominikanska republiken", "country.dza": "Algeriet", "country.ecu": "Ecuador", "country.egy": "<PERSON><PERSON>", "country.eri": "Eritrea", "country.esh": "Västsahara", "country.esp": "Spanien", "country.est": "Estland", "country.eth": "Etiopien", "country.fin": "Finland", "country.fji": "Fiji", "country.flk": "Falklandsöarna (Malvinas)", "country.fra": "<PERSON><PERSON><PERSON>", "country.fro": "F<PERSON>r<PERSON><PERSON><PERSON>", "country.fsm": "<PERSON><PERSON><PERSON><PERSON><PERSON>, federala staterna", "country.gab": "Gabon", "country.gbr": "Förenade kungariket", "country.geo": "<PERSON><PERSON>", "country.ggy": "Guernsey", "country.gha": "Ghana", "country.gib": "Gibraltar", "country.gin": "Guinea", "country.glp": "Guadeloupe", "country.gmb": "Gambia", "country.gnb": "Guinea-Bissau", "country.gnq": "Ekvatorialguinea", "country.grc": "Grekland", "country.grd": "Grenada", "country.grl": "Grönland", "country.gtm": "Guatemala", "country.guf": "Franska Guiana", "country.gum": "Guam", "country.guy": "Guyana", "country.hkg": "Hong Kong", "country.hmd": "<PERSON><PERSON><PERSON> o<PERSON>", "country.hnd": "Honduras", "country.hrv": "<PERSON><PERSON><PERSON>", "country.hti": "Haiti", "country.hun": "<PERSON><PERSON><PERSON>", "country.idn": "Indonesien", "country.imn": "Isle of Man", "country.ind": "Indien", "country.iot": "Brittiskt territorium i Indiska Oceanen", "country.irl": "Irland", "country.irn": "Iran, islamiska republiken", "country.irq": "<PERSON><PERSON>", "country.isl": "Island", "country.isr": "Israel", "country.ita": "Italien", "country.jam": "Jamaica", "country.jey": "Jersey", "country.jor": "<PERSON><PERSON>", "country.jpn": "Japan", "country.kaz": "Kazakstan", "country.ken": "Kenya", "country.kgz": "Kirgisistan", "country.khm": "Kambodja", "country.kir": "Kiribati", "country.kna": "Sankt Kitts och Nevis", "country.kor": "Sydkorea", "country.kwt": "Kuwait", "country.lao": "Demokratiska folkrepubliken Lao", "country.lbn": "Libanon", "country.lbr": "Liberia", "country.lby": "Libyen", "country.lca": "Sankt Lucia", "country.lie": "Liechtenstein", "country.lka": "Sri Lanka", "country.lso": "Lesotho", "country.ltu": "Li<PERSON>uen", "country.lux": "Luxemburg", "country.lva": "Lettland", "country.mac": "Macao", "country.maf": "<PERSON> (franska delen)", "country.mar": "<PERSON><PERSON><PERSON>", "country.mco": "Monaco", "country.mda": "Moldavien, republiken", "country.mdg": "<PERSON><PERSON><PERSON>", "country.mdv": "Maldiverna", "country.mex": "Mexico", "country.mhl": "<PERSON><PERSON><PERSON><PERSON>", "country.mkd": "<PERSON><PERSON><PERSON>, republiken", "country.mli": "Mali", "country.mlt": "Malta", "country.mmr": "Myanmar", "country.mne": "Montenegro", "country.mng": "Mongoliet", "country.mnp": "Nordmarianerna", "country.moz": "Moçambique", "country.mrt": "<PERSON><PERSON><PERSON><PERSON>", "country.msr": "Montserrat", "country.mtq": "Martinique", "country.mus": "Mauritius", "country.mwi": "Malawi", "country.mys": "Malaysia", "country.myt": "Mayotte", "country.nam": "Namibia", "country.ncl": "<PERSON><PERSON>", "country.ner": "Niger", "country.nfk": "<PERSON><PERSON><PERSON><PERSON>", "country.nga": "Nigeria", "country.nic": "Nicaragua", "country.niu": "Niue", "country.nld": "Nederländerna", "country.nor": "Norge", "country.npl": "Nepal", "country.nru": "Nauru", "country.nzl": "<PERSON><PERSON>", "country.omn": "Oman", "country.pak": "Pakistan", "country.pan": "Panama", "country.pcn": "Pitcairn", "country.per": "Peru", "country.phl": "Filippinerna", "country.plw": "<PERSON><PERSON>", "country.png": "Papua Nya Guinea", "country.pol": "<PERSON><PERSON>", "country.pri": "Puerto Rico", "country.prk": "Korea, demokratiska folkrepubliken", "country.prt": "Portugal", "country.pry": "Paraguay", "country.pse": "Staten Palestina", "country.pyf": "Franska Polynesien", "country.qat": "Qatar", "country.registration.label": "Registreringsland", "country.reu": "Réunion", "country.rou": "Rumänien", "country.rus": "Ryska federationen", "country.rwa": "Rwanda", "country.sau": "Saudiarabien", "country.sdn": "Sudan", "country.sen": "Senegal", "country.sgp": "Singapore", "country.sgs": "Sydgeorgien och södra <PERSON>öarna", "country.shn": "<PERSON>, Ascension och <PERSON> Cunha", "country.sjm": "Svalbard och <PERSON>", "country.slb": "Salomonöarna", "country.sle": "Sierra Leone", "country.slv": "El Salvador", "country.smr": "San Marino", "country.som": "Somalia", "country.spm": "Sankt Pierre o<PERSON> Miquelon", "country.srb": "<PERSON><PERSON>", "country.ssd": "<PERSON><PERSON><PERSON><PERSON>", "country.stp": "São Tomé och Príncipe", "country.sur": "Surinam", "country.svk": "Slovakien", "country.svn": "Slovenien", "country.swe": "Sverige", "country.swz": "Swaziland", "country.sxm": "Sint Maarten (nederländska delen)", "country.syc": "<PERSON><PERSON><PERSON><PERSON>", "country.syr": "Syriska arabrepubliken", "country.tca": "Turks- och Caicosöarna", "country.tcd": "Tchad", "country.tgo": "Togo", "country.tha": "Thailand", "country.tjk": "Tadzjikistan", "country.tkl": "Tokelau", "country.tkm": "Turkmenistan", "country.tls": "<PERSON><PERSON><PERSON><PERSON>", "country.ton": "Tonga", "country.tto": "Trinidad och Tobago", "country.tun": "Tunisien", "country.tur": "Turkiet", "country.tuv": "Tuvalu", "country.twn": "Taiwan, provins i <PERSON>na", "country.tza": "Tanzania, förenade republiken", "country.uga": "Uganda", "country.ukr": "<PERSON><PERSON><PERSON>", "country.umi": "USA:s avlägsna mindre öar", "country.ury": "Uruguay", "country.usa": "USA", "country.uzb": "Uzbekistan", "country.vat": "Vatikanstaten", "country.vct": "Sankt Vincent och Grenadinerna", "country.ven": "Venezuela, Bolivarianska republiken", "country.vgb": "Jung<PERSON><PERSON><PERSON><PERSON><PERSON>, brittiska", "country.vir": "Jungfruöarna, amerikanska", "country.vnm": "Vietnam", "country.vut": "Vanuatu", "country.wlf": "<PERSON> och <PERSON>a", "country.wsm": "Samoa", "country.yem": "Yemen", "country.zaf": "Sydafrika", "country.zmb": "Zambia", "country.zwe": "Zimbabwe", "createCSAccount.bodyMainText": "<PERSON><PERSON>r att ni ska kunna använda ID06 Bolagsdeklaration måste ID06 skapa ett Creditsafe-konto som tillhör er. Kontot kommer enbart kunna användas för kreditbevakning i ID06 Bolagsdeklaration. Kontot ingår kostnadsfritt i tjänsten.", "createCSAccount.changeUserDetailsLink": "<PERSON><PERSON>gi<PERSON>", "createCSAccount.createCSAccountButton": "Skapa Creditsafe-konto", "createCSAccount.errorAccountAlreadyExists": "Det finns redan ett aktivt Creditsafe-konto för den här organisationen. Gå till Hem och sedan tillbaka till ID06 Bolagsdeklaration för att fortsätta.", "createCSAccount.errorAccountPendingExists": "Du har redan skapat ett Creditsafe-konto för den här organisationen. Gå till Hem och sedan tillbaka till ID06 Bolagsdeklaration för att fortsätta.", "createCSAccount.errorAutoaccount": "<PERSON>tt fel uppstod när ert Creditsafe-konto skulle skapas. Kontakta {emailLink} så hjälper vi dig vidare.", "createCSAccount.errorForeignCompany": "Företaget måste registrera ett svenskt skattenummer hos ID06 för att få skapa ett Creditsafe-konto. Kontakta en administratatör för ert företagskonto så kan denne registrera uppgiften. Vet du inte vem som är administratör så kontakta {emailLink} så hjälper vi dig vidare.", "createCSAccount.errorMessageFromCS": "Felmeddelande från <PERSON>afe:", "createCSAccount.headerMainTitle": "Skapa ett Creditsafe-konto", "createCSAccount.headerUserDetails": "ANVÄNDARUPPGIFTER", "createCSAccount.userEmailAddress": "E-post:", "createCSAccount.userPhoneNumber": "Telefonummer:", "customerSupport.TnC": "GDPR och avtal", "customerSupport.TnCDesc": "ID06 Allmänna bestämmelser och särskilda villkor för ID06 Bolagsdeklaration.", "customerSupport.contactUsTitle": "KONTAKTA OSS", "customerSupport.customerSupportTitle": "KUNDSUPPORT", "customerSupport.faq": "Vanliga frågor och svar", "customerSupport.faqDesc": "Vanliga frågor  och svar om ID06 Bolagsdeklaration.", "customerSupport.legalTitle": "ANVÄNDARVILLKOR", "customerSupport.userGuidePdf": "ID06 Bolagsdeklaration Användarguide.pdf", "customerSupport.userGuidePdfDesc": "Beskrivningar och instruktioner för hur du använder ID06 Bolagsdeklaration.", "customerSupport.userGuidesTitle": "ANVÄNDARGUIDER", "customerSupport.whatIsBolagsdeklaration": "ID06 Bolagsdeklaration är en företagskontrolltjänst som tillhandahålls av {id06Link}.", "details.closeDetails": "Stäng vy", "idCardIconLabel": "Inpasseringsperiod", "loading.empty": "Inga objekt hittades", "loading.failed": "Laddning misslyckades.", "loading.initial": "<PERSON><PERSON> s<PERSON>k<PERSON>", "loading.loading": "Laddar ...", "openCompanyDetailsIconLabel": "Öppna företagsinformation", "openCompnyReportIconLabel": "Öppna företagsrapport", "paStatusLabel.confirmed": "Bekräftad", "paStatusLabel.created": "Registrera", "paStatusLabel.inReview": "Granskas", "paStatusLabel.pa_created": "Created", "paStatusLabel.registered": "Granska", "paStatusLabel.rejected": "Avvisad", "paStatusLabel.waitingForRegistration": "Väntar på registrering", "preannouncementDetails.cant_load_pa": "Uppdatering av föranmälan misslyckades.", "preannouncementDetails.contactinfo_header": "Kontaktinformation", "preannouncementDetails.contracLongerThanSixMonths": "Kontrakt som avser arbete längre än 6 månader kräver ett fast driftställe", "preannouncementDetails.emailhelptext": "E-postadressen kommer att användas för att skicka aviseringar avseende föranmälningar", "preannouncementDetails.foreman_header": "Arbetsledare", "preannouncementDetails.informant_customer_header": "Uppgiftslämnare beställare", "preannouncementDetails.informant_header": "Uppgiftslämnare leverantör", "preannouncementDetails.no": "<PERSON><PERSON>", "preannouncementDetails.paCanConfirmOverride": "Rapporten kunde inte hämtas", "preannouncementDetails.paCanConfirmWait": "Rapporten hä<PERSON>…", "preannouncementDetails.paCanConfirmWaitHelpText": "Rapporten hämtas i bakgrunden. Stäng fönstret och prova igen om någon minut.", "preannouncementDetails.pa_active_cards": "Aktiva kort", "preannouncementDetails.pa_assigned_to": "Tilldelad till", "preannouncementDetails.pa_assigned_to_date": "<PERSON><PERSON><PERSON> datum", "preannouncementDetails.pa_collective_agreement": "Är företaget medlem i en arbetsgivarorganisation eller har företaget tecknat hängavtal?", "preannouncementDetails.pa_collective_agreement_name": "Ange namn på avtalet", "preannouncementDetails.pa_collective_agreement_placeholder": "Exempelvis Entreprenadmaskinavtalet", "preannouncementDetails.pa_company_business_id": "Organisationsnummer", "preannouncementDetails.pa_company_information_header": "Företagsinformation", "preannouncementDetails.pa_company_name": "Företagsnamn", "preannouncementDetails.pa_company_status": "Status", "preannouncementDetails.pa_company_status_cannot_be_determined": "Status kan inte avgöras eftersom rapport saknas. Kontrollera att det finns stöd för bevakning av registreringslandet enligt {faq_link}. Om det finns stöd och rapporten ändå saknas, kontakta {mail_link}", "preannouncementDetails.pa_company_status_faq_link_text": "FAQ", "preannouncementDetails.pa_confirm": "Bekräfta", "preannouncementDetails.pa_confirm_by_buyer": "Bekräfta och lämna in", "preannouncementDetails.pa_confirmed_date": "Bekräftad datum", "preannouncementDetails.pa_confirmed_name": "Bekräftad av", "preannouncementDetails.pa_construction_client": "<PERSON><PERSON><PERSON><PERSON>", "preannouncementDetails.pa_contact_info_email": "<PERSON><PERSON><PERSON>r e-postadress", "preannouncementDetails.pa_contact_info_email_confirm": "Bekräfta e-postadress", "preannouncementDetails.pa_contract_information_header": "Kontraktsinformation", "preannouncementDetails.pa_contract_type": "Kontraktstyp", "preannouncementDetails.pa_contractor": "Entreprenör", "preannouncementDetails.pa_country_of_regiser": "Registreringsland", "preannouncementDetails.pa_customer": "Beställare", "preannouncementDetails.pa_email_not_valid": "<PERSON>e en giltig e-postadress.", "preannouncementDetails.pa_end_date": "Slut<PERSON><PERSON>", "preannouncementDetails.pa_foreman_email": "E-post", "preannouncementDetails.pa_foreman_first_name": "Förnamn", "preannouncementDetails.pa_foreman_last_name": "E<PERSON>nam<PERSON>", "preannouncementDetails.pa_foreman_on_site": "Kommer ni ha en arbetsledare på plats?", "preannouncementDetails.pa_foreman_phone_number": "Telefon", "preannouncementDetails.pa_informant": "<PERSON><PERSON>", "preannouncementDetails.pa_informant_mail": "E-postadress", "preannouncementDetails.pa_informant_phone": "Telefonnummer", "preannouncementDetails.pa_intro": "Använd nedanstående formulär för att registrera uppgifter om ditt företag och ditt avtal i projektet. Kontrollera även den kontraktsinformation som angivits av din beställare. Informationen som anges nedan kommer att granskas av din beställare som därefter lämnar in föranmälan. Samtliga företag ovanför dig i leverantörskedjan kommer därefter granska uppgifterna. När föranmälan är bekräftad kommer du att motta en avisering.", "preannouncementDetails.pa_main_contractor": "Huvudentreprenör", "preannouncementDetails.pa_one_man_business": "Är företaget ett enmansföretag?", "preannouncementDetails.pa_one_man_business_helptext": "Som enmansföretag räknas ett bolag med en anställd eller en enskild näringsidkare utan anställda.", "preannouncementDetails.pa_perm_place": "<PERSON><PERSON><PERSON><PERSON><PERSON> det grund för att företaget ska ha fast driftställe när arbetet påbörjas?", "preannouncementDetails.pa_professional_work": "Yrkesområde inom entreprenaden", "preannouncementDetails.pa_project_info_header": "Projektinformation", "preannouncementDetails.pa_project_name": "Projektnamn", "preannouncementDetails.pa_received": "Mottagen", "preannouncementDetails.pa_register": "Registrera", "preannouncementDetails.pa_register_close": "Stäng", "preannouncementDetails.pa_register_error": "Alla obligatoriska fält har inte fyllts i", "preannouncementDetails.pa_reject": "Avvisa", "preannouncementDetails.pa_rejected_date": "Avvisad datum", "preannouncementDetails.pa_rejected_name": "Avvisad av", "preannouncementDetails.pa_reviewer": "<PERSON><PERSON><PERSON>", "preannouncementDetails.pa_start_date": "Startdatum", "preannouncementDetails.pa_status": "Status", "preannouncementDetails.pa_submitted_date": "Inlämnad datum", "preannouncementDetails.pa_vat": "VAT-nummer", "preannouncementDetails.preannouncement_header": "Föranmälan", "preannouncementDetails.preannouncement_header_created": "Registrera föranmälan för ", "preannouncementDetails.preannouncement_header_registered": "Föranmälan för ", "preannouncementDetails.swTaxNumberHelptext": "Av leverantören registrerad uppgift i ID06 systemet.", "preannouncementDetails.swedishTaxNumber": "Svenskt skattenummer", "preannouncementDetails.yes": "<PERSON>a", "project.editProjectButtonInTitle": "Redigera projekt", "project.editProjectPanelTitle": "Redigera projekt", "project.newProjectPanelTitle": "Nytt projekt", "project.stateActive": "Aktiv", "project.stateClosed": "Stängd", "project.stateDraft": "Utkast", "project.statuses.failed": "Uppdateringen av projektstatus misslyckades.", "project.statuses.nonPaedTotal": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>", "project.statuses.supplierTotal": "Leverantörer totalt", "project.statuses.visitorTotal": "Besökande företag", "projectDetails.addFirstSupplier": "Lägg till den första leverantören", "projectDetails.bulkSupplierAdd": "Lägg till flera leverantörer", "projectDetails.bulkSupplierAddEmptyProject": "Lägg till flera leverantörer", "projectDetails.detailsHeading": "Projektinformation", "projectDetails.emptyProjectMessage": "Det finns inga leverantörer på det här projektet", "projectDetails.endDateLabel": "Slutdatum:", "projectDetails.inlineAddSupplierFormTitle": "Lägg till leverantörer till projektet", "projectDetails.internalIdLabel": "Internt projektnummer:", "projectDetails.loadingFailedMessage": "Laddning misslyckades", "projectDetails.projectMembers": "Projektmedlem", "projectDetails.projectMembers.noBolPermissions": "Ingen tillgång till service", "projectDetails.projectMembers.pendingInvitation": "Väntar på användarens bekräftelse", "projectDetails.relatedSupplier": "Projektleverantörer", "projectDetails.singleSupplierAdd": "Lägg till leverantör", "projectDetails.singleSupplierPreannounce": "Föranmäl underleverantör", "projectDetails.startDateLabel": "Startdatum:", "projectDetails.stateLabel": "Skede:", "projectDetails.statusHeading": "Status", "projectDetails.supplyChainHeading": "Leverantörskedja", "projectDetails.taxIdLabel": "Byggarbetsplats-ID:", "projectDetails.viewProjectReport": "Visa projektrapport", "projectForm.addProjectClientLink": "Lägg till projektets beställare", "projectForm.addedClientCanView": "Projektets beställare ska ha full insyn i projektet", "projectForm.addedClientCanViewInfoLabel": "Om projektets beställare har beviljats full insyn kan det inte återkallas senare.", "projectForm.addedClientCannotView": "Projektets beställare ska inte kunna se eller ha tillgång till projektet", "projectForm.cancelAction": "Å<PERSON><PERSON>", "projectForm.companyNameIsLabel": "är:", "projectForm.compulsoryNote": "Markerar ett obligatoriskt fält", "projectForm.endDate": "Slut<PERSON><PERSON>", "projectForm.infoHeading": "Projektinformation", "projectForm.internalProjectIdLabel": "Internt projektnummer", "projectForm.mainContractorLabel": "Huvudentreprenör", "projectForm.noTaxIdLabel": "Det här projektet behöver inte ha ett byggarbetsplats-ID", "projectForm.paCheckboxInfoLabel": "Det är inte möjligt att i efterhand ändra projekt till att använda ID06 Digital föranmälan.", "projectForm.projectClientLabel": "Projektets beställare", "projectForm.projectNameDescription": "Projektnamnet bör vara tydligt och beskrivande av projektet", "projectForm.projectNameLabel": "Projektnamn", "projectForm.requirePAcheckboxLabel": "Det här projektet använder ID06 Digital föranmälan", "projectForm.saveButton": "Spara", "projectForm.startDate": "Startdatum", "projectForm.stateLabel": "<PERSON><PERSON><PERSON>", "projectForm.taxIdDescription": "Internt projektID (kan väljas fritt) eller Byggplats ID (givet av Skattemyndigheten) krävs innan man kan aktivera projektet.", "projectForm.taxIdLabel": "Byggarbetsplats-ID", "projectReport.attentionSymbolDescription": "Företaget bör ge in ytterligare information avseende angivna avvikelser eller att viss information bör noteras", "projectReport.enclosure": "Uppgifter som presenteras i ID06 Bolagsdeklaration har tagits fram av Creditsafe i Sverige AB (556514-4408) som är ett kreditupplysningsföretag med tillstånd enligt kreditupplysningslagen.", "projectReport.failedOrNoPermissions": "Inläsning av projektrapporten misslyckades eller så har du inte behörighet att se rapporten.", "projectReport.incompleteSymbolDescription": "Information saknas från <PERSON>", "projectReport.informationCoverage": "Information nedan omfattas inte av utgivningsbeviset.", "projectReport.investigateSymbolDescription": "Företaget uppfyller inte kontrollerade kriterier och bör utredas", "projectReport.latestRegisteredChange": "Senaste registrerade förändringen: ", "projectReport.noReportRetrieved": "Status kan inte avgöras eftersom rapporten ännu inte har hämtats.", "projectReport.okSymbolDescription": "Företagets uppfyller kontrollerade kriterier", "projectReport.projectSuppliersDetails": "Detaljerad information om leverantörer", "projectReport.projectSupplyChain": "Leverantörskedja", "projectReport.reportDescription": "Rapportförklaring", "projectReport.reportSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectReport.stopSymbolDescription": "KONTROLLERA FÖRETAGET!", "projectReport.supplierCriteriaForForeignCompanies": "Leverantörskriterier för utländska bolag", "projectReport.supplierCriteriaForForeignCompanies.vatRegistration": "VAT registrering: Registrerad i lokalt momsregister", "projectReport.supplierCriteriaForSwedishCompanies": "Leverantörskriterier för svenska bolag", "projectReport.supplierCriteriaForSwedishCompanies.auditor": "Kontroll av krav för revisor: <PERSON><PERSON> revisor", "projectReport.supplierCriteriaForSwedishCompanies.companyForm": "Företags-/Organisationsform för svenska företag: AB, EF eller HB/KB", "projectReport.supplierCriteriaForSwedishCompanies.companyStatus": "Företagsstatus: Ingen statuspåverkan noterad", "projectReport.supplierCriteriaForSwedishCompanies.companyStatusActive": "Företagsstatus: Aktivt", "projectReport.supplierCriteriaForSwedishCompanies.debtBalanceCompanyOrPrivate": "Skuldsaldo enskilda mål hos Kronofogden: <PERSON><PERSON>reta<PERSON> har inga eller ringa skulder avseende enskilda mål", "projectReport.supplierCriteriaForSwedishCompanies.debtBalanceTaxesAndFees": "Skuld<PERSON><PERSON> skatter och avgifter hos Kronofogden: <PERSON><PERSON><PERSON><PERSON> har inga eller ringa skatteskulder", "projectReport.supplierCriteriaForSwedishCompanies.fTaxControl": "F-skatt kontroll: Innehar F-skatt eller FA-skatt (enskild firma)", "projectReport.supplierCriteriaForSwedishCompanies.payrollRegister": "Svenska arbetsgivarregistret: Registrerad i arbetsgivarregistret", "projectReport.supplierCriteriaForSwedishCompanies.rating": "Rating: <PERSON><PERSON> låg risk till medelrisk för insolvens inom 12 månader", "projectReport.supplierCriteriaForSwedishCompanies.representativeCheck": "Företrädarkontroll svenska AB: Ingen negativ information hittades", "projectReport.supplierCriteriaForSwedishCompanies.vatRegister": "Svenska momsregistret: Registrerad i momsregistret eller organisationsformen momsregistreras normalt inte", "projectReport.symbols": "Symboler", "projectSuppliers.removeDialog.removableSupplier": "<PERSON>mmer att tas bort", "projectSuppliers.removeDialog.removableSuppliers": "<PERSON><PERSON> dessutom att tas bort", "projectTree.SupplierRoleDowngradeDialogComponent.heading": "Leverantören kan inte ha en roll {previous_role} på den här nivån av leverantörskedjan. Genom att göra den här flytten kommer rollen att ändras till leverantör och förmånerna kommer att tas bort.", "projectTree.clientRole": "Projektets beställare", "projectTree.edit.exit": "Av<PERSON><PERSON>a utan att spara", "projectTree.edit.save": "Spara", "projectTree.edit.saveErrros": "<PERSON><PERSON> fel uppstod när ändringar skulle sparas", "projectTree.noRegisteredNonPaedSuppliers": "Det finns inga ej föranmälda leverantörer", "projectTree.noRegisteredVisitors": "Det finns inga registrerade besökande företag", "projectTree.noUnlinkedSuppliers": "Det finns inga ej länkade leverantörer", "projectTree.nonPaedSuppliers": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>", "projectTree.removeDialog.removeButton": "<PERSON> bort", "projectTree.removeDialog.removeSingleSupplier": "<PERSON><PERSON> kommer att ta bort leverantören från trädet och kan inte ångras (men du kan lägga till leverantören på nytt)", "projectTree.removeDialog.removeSupplierWithChildren": "<PERSON><PERSON> kommer att ta bort leverantören tillsammans med dess {childrenCount} {childrenCount, plural, one {underleverantör} other {underleverantörer}} från trädet och kan inte ångras.", "projectTree.removeDialog.title": "<PERSON> bort lever<PERSON>ör {name}", "projectTree.supplierRoleDowngrade.title": "<PERSON><PERSON><PERSON>", "projectTree.supplierRoleDowngradeDialog.confirmAction": "OK", "projectTree.title": "Projekt:", "projectTree.unlinkedSuppliers": "<PERSON>j länkade leverantörer", "projectTree.visitors": "Besökande företag", "projectUsers.actionsLabel": "Åtgärder", "projectUsers.cancelAction": "<PERSON><PERSON><PERSON>", "projectUsers.changeSuccess": "Projektmedlemmarna har uppdaterats framgångsrikt", "projectUsers.companyLabel": "Företag", "projectUsers.deletDialog.contentMessage": "Den här åtgärden tar bort användaren {email} med roll {role} som företräder företaget {company} från projektet. Du kan lägga till den här användaren till projektet igen.", "projectUsers.deletDialog.titleMessage": "Ta bort användare {email} med roll {role}?", "projectUsers.deleteUserLink": "<PERSON><PERSON><PERSON>", "projectUsers.notifyLabel": "<PERSON><PERSON><PERSON>", "projectUsers.removeUserLink": "<PERSON> bort", "projectUsers.roleLabel": "Roll", "projectUsers.saveButton": "Spara", "projectUsers.userInfoLabel": "Användarinformation", "projectUsers.usersListEmpty": "<PERSON><PERSON> är tom", "projectUsers.usersListSelectPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "projects.addNewProject": "Lägg till ett nytt projekt", "projects.addNewProjectCompact": "<PERSON><PERSON><PERSON> till", "projects.contractorsColumn": "<PERSON><PERSON>", "projects.filter.searchLabel": "<PERSON>ö<PERSON>", "projects.filter.searchPlaceholder": "Projektnamn eller nummer", "projects.filter.stateLabel": "<PERSON><PERSON><PERSON>", "projects.filter.statusLabel": "Status", "projects.nameColumn": "Projekt", "projects.node.addSingleSupplier": "Lägg till ny leverantör", "projects.node.beingMoved": "<PERSON><PERSON> fö<PERSON>", "projects.node.visitDate": "Besöksdatum", "projects.node.visitPeriod": "Besöksperiod", "projects.node.print": "Skriva ut", "projects.node.undoText": "<PERSON><PERSON><PERSON> flytt", "projects.nodemenu.addSupplier": "Lägg till leverantör", "projects.nodemenu.cancelMove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projects.nodemenu.companyDetails": "Visa företag", "projects.nodemenu.createNewPreannouncement": "Skapa ny föranmälan", "projects.nodemenu.editSupplier": "<PERSON><PERSON><PERSON>", "projects.nodemenu.moveSupplier": "Flytta leverantör", "projects.nodemenu.openPreannouncement": "Visa föranmälan", "projects.nodemenu.placeSupplier": "<PERSON><PERSON>", "projects.nodemenu.preannounceSupplier": "Föranmäl underleverantör", "projects.nodemenu.printNoStatuses": "Skriv ut utan status", "projects.nodemenu.removeSupplier": "Ta bort lever<PERSON>ör", "projects.nodemenu.rootAddSupplier": "Lägg till leverantör", "projects.nodemenu.viewReport": "Se rapport", "projects.projectUsers.roleLabel": "<PERSON><PERSON><PERSON><PERSON> roll", "projects.projectUsers.searchLabel": "<PERSON><PERSON>k använda<PERSON>", "projects.stateColumn": "<PERSON><PERSON><PERSON>", "projects.statusColumn": "Status", "relatedProjects.endDateLabel": "Slut<PERSON><PERSON>", "relatedProjects.loadingFailed": "Laddning misslyckades", "relatedProjects.preannouncement": "Föranmälan", "relatedProjects.projectLabel": "Projekt", "relatedProjects.startDateLabel": "Startdatum", "relatedProjects.statusLabel": "Status", "relatedSuppliers.buyerLabel": "Inköpare", "relatedSuppliers.contactPerson": "Kontaktperson", "relatedSuppliers.reportLabel": "Rapport", "relatedSuppliers.supplierLabel": "Leverantör", "relatedSuppliers.commentsLabel": "<PERSON><PERSON><PERSON><PERSON>", "relatedSuppliers.unreadCommentAriaLabel": "<PERSON><PERSON><PERSON><PERSON> kommentarer", "relatedSuppliers.readCommentAriaLabel": "<PERSON><PERSON><PERSON> kommentarer", "roles.project.leader": "Led<PERSON>", "roles.project.leader.description": "Kan se projekt och hantera användare från egen organisation", "roles.project.manager": "Chef", "roles.project.manager.description": "Kan se projekt och hantera användare från egen organisation", "roles.project.member": "Me<PERSON>m", "roles.project.member.description": "Kan se projekt", "roles.supplier.main_contractor": "Huvudentreprenör", "roles.supplier.main_contractor.description": "<PERSON><PERSON><PERSON> och redigera åtkomsträttigheter till hela projektleverantörskedjan", "roles.supplier.supervisor": "ID06 Administratör", "roles.supplier.supervisor.description": "<PERSON><PERSON><PERSON> och redigera åtkomsträttigheter till hela projektleverantörskedjan", "roles.supplier.supplier": "Leverantör", "roles.supplier.supplier.description": "Inga rättigheter har beviljats", "search.countryColumn": "Land", "search.message.noSubscription": "Du använder för tillfället en gratisversion av tjänsten Rapport, där du kan söka Pålitlig Partner-rapporter. Beställ Rapport PRO för att arkivera rapporterna automatiskt!", "search.message.subscribeButton": "Beställ Rapport PRO", "search.nameColumn": "Företag", "search.pageheader": "<PERSON><PERSON><PERSON>lig Partner-rapporter", "search.ralaColumn": "RALA", "search.row.terminated": "<PERSON><PERSON><PERSON><PERSON>", "search.row.terminated.tooltip": "Källa: Bisnode", "search.rpColumn": "Pålitlig Partner", "search.rpColumn.short": "PP", "search.tip.subscribed": "<PERSON> använder tjänsten Rapport PRO, och de rapporter som du laddar ned arkiveras automatiskt. Rapportarkivet hittas på fliken <a href=\"/#/companies\">Sökhistorik.</a>", "search.tooManyResults": "F<PERSON>r många sökresultat. Specificera din sökning, om företaget du sökte inte finns i sökresultaten.", "searchCompanyDetails.gotoParentCompany": "Gå till huvudbolaget", "searchCompanyDetails.hasSubsidiaries": "Företagets form är utländskt samfund eller filial. Du hittar Pålitlig Partner-rapporten i det utländska huvudbolagets information.", "searchCompanyDetails.multipleSubsidiaries": "<PERSON><PERSON><PERSON><PERSON>, nu gick det tokigt med sökningen av information om huvudbolaget. Vänligen kontakta vår kundtjänst.", "searchCompanyDetails.noReportAvailable": "Företaget är inte i Pålitlig Partner-tjänsten. Ingen rapport tillgänglig.", "searchCompanyDetails.zeckitDetailsHeading": "Zeckit-information", "searchCompanyDetails.zeckitWhatIs": "Vad är Zeckit?", "searchCompanyDetails.zeckitWhatIsLink": "/fi-sv/vara-tjanster/zeckit", "stateLabel.active": "Aktiv", "stateLabel.closed": "Stängd", "stateLabel.draft": "Utkast", "stateLabel.notClosed": "Inte stängd", "statusChart.viewEditEdit": "redigera", "statusChart.viewEditView": "Se/", "statusChart.visitorSliceTooltip": "Besökande företag", "statusLabel.attention": "Observera", "statusLabel.incomplete": "<PERSON>ull<PERSON><PERSON><PERSON><PERSON>", "statusLabel.investigate": "Utred", "statusLabel.ok": "OK", "statusLabel.stop": "Varning!", "submenu.companies": "Företag", "submenu.contacts": "<PERSON><PERSON><PERSON><PERSON>", "submenu.home": "Bolagsfakta", "submenu.help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submenu.projects": "Projekt", "submenu.search": "<PERSON>ö<PERSON>", "submenu.searchHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submenu.subscription": "Beställningar", "subscription.acceptCancellation": "Jag har läst ovanståande varning och jag vill säga upp Rapport PRO-tjänsten.", "subscription.archiveWarning": "Endast rapporter av Finska och Estniska bolag kan arkiveras.", "subscription.cancel": "Säg upp beställningen av Rapport PRO-tjänsten", "subscription.cancelWarning": "Du håller på att säga upp ditt företags Rapport PRO-tjänst. Observera att om du säger upp tjänsten kommer rapportarkivet att raderas permanent och användarna inom ditt företag kan inte längre använda tjänsten.<br /><br />Om du bekräftar uppsägningen av tjänsten och raderingen av arkivet upphör din tillgång till tjänsten omedelbart. Eventuella serviceavgifter som redan betalats kompenseras inte om tjänsten sägs upp.", "subscription.cancelled": "Du har sagt upp beställningen för Rapport PRO-tjänsten.", "subscription.confirmationNotDoneTooltip": "Du måste först bekräfta uppsägningen av tjänsten", "subscription.created": "Du har beställt Rapport PRO-tjänsten!", "subscription.error": "<PERSON>tt fel uppstod. <PERSON><PERSON><PERSON><PERSON><PERSON>, eller kontakta vår kundservice.", "subscription.hideDescription": "G<PERSON>m tjänstens beskrivning", "subscription.loading": "Laddar...", "subscription.marketing.first.content": "Rapport PRO är en tjänst som automatiskt arkiverar dina Pålitlig Partner-rapporter. Rapporterna innehåller information gällande beställaransvarslagen om Pålitlig Partner-företag. För att visa ditt företags rapport och för att få mera information om Pålitlig Partner-tj<PERSON><PERSON><PERSON>, l<PERSON><PERSON> mer <a href=\"https://kampanja.vastuugroup.fi/palitlig-partner-tjansten\">här</a>.", "subscription.marketing.first.header": "Vad är Rapport PRO?", "subscription.marketing.second.content": "Tjänsten sparar de Pålitlig Partner-rapporter som du laddat ner i ett arkiv som finns under flike<PERSON> Sökhistorik. Endast de rapporter som du laddat ned efter beställningen finns tillgängliga i rapportarkivet.", "subscription.marketing.second.header": "Hur fungerar tjänsten?", "subscription.marketing.third.content": "Du kan beställa tjänsten enkelt under Beställning av tjänsten. Ange vid behov en beställningsreferens, godkänn tjänstens användarvillkor och tryck på knappen \"Beställ Rapport PRO-tjänsten\".", "subscription.marketing.third.header": "<PERSON>r beställer jag tjänsten?", "subscription.noPermission.active": "Rapport PRO-tjänsten är aktiv. Du har inte behörighet att hantera beställning av tjänster. Vänligen kontakta ditt företags huvudsakliga användare för att hantera tjänsten.", "subscription.noPermission.inactive": "Du har inte behörighet att beställa tjänsten. Be företagets huvudsakliga användare att beställa tjänsten.", "subscription.order": "Beställ Rapport PRO-tjänsten", "subscription.orderReference": "Orderreferens (valfri):", "subscription.orderingHeader": "Beställning av tjänsten", "subscription.price": "Årsavgiften är 130 € + moms.", "subscription.showCancellation": "Jag vill säga upp Rapport PRO -tjänsten", "subscription.showDescription": "Visa tjänstens beskrivning", "subscription.subscriptionActive": "Ditt företag har Rapport PRO -tjänsten i använding.", "subscription.subscriptionInactive": "Rapport PRO -tj<PERSON><PERSON><PERSON> har sagts upp. <PERSON><PERSON>ll tjänsten för att arkivera rapporterna du laddar ner.", "subscription.termsNotAcceptedTooltip": "Accepterna användningsvillkoren först", "subscription.termsOfService": "Jag accepterar Rapport PRO-tj<PERSON><PERSON><PERSON> {link}", "subscription.termsOfService.link": "https://www.tilaajavastuu.fi", "subscription.termsOfService.linkText": "användingsvillkor", "subscription.warning.address": "<PERSON>tt företag saknar kontaktinformation. Beställningen kan miss<PERSON>, vänligen fyll i kontaktinformationen via {link}.", "subscription.warning.addressandbilling": "Ditt företag saknar kontaktuppgifter och faktureringsinformation. Beställningen kan missly<PERSON>, vänligen fyll i informationen via {link}.", "subscription.warning.billing": "Ditt företag saknar faktureringsuppgifter. Beställningen kan miss<PERSON>, vänligen fyll i faktureringsuppgifterna via {link}.", "subscription.warning.companyaccountlink": "Företagets informationshantering", "supplier.contract_type.consulting": "<PERSON><PERSON><PERSON>", "supplier.contract_type.consulting.description": "Exv. byggkonsult (som representerar byggherre), projektledning, säkerhet, tillsyn, revision etc.", "supplier.contract_type.contracting": "Entreprenad", "supplier.contract_type.contracting.description": "<PERSON>r an<PERSON><PERSON> för att bygga, an<PERSON><PERSON><PERSON><PERSON> och förse byggarbetsplatsen med en tjänst", "supplier.contract_type.machine_equipment_leasing": "Uthyrning av maskin eller utrustning", "supplier.contract_type.machine_equipment_leasing.description": "Endast uthyrning utan personal som använder maskinen eller utrustningen på arbetsplatsen", "supplier.contract_type.materials_and_products": "Materielleveranser (med underentreprenör)", "supplier.contract_type.materials_and_products.description": "Leverantören kommer att anlita underentreprenör för att genomföra arbete på arbetsplatsen (exv. installationsarbete) eller behöver anlita transportör för leverans", "supplier.contract_type.personnel_leasing": "Personaluthyrning", "supplier.contract_type.personnel_leasing.description": "<PERSON><PERSON><PERSON> endast personal", "supplier.contract_type.transportation": "Transporter", "supplier.contract_type.transportation.description": "Genomför enbart transporter och kommer inte utföra något arbete på byggarbetsplatsen", "supplier.contract_work_area.assembly": "<PERSON><PERSON>", "supplier.contract_work_area.casting_all_materials": "Gjutning alla material", "supplier.contract_work_area.cleaning": "<PERSON><PERSON><PERSON>", "supplier.contract_work_area.demolition": "Rivning", "supplier.contract_work_area.drywall": "<PERSON><PERSON>s", "supplier.contract_work_area.framework": "<PERSON><PERSON><PERSON>", "supplier.contract_work_area.land_works": "Markarbete", "supplier.contract_work_area.masonry_and_plastering": "Murning och putsning", "supplier.contract_work_area.other": "<PERSON><PERSON>", "supplier.contract_work_area.sanitation": "<PERSON><PERSON>", "supplier.contract_work_area.scaffolding": "Ställningsbyggnad", "supplier.submenu.add_project_client": "Lägg till projektets beställare", "supplier.submenu.add_subcontractor_form": "Lägg till leverantör under {companyName}", "supplier.submenu.contract_information_helptext": "<PERSON><PERSON><PERSON> att ändra information som ingår i föranmälan behöver en ny föranmälan skapas.", "supplier.submenu.create_new_preannouncement_form_edit": "Skapa ny föranmälan för {companyName}", "supplier.submenu.edit_project_client": "Redigera projektets beställare", "supplier.submenu.preannounce_subcontractor_form": "Föranmäl underleverantör till {companyName}", "supplier.submenu.subcontractor_form_edit": "Redigera leverantör {companyName}", "supplierBulkImport.clearAllBtn": "<PERSON><PERSON> alla", "supplierBulkImport.companiesNotSubmitted": "{numberOfCompanies, number} företag som inte lagts till", "supplierBulkImport.companiesReadyToBeSubmitted": "{numberOfCompanies, number} företag redo att läggas till", "supplierBulkImport.companiesSubmitInProgress": "Bearbetar {numberOfCompanies, number} företag av totalt {totalCompanies, number} företag", "supplierBulkImport.companiesSubmitted": "{numberOfCompanies, number} Företag som lagts till", "supplierBulkImport.companiesWillNotBeSubmitted": "{numberOfCompanies, number} företag som inte kommer att läggas till", "supplierBulkImport.enterDataFormHelpText": "Lägg till upp till 100 st organisationsnummer separerade med komma eller ny rad. Vi kommer att få företagsinformationen från officiella register och du kan granska företagslistan i nästa steg, innan du lägger till den.", "supplierBulkImport.enterDataFormHelpTextVAT": "Lägg till upp till 100 st organisationsnummer eller momsnummer separerade med komma eller ny rad. Vi kommer att få företagsinformationen från officiella register och du kan granska företagslistan i nästa steg, innan du lägger till den.", "supplierBulkImport.enterWizardStep": "Skriv in data", "supplierBulkImport.genericJobError": "<PERSON>tt fel har uppstått vid beredningen av importen", "supplierBulkImport.genericSubmitError": "Ett fel har uppstått vid inlämning av data.", "supplierBulkImport.govOrgIdTextareaLabel": "Organisationsnummer", "supplierBulkImport.govOrgIdTextareaLabelVAT": "Organisationsnummer eller momsnummer", "supplierBulkImport.reviewDataFormHelpText": "Endast den vänstra kolumnen kommer att registreras i systemet.", "supplierBulkImport.reviewWizardStep": "Granska och lämna", "supplierBulkImport.search_by_vat_info_not_supported_countries": "Sökning med momsnummer stöds inte för Sverige, Litauen, Nederländerna och Norge", "supplierBulkImport.submitWizardStep": "Stäng", "supplierBulkImport.wizardDoneStepDoneButton": "Stäng", "supplierBulkImport.wizardEnterDataNextBtn": "<PERSON><PERSON><PERSON>", "supplierBulkImport.wizardReviewCancelAndBackButton": "Avbryt och gå tillbaks till föregående steg", "supplierBulkImport.wizardReviewNextButton": "<PERSON><PERSON><PERSON>", "supplierBulkImport.wizardTitle": "Lägg till leverantörer", "supplierForm.VAT_label": "MOMS", "supplierForm.button_next": "<PERSON><PERSON><PERSON>", "supplierForm.cancel_action": "Å<PERSON><PERSON>", "supplierForm.company_bussiness_id_label": "Organisationsnummer", "supplierForm.company_bussiness_id_or_vat_label": "Organisationsnummer eller MOMS", "supplierForm.company_name_label": "Företagsnamn", "supplierForm.country_of_registration_label": "Registreringsland", "supplierForm.endDate": "Slut<PERSON><PERSON>", "supplierForm.enter_contact_email_placeholder": "Fyll i e-postadressen till leverantören", "supplierForm.field_is_required": "Obligatoriskt fält", "supplierForm.nocontractType_placeholder": "Välj kontraktstyp", "supplierForm.norole_placeholder": "V<PERSON><PERSON>j <PERSON>", "supplierForm.startDate": "Startdatum", "supplierForm.supplier_contract_type": "Kontraktstyp", "supplierForm.supplier_role": "Leverantörstyp", "supplierForm.workArea": "Yrkesområde inom entreprenaden (flera kan väljas)", "errors.validation.field.alphanumeric": "Fältet kan innehålla endast bokstäver (a-z) eller siffror", "errors.validation.field.alphanumericWithAdditionalSymbols": "<PERSON><PERSON>ltet kan innehålla endast bokstäver (a-z), si<PERSON><PERSON><PERSON> eller en av symbolerna: {additionalSymbolsJoined}", "errors.validation.field.atLeastOneAlphanumeric": "Fältet ska innehålla minst en bokstav eller siffra", "errors.validation.field.billingAddress": "Kontrollera nätfaktureringsadressen", "errors.validation.field.blacklistedEmailAddress": "{email} email addresses are not allowed(SV)", "errors.validation.field.bulgarianOrganizationNumber": "Ange Bulgariskt organisationsnummer i korrekt format (ex. *********)", "errors.validation.field.componentCustomValidation": "Fältets information innehåller fel", "errors.validation.field.componentCustomValidation.phoneNumber": "Felaktigt telefonnummer", "errors.validation.field.coordinationNumber": "Samordningsnumret är ogiltigt. (T.ex. 19840584-7290)", "errors.validation.field.croatianOrganizationNumber": "<PERSON>e minst sju tecken", "errors.validation.field.czechianOrganizationNumber": "Ange Tjeckiskt organisationsnummer i korrekt format (ex 00014915)", "errors.validation.field.danishOrganizationNumber": "Ange Danskt organisationsnummer i korrekt format (ex. 21076600)", "errors.validation.field.dateFormat": "<PERSON><PERSON> ett korrekt datum (yyyy-mm-dd)", "errors.validation.field.dateOnlyUntilToday": "<PERSON><PERSON> måste vara senast idag", "errors.validation.field.email": "E-posten har fel format", "errors.validation.field.error": "Fel i fältets information", "errors.validation.field.estonianOrganizationNumber": "Ange Estniskt organisationsnummer i korrekt format (ex 12401738)", "errors.validation.field.exactNumberOfDigits": "Ange exakt {numberOfDigits} siffror", "errors.validation.field.expiracyDateInvalid": "Datumet måste vara tidigare än Utgivningsdatum", "errors.validation.field.fTaxOrganizationNumber": "Ange F-skattenummer i korrekt format (123456-1234)", "errors.validation.field.faroeIslandsOrganizationNumber": "Ange Färöarnas organisationsnummer i korrekt format (ex. 1234)", "errors.validation.field.finnishOrganizationCode": "Ange Finskt organisationsnummer i korrekt format  (ex. 1234567-1)", "errors.validation.field.finnishPersonalNumber": "<PERSON><PERSON> rätt personnummer (T.ex. 160768-867F)", "errors.validation.field.incorrectEmailsInString": "Kontrollera e-postadressens format: {incorrectEmails}", "errors.validation.field.latvianOrganizationNumber": "Ange Lettiskt organisationsnummer i korrekt format (ex 40008012582)", "errors.validation.field.lithuanianOrganizationCode": "Ange Litauiskt organisationsnummer i korrekt format (ex. *********, 123456)", "errors.validation.field.match": "Fälten överensstämmer inte", "errors.validation.field.match.passwords": "Lösenordet stämmer inte. Var god fyll i samma lösenord två gånger.", "errors.validation.field.maxLength": "Ange max. {maxLength} tecken", "errors.validation.field.maxNumberOfEmailsIsImported": "Max {numberOfEmails} e-postadresser kan importeras på en gång. Nu importerades {currentNumberOfEmails} e-postadresser.", "errors.validation.field.minLength": "<PERSON>e minst {minLength} tecken", "errors.validation.field.mismatch": "Fälten ska ha olika information", "errors.validation.field.mismatch.passwords": "Det nya lösenordet kan inte vara likadant som det gamla.", "errors.validation.field.nationalityCode": "Nationalitetskoden måste vara exakt {codeLength} symbollängd", "errors.validation.field.netherlandsOrganizationNumber": "Ange Nederländska organisationsnummer i korrekt format (ex. 67068006)", "errors.validation.field.norwegianOrganizationNumber": "Ange Norsk organisationsnummer i korrekt format (ex. *********)", "errors.validation.field.organizationApplicationState": "Det är inte tillåtet att ändra statuset.", "errors.validation.field.password": "Lösenordet ska innehålla minst 8 tecken", "errors.validation.field.password.hasSpaces": "Lösenordet kan inte bör<PERSON> eller sluta med mellanslag.", "errors.validation.field.personNumberCountry": "Under pilotprojektet kan endast personer som innehar ett finskt personnummer använda tjänsten", "errors.validation.field.polishOrganizationNumber": "Ange Polskt organisationsnummer i korrekt format (ex. 1234567890)", "errors.validation.field.required": "Obligatorisk information", "errors.validation.field.romanianOrganizationNumber": "Ange Rumänskt organisationsnummer i korrekt format (ex. J12/0101/1990)", "errors.validation.field.scannerIdCodeIsNotValid": "<PERSON>e ett giltigt skannernummer", "errors.validation.field.slovakOrganizationNumber": "<PERSON>e minst sju tecken", "errors.validation.field.spanishOrganizationNumber": "Ange Spanskt organisationsnummer i korrekt format (ex. G79554275)", "errors.validation.field.swedishOrganizationCode": "Ange Svenskt organisationsnummer i korrekt format (ex. 123456-1234)", "errors.validation.field.swedishPersonalNumber": "Felak<PERSON><PERSON> personnummer (T.ex.19840524-7290)", "errors.validation.field.swedishFTaxNumber": "Ange F-skattenummer i korrekt format (123456-1234)", "errors.validation.form.error": "Du har inte fyllt i alla uppgifter korrekt", "registration.findCompany.SupportedCountries": "Information om vilka länder vi stödjer i ID06-systemet", "registration.findCompany.accessCompany": "Accessföretag", "registration.findCompany.back": "Tillbaka", "registration.findCompany.compLegalName": "Företagets juridiska namn", "registration.findCompany.companyDetails": "Företagsinformation", "registration.findCompany.companyInformationAlreadyAvailable": "Företagsinformation är redan tillgänglig", "registration.findCompany.companyNotFoundCreditinfo": "Ett företag inte finns hos Creditinfo. Sök efter ett annat företag eller kontakta vår", "registration.findCompany.companyNotFoundCreditsafe": "Ett företag inte finns hos Creditsafe. Sök efter ett annat företag eller kontakta vår", "registration.findCompany.companyNotFoundRegistry": "Ett företag inte finns i registret. Sök efter ett annat företag eller kontakta vår", "registration.findCompany.orgNotSufficientInfo": "Fö<PERSON>get hittades, men företagets status är inte tillgängligt i Creditsafe. Sök efter ett annat företag eller kontakta vår", "registration.findCompany.orgInfoLimit": "Det finns för närvarande ingen rapport om företaget tillgänglig från Creditsafe. Sök efter ett annat företag eller kontakta vår", "registration.findCompany.companyPhoneInRegistry": "Telefon", "registration.findCompany.continue": "Fortsätt", "registration.findCompany.correctOrgId": "rätta organisationsnummer", "registration.findCompany.countryOfRegistration": "Registreringsland", "registration.findCompany.countryOfResidence": "<PERSON><PERSON><PERSON><PERSON> hemland", "registration.findCompany.customerService": "kundservice", "registration.findCompany.enterOrganizationIdEg": "Organisationsnummer", "registration.findCompany.enterOrganizationIdEg.bg": "<PERSON><PERSON> si<PERSON> (ex. *********)", "registration.findCompany.enterOrganizationIdEg.cz": "<PERSON><PERSON> si<PERSON> (ex. 00014915)", "registration.findCompany.enterOrganizationIdEg.dk": "<PERSON><PERSON> siff<PERSON> (ex. 21076600)", "registration.findCompany.enterOrganizationIdEg.ee": "<PERSON><PERSON> siff<PERSON> (ex. 12345678)", "registration.findCompany.enterOrganizationIdEg.es": "<PERSON><PERSON> tecken (ex. G79554275)", "registration.findCompany.enterOrganizationIdEg.est.required": "Fyll i Estniskt organisationsnummer i korrekt format ({numberOfDigits} siffror)", "registration.findCompany.enterOrganizationIdEg.fi": "<PERSON><PERSON> (ex. 1234567-1)", "registration.findCompany.enterOrganizationIdEg.fo": "<PERSON><PERSON> tecken (ex. 1234)", "registration.findCompany.enterOrganizationIdEg.hr": "<PERSON><PERSON> minst sju tecken (ex. 1234567)", "registration.findCompany.enterOrganizationIdEg.lt": "Sex eller nio nummer (ex. 123456 eller *********)", "registration.findCompany.enterOrganizationIdEg.lv": "<PERSON><PERSON> siffror (ex. 40001234567)", "registration.findCompany.enterOrganizationIdEg.lv.required": "Fyll i Lettiskt organisationsnummer i korrekt format (elva siffror)", "registration.findCompany.enterOrganizationIdEg.nl": "<PERSON><PERSON> si<PERSON> (ex. 67068006)", "registration.findCompany.enterOrganizationIdEg.no": "<PERSON><PERSON> si<PERSON> (ex. *********)", "registration.findCompany.enterOrganizationIdEg.pl": "<PERSON><PERSON> siffror (ex. 1234567890)", "registration.findCompany.enterOrganizationIdEg.pl.required": "Fyll i Polskt organisationsnummer i korrekt format (KRS)", "registration.findCompany.enterOrganizationIdEg.ro": "Minst 12 tecken (ex. J12/0101/1990)", "registration.findCompany.enterOrganizationIdEg.se": "<PERSON><PERSON> tecken (ex. 123456-1234)", "registration.findCompany.enterOrganizationIdEg.sk": "<PERSON>e minst sju tecken (ex. 12345/A or 1234567)", "registration.findCompany.enterOrganizationName": "Fyll i företagets namn", "registration.findCompany.feesFilename": "ID06-Årsavgift.pdf", "registration.findCompany.findCompanyBtn": "Hämta företagsuppgifter", "registration.findCompany.findYourCompany": "Sök företag:", "registration.findCompany.forHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "registration.findCompany.formHasErrors": "Kontrollera företagsinformation", "registration.findCompany.formOfCompany": "Bolagsform", "registration.findCompany.ftax.description.link": "https://www.skatteverket.se/foretagochorganisationer/skatter/internationellt/utlandskaforetag.4.18e1b10334ebe8bc80004949.html", "registration.findCompany.ftax.description.linkText": "Information från Skatteverket om hur ett utländsk företag registrerar sig med svensk skatteregistrering", "registration.findCompany.ftax.description1": "En svensk F-skatt måste registreras av både utländska och svenska företag, vilket är ett krav för att anslutas till ID06-systemet i enlighet med ID06 Allmänna bestämmelser.", "registration.findCompany.ftax.description2": "Om ni inte är registrerad för den svenska F-skatten, ange istället **000000-0000** och ändra er registrering senast 3 månader till ett korrekt registreringsnummer för att undvika att ni utesluts från ID06-systemet.", "registration.findCompany.ftax.description3": "En svensk F-skattregistrering genomförs hos Skatteverket av det utländska bolaget som **utländsk juridisk person** eller som en **filial** av det utländska bolaget.", "registration.findCompany.ftax.inputLabel": "F-skattenummer", "registration.findCompany.ifCorrect": "Är det rätt klicka på registrera företag. Eller gå tillbaka och", "registration.findCompany.ifYouAlreadyHaveAccount": "<PERSON>m du redan har ett användarkonto, ", "registration.findCompany.loadingInfoMessage": "Hämtar data från online-register. Detta kan ta några minuter, vänligen vänta.", "registration.findCompany.orgHasNoSignatories": "Detta företag är inte berättigat att registrera sig (det finns ingen information kring firmatecknare i bolagsregistret). Sök efter ett annat företag eller kontakta vår", "registration.findCompany.orgHasNotRegisteredFTAX": "Detta företag är inte berättigat att registrera sig (företaget har inte registrerat sig för F-Skatt). Sök efter ett annat företag eller kontakta vår", "registration.findCompany.orgHasPaymentOverdue": "Detta företag har en försenad betalning. Sök efter ett annat företag eller kontakta vår", "registration.findCompany.orgNeedsToBeEnrolledFirst": "<PERSON><PERSON><PERSON> att slutföra validering av användare, måste företaget vara anslutet först", "registration.findCompany.orgNeedsToBeWhitelistedInManualId.line1": "<PERSON><PERSON><PERSON> att ansluta företaget ska du vitlistad eller verifieras som firmatecknare under <PERSON><PERSON> verifiering.", "registration.findCompany.orgNeedsToBeWhitelistedInManualId.line2": "{searchForAnotherCompanyLink} eller kontakta vår kundservice för hjälp: <EMAIL> 010-480 92 00.", "registration.findCompany.orgNeedsToBeWhitelistedInManualId.line2.searchAnotherCompany": "Sö<PERSON> efter ett annat företag", "registration.findCompany.orgNotActive": "Det verkar som om detta företag inte är aktivt ({reasonOfFailure}). Sök efter ett annat företag eller kontakta vår", "registration.findCompany.orgRegisteredOwnerNotSignatory": "Personen är inte firmatecknare av företaget {orgName} ({orgNumber})", "registration.findCompany.organizationId": "Företags ID", "registration.findCompany.organizationId.est": "Organisationsnummer", "registration.findCompany.organizationId.fin": "Organisationsnummer", "registration.findCompany.organizationId.lt": "Juridisk enhetskod", "registration.findCompany.organizationId.lv": "Organisationsnummer", "registration.findCompany.organizationId.pl": "Organisationsnummer (KRS)", "registration.findCompany.organizationId.swe": "Organisationsnummer", "registration.findCompany.organizationIdTitle": "Organisationsnummer", "registration.findCompany.organizationIdTitle.bg": "Organisationsnummer", "registration.findCompany.organizationIdTitle.cz": "Organisationsnummer", "registration.findCompany.organizationIdTitle.dk": "Organisationsnummer", "registration.findCompany.organizationIdTitle.ee": "Organisationsnummer", "registration.findCompany.organizationIdTitle.es": "Organisationsnummer", "registration.findCompany.organizationIdTitle.fi": "Organisationsnummer", "registration.findCompany.organizationIdTitle.hr": "Organisationsnummer", "registration.findCompany.organizationIdTitle.lt": "Organisationsnummer", "registration.findCompany.organizationIdTitle.lv": "Organisationsnummer", "registration.findCompany.organizationIdTitle.nl": "Organisationsnummer", "registration.findCompany.organizationIdTitle.no": "Organisationsnummer", "registration.findCompany.organizationIdTitle.pl": "Organisationsnummer", "registration.findCompany.organizationIdTitle.ro": "Organisationsnummer", "registration.findCompany.organizationIdTitle.se": "Organisationsnummer", "registration.findCompany.organizationIdTitle.sk": "Organisationsnummer", "registration.findCompany.organizationName": "Företagets namn", "registration.findCompany.regNumber": "Organisationsnummer", "registration.findCompany.registerCompany": "Registrera företag", "registration.findCompany.registryFullAddress": "Registreringsadress", "registration.findCompany.requestAccess": "<PERSON><PERSON><PERSON><PERSON>", "registration.findCompany.requestAccessSuccess": "<PERSON> fö<PERSON>gan skickades till Administratören. Vänligen invänta inbjudning via e-post.", "registration.findCompany.searchDoneInRegistry": "Företagsinformation kommer att hämtas från företagsregistret baserat på denna information.", "registration.findCompany.signInHere": "logga in här", "registration.findCompany.vatNumber": "Momsregistreringsnummer", "registration.findCompany.searchBy": "<PERSON><PERSON><PERSON> e<PERSON>", "registration.findCompany.registrationNumberRadio": "Organisationsnummer", "registration.findCompany.vatNumberRadio": "Momsregistreringsnummer", "registration.findCompany.vatNumberRadioDisabledText": "Sökning med momsnummer stöds inte för detta land", "submenu.dashboard": "HEM", "serviceRedirect.explanation.conclusion": "Vi informerar dig om förbättringar.", "serviceRedirect.explanation.mainMessage": "I första skedet kan en användare få behörighet till flera företagskonton. För tillfället är användarbehörigheten för Valvoja-tjänsten begränsade till det första kontot användaren registrerades i.", "serviceRedirect.explanation.title": "Vi skapar n<PERSON>t", "serviceRedirect.modal.cancelBtn": "Å<PERSON><PERSON>", "serviceRedirect.modal.confirmBtn": "Fortsätt till {name}", "serviceRedirect.modal.message1": "Du förflyttas till {service} tj<PERSON><PERSON>en ", "serviceRedirect.modal.message2": "<PERSON><PERSON><PERSON> tillfället kan du använda Valvoja-tjänsterna för endast ett företag.", "serviceRedirect.modal.readMore": "(läs mera)", "serviceRedirect.modal.title": "BYT FÖRETAG", "time.now": "<PERSON>u", "tooltips.password.longPasswordException": "<PERSON>m ditt lösenord har över 20 tecken, gä<PERSON> de ovannämnda begränsningarna inte.", "tooltips.password.passwordMustHave": "Lösenordet bör inn<PERSON>", "tooltips.password.passwordMustHave.atLeastOneNumber": "minst ett nummer", "tooltips.password.passwordMustHave.atLeastTenCharacters": "minst 10 tecken", "tooltips.password.passwordMustHave.upperAndLowerCaseLetters": "b<PERSON>de stora och små bokstäver", "pagination.first": "<PERSON><PERSON><PERSON>", "pagination.last": "Sista", "organization.reminder.changeInvoicingType.changeInvoicing": "Du kan övergå till elektronisk fakturering {here}", "organization.reminder.changeInvoicingType.changeInvoicingSettings": "Kontrollera faktureringsinformation", "organization.reminder.changeInvoicingType.closeReminder": "<PERSON>äng fönster", "organization.reminder.changeInvoicingType.here": "<PERSON>är", "organization.reminder.changeInvoicingType.info": "Ditt företag har hittills valt postfakturering, men vi vill gärna att ditt företag övergår till elektonisk fakturering. "}