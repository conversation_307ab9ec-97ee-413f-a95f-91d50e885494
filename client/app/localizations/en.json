{"CSAccountActivate.active_exists_error": "There is already an active Creditsafe account for this organisation. Go to Home and then back to ID06 Bolagsdeklaration to continue.", "CSAccountActivate.button": "Save password", "CSAccountActivate.creditsafeLinkTitle": "www.creditsafe.com", "CSAccountActivate.description": "Follow the instructions below to activate your Creditsafe account and to connect it to ID06 Bolagsdeklaration.", "CSAccountActivate.forgotPassword.link": "https://login.creditsafe.com/password-reset?lang=en-GB", "CSAccountActivate.forgotPassword.title": "Forgot Creditsafe password", "CSAccountActivate.goToBolService": "Go to ID06 Bolagsdeklaration", "CSAccountActivate.headerMainTitle": "Activate and connect your Creditsafe account", "CSAccountActivate.headerSuccessTitle": "Your Creditsafe account is now connected", "CSAccountActivate.invalid": "Password is not valid", "CSAccountActivate.legalNote": "Information presented in ID06 Bolagsdeklaration has been prepared by Creditsafe i Sverige AB (556514-4408), which is a credit reporting company authorized under the Credit Information Act", "CSAccountActivate.needHelp": "Need support? Please contact {email}", "CSAccountActivate.password.placeholder": "Enter Creditsafe password", "CSAccountActivate.passwordField": "Creditsafe password", "CSAccountActivate.step1.description": "Creditsafe will send an activation email to the email adress specified in your ID06 account. The email contains a link to activate the account. Click on the link and follow the instructions.", "CSAccountActivate.step1.header": "Receive activation mail from Creditsafe", "CSAccountActivate.step2.description": "Choose a password and answer Creditsafe security questions. Note the password as it must be entered in the next step.", "CSAccountActivate.step2.header": "Activate Creditsafe account", "CSAccountActivate.step3.description": "Enter your Creditsafe password below and save to complete the connection to ID06 Bolagsdeklaration.", "CSAccountActivate.step3.header": "Connect your Creditsafe-account to ID06 Bolagsdeklaration", "CSAccountActivate.success": "A Creditsafe account has been created and is now connected to ID06 Bolagsdeklaration. The account is included free of charge in the service.", "CSAccountActivate.validation.field.required": "Required field", "addProjectClient.searchMainContractorCannotAddAsProjectClient": "A main contractor can not add themselves as the project client. If you are also the project’s client, select that option and then add yourself as the main contractor once the project has been created.", "addSubcontractorForm.contactPersonSearch.emailNotValid": "Please enter a valid email address", "addSupplier.addClientCountryHelpText": "ID06 Bolagsdeklaration may only be used in projects carried out in Sweden", "addSupplier.cannot_add_error": "This company is already a supplier at this level", "addSupplier.clear_search_tooltip": "Clear search", "addSupplier.contactPersonFindAdd": "Search for contact person or add email", "addSupplier.contactPersonFindAddPaProject": "Search for contact person or add email for preannouncements", "addSupplier.contactPersonSelect": "Select supplier contact person", "addSupplier.contact_email_label": "Supplier contact person email", "addSupplier.contact_person_find_add_for_client_label": "Contact person who should be given access to the project", "addSupplier.contact_person_find_add_for_client_placeholder": "Fill in e-mail address for the project client", "addSupplier.contact_person_found": "Contact found", "addSupplier.contact_person_found_not_active": "The user is disabled and will not be able to access the service.", "addSupplier.contact_person_found_without_permission": "This user has no access to the service.", "addSupplier.contact_person_not_found": "Contact not found. This user will not have access to service.", "addSupplier.contact_person_waiting_for_supplier_confirmation": "An email will be sent to the email address once the supplier is confirmed.", "addSupplier.enter_company_business_id_label_for_add_client": "Business ID or VAT", "addSupplier.enter_company_business_id_or_vat_placeholder": "Company Business ID or VAT", "addSupplier.enter_company_business_id_placeholder": "Company Business ID", "addSupplier.enter_company_business_id_placeholder_for_add_client": "Company Business ID or VAT", "addSupplier.save_and_add_action": "Save and add another", "addSupplier.save_button": "Save", "addSupplier.search_by_vat_info_not_supported_countries": "Search by VAT number is not supported for Sweden, Lithuania, Netherlands and Norway", "addSupplier.search_cannot_add_private person": "Cannot add private person that is not a sole trader", "addSupplier.search_error": "This company does not exist in available registers.\nPlease check Business ID.", "addSupplier.search_error_check_id_or_vat": "This company does not exist in available registers. Please check Business ID or VAT", "addSupplier.search_in_progress_tooltip": "Search in progress", "add_user.addProjectUserLink": "Add project member", "archivedReports.accessTimeLabel": "Archived", "archivedReports.archiveIdLabel": "Archive number", "archivedReports.confirmDelete": "Are you sure you want to delete this archived report?", "archivedReports.confirmDeleteAll": "Are you sure you want to delete ALL archived reports for this company?", "archivedReports.confirmRemoval": "Confirm removal", "archivedReports.deleteAllReports": "Delete all archived reports", "archivedReports.deleteFailed": "Archived report deletion failed.", "archivedReports.deleteReportLabel": "Delete", "archivedReports.disabledHint": "Some of your archived reports are unavailable, because they were fetched before your current subscription began. Please contact customer service if you have any questions.", "archivedReports.disabledNoSubscriptionHint": "Your archived reports are unavailable, because your Report PRO license has been terminated. Please contact customer service if you have any questions.", "archivedReports.disabledReportTooltip": "This report is unavailable because it was fetched before your current subscription began", "archivedReports.empty": "There is no data to display", "archivedReports.endDate": "End date", "archivedReports.orderNowButton": "Subscribe now!", "archivedReports.orderNowWithoutPermission": "Ask the main user of your company about subscribing the Report PRO service.", "archivedReports.removeDialog.cancelButton": "Cancel", "archivedReports.removeDialog.removeButton": "Remove", "archivedReports.reportIconLabel": "Report", "archivedReports.salesPitch": "<p>Your company has downloaded reports {allReports, number} times, including {currentReports, number} times for the company you are looking at right now. <b>Did you remember to archive those reports?</b></p><p>By ordering the Report PRO service, reports that you have downloaded are archived automatically.</p>", "archivedReports.startDate": "Start date", "archivedReports.statusHidden": "Unavailable", "archivedReports.statusLabel": "Status", "authorizationFailed.title": "Access Denied: ID06 Bolagsdeklaration", "basepage.ID06": "ID06", "basepage.STV": "Vastuu Group Oy", "basepage.id06.page_title": "Welcome to ID06 Bolagsdeklaration", "basepage.id06.welcome_text": "ID06 Bolagsdeklaration is a company screening service provided by {provider}.", "basepage.register_new_company_body": "If you do not have a user account, please {register_new_company}.", "basepage.register_new_company_link": "register an organisation", "basepage.sign_in_button": "Please{br}Sign in", "basepage.stv.page_title": "Welcome to the Report service", "basepage.stv.welcome_text": "In the Report service, you can search for Reliable Partner reports. The service is provided by  {provider}.", "basepage.licenseNeeded.header": "Action Required: License Needed", "basepage.licenceNeeded.body": "In order to use ID06 Bolagsdeklaration, your organization must purchase a license. Visit {orderBolUrl} to learn how to order a license and unlock all the benefits of this service.", "basepage.orderBolLinkText": "Order ID06 Bolagsdeklaration", "bulkImport.companyAlreadyInProject": "Already in supply chain", "bulkImport.companyNotFound": "ID not found in registries", "bulkImport.subscriptionFailed": "Error when adding to monitoring", "bulkImport.companyPending": "Pending", "bulkImport.privatePerson": "Person, not a company", "comments.addHelpText": "Comments are only visible to the main contractor, the project's client and supervisors. Other suppliers in the project cannot see or write comments.", "comments.addPlaceholder": "Write a comment about the supplier in the project", "comments.by": "by", "comments.cancel": "Cancel", "comments.delete": "Delete", "comments.deleted": "Deleted", "comments.deleteFailed": "Failed to delete comment.", "comments.deleteWarning": "Deleted comments cannot be restored.", "comments.deleteWarningTitle": "Are you sure you want to delete this comment?", "comments.edit": "Edit", "comments.emptyList": "There are no comments for this supplier in this project.", "comments.loadingFailed": "Failed to load comments", "comments.save": "Save", "comments.saveFailed": "Could not save comment", "comments.title": "Comments", "comments.updated": "Updated", "companies.ProjectsColumn": "Projects", "companies.VATColumn": "VAT ID", "companies.businessIdColumn": "Business ID", "companies.companyDetailsLink": "Details", "companies.countryColumn": "Country", "companies.filter.clearFilter": "Clear filter", "companies.filter.compSearchPlaceholder": "Company name or number", "companies.filter.countryLabel": "Country", "companies.filter.countryTitle.ee": "Estonia", "companies.filter.countryTitle.fi": "Finland", "companies.filter.countryTitle.lt": "Lithuania", "companies.filter.countryTitle.lv": "Latvia", "companies.filter.countryTitle.pl": "Poland", "companies.filter.countryTitle.se": "Sweden", "companies.filter.queryTooShort": "Please enter at least two characters", "companies.filter.statusAny": "Any status", "companies.filter.statusNotOK": "Not OK", "companies.latestStatusColumn": "Latest status", "companies.nameColumn": "Company", "companies.nameunavailable": "Name not available", "companies.pageheader": "Search for companies among previously searched companies", "companies.reportColumn": "Report", "companies.reportLangSw.inEnglish": "In English", "companies.reportLangSw.inFinish": "In Finnish", "companies.reportLangSw.inSwedish": "In Swedish", "companies.status": "Status", "companies.statusColumn": "Status report", "companies.tip.noSubscription": "Only the search history of your company is shown below. The previously downloaded reports are not available and thus cannot be opened in the service. Order <a href=\"/#/subscription\">Report PRO</a> to archive your reports automatically in the future.", "companies.tip.subscribed": "The Report PRO service is in use. Select a company from the listing below to see the archived reports.", "companyDetails.archivedReports": "Archived reports", "companyDetails.businessIdLabel": "Business ID:", "companyDetails.certificate_publication": "Information below is not covered by the publishing certificates", "companyDetails.checkReportReminder": "Download, check and save the report", "companyDetails.comments": "Comments", "companyDetails.companyLatestReportLink": "View latest report", "companyDetails.companyNameLabel": "Company name:", "companyDetails.companyReportLink": "View complete report", "companyDetails.companyReportUpdatedAt": "Report updated: {updatedAt}", "companyDetails.companyStatusHeading": "Status:", "companyDetails.companyStatusLabel": "Status:", "companyDetails.contextual_title": "Company details", "companyDetails.countryLabel": "Country:", "companyDetails.defaultModalTitle": "Loading", "companyDetails.detailsHeading": "Company information", "companyDetails.finnishDetailsHeading": "Finnish company information", "companyDetails.loadingErrorMessage": "Error", "companyDetails.loadingFailedMessage": "Loading failed", "companyDetails.noSubscription": "This report will not be available for download in Search history since your Report PRO subscription is not active.", "companyDetails.nonArchivable": "This report will not be archived. Only Finnish and Estonian company reports are archived.", "companyDetails.notFoundMessage": "Company data is not available. Please contact our customer service if required.", "companyDetails.pdfsOnly": "NB! Regardless of the choice of language, the reports will be archived only in Finnish.", "companyDetails.relatedProject": "Related projects", "companyDetails.rpDetailsHeading": "Reliable Partner information", "companyDetails.status.terminated": "Terminated", "companyDetails.table.noDataToDisplay": "There is no data to display", "companyDetails.terminated.tooltip": "Information source: Asiakastieto", "companyDetails.vatNumberLabel": "VAT:", "companyReportMissingIconLabel": "Company report is not available", "companyStatusChangeReport.failedMessage": "Could not load report: bad link or server failure.", "companyStatusChangeReport.title": "Companies that have changed their status to {status} ({dates})", "companyStatusChangeReport.unauthorizedMessage": "Could not load report: unauthorized.", "confirmClientshipModal.bodyText1": "{project_creator} has stated that your organization is the client of this project and must have full transparency in the project.", "confirmClientshipModal.bodyText2": "If {project_client} confirms below, all credit reports will be displayed and {project_client} will appear as the requester on letters of disclosures.", "confirmClientshipModal.bodyText3": "As a client, you will have permission to add side contractors.", "confirmClientshipModal.cancelAction": "Cancel", "confirmClientshipModal.confirmAction": "Confirm", "confirmClientshipModal.headerText": "Confirm full access according to contract", "country.abw": "Aruba", "country.afg": "Afghanistan", "country.ago": "Angola", "country.aia": "<PERSON><PERSON><PERSON>", "country.ala": "Åland Islands", "country.alb": "Albania", "country.and": "Andorra", "country.are": "United Arab Emirates", "country.arg": "Argentina", "country.arm": "Armenia", "country.asm": "American Samoa", "country.ata": "Antarctica", "country.atf": "French Southern Territories", "country.atg": "Antigua and Barbuda", "country.aus": "Australia", "country.aut": "Austria", "country.aze": "Azerbaijan", "country.bdi": "Burundi", "country.bel": "Belgium", "country.ben": "Benin", "country.bes": "Bonaire, Sint Eustatius and Saba", "country.bfa": "Burkina Faso", "country.bgd": "Bangladesh", "country.bgr": "Bulgaria", "country.bhr": "Bahrain", "country.bhs": "Bahamas", "country.bih": "Bosnia and Herzegovina", "country.blm": "<PERSON>", "country.blr": "Belarus", "country.blz": "Belize", "country.bmu": "Bermuda", "country.bol": "Bolivia, Plurinational State of", "country.bra": "Brazil", "country.brb": "Barbados", "country.brn": "Brunei Darussalam", "country.btn": "Bhutan", "country.bvt": "Bouvet Island", "country.bwa": "Botswana", "country.caf": "Central African Republic", "country.can": "Canada", "country.cck": "Cocos (Keeling) Islands", "country.che": "Switzerland", "country.chl": "Chile", "country.chn": "China", "country.civ": "Côte d'Ivoire", "country.cmr": "Cameroon", "country.cod": "Congo, The Democratic Republic of the", "country.cog": "Congo", "country.cok": "Cook Islands", "country.col": "Colombia", "country.com": "Comoros", "country.cpv": "Cabo Verde", "country.cri": "Costa Rica", "country.cub": "Cuba", "country.cuw": "Curaçao", "country.cxr": "Christmas Island", "country.cym": "Cayman Islands", "country.cyp": "Cyprus", "country.cze": "Czechia", "country.deu": "Germany", "country.dji": "Djibouti", "country.dma": "Dominica", "country.dnk": "Denmark", "country.dom": "Dominican Republic", "country.dza": "Algeria", "country.ecu": "Ecuador", "country.egy": "Egypt", "country.eri": "Eritrea", "country.esh": "Western Sahara", "country.esp": "Spain", "country.est": "Estonia", "country.eth": "Ethiopia", "country.fin": "Finland", "country.fji": "Fiji", "country.flk": "Falkland Islands (Malvinas)", "country.fra": "France", "country.fro": "Faroe Islands", "country.fsm": "Micronesia, Federated States of", "country.gab": "Gabon", "country.gbr": "United Kingdom", "country.geo": "Georgia", "country.ggy": "Guernsey", "country.gha": "Ghana", "country.gib": "Gibraltar", "country.gin": "Guinea", "country.glp": "Guadeloupe", "country.gmb": "Gambia", "country.gnb": "Guinea-Bissau", "country.gnq": "Equatorial Guinea", "country.grc": "Greece", "country.grd": "Grenada", "country.grl": "Greenland", "country.gtm": "Guatemala", "country.guf": "French Guiana", "country.gum": "Guam", "country.guy": "Guyana", "country.hkg": "Hong Kong", "country.hmd": "Heard Island and McDonald Islands", "country.hnd": "Honduras", "country.hrv": "Croatia", "country.hti": "Haiti", "country.hun": "Hungary", "country.idn": "Indonesia", "country.imn": "Isle of Man", "country.ind": "India", "country.iot": "British Indian Ocean Territory", "country.irl": "Ireland", "country.irn": "Iran, Islamic Republic of", "country.irq": "Iraq", "country.isl": "Iceland", "country.isr": "Israel", "country.ita": "Italy", "country.jam": "Jamaica", "country.jey": "Jersey", "country.jor": "Jordan", "country.jpn": "Japan", "country.kaz": "Kazakhstan", "country.ken": "Kenya", "country.kgz": "Kyrgyzstan", "country.khm": "Cambodia", "country.kir": "Kiribati", "country.kna": "Saint Kitts and Nevis", "country.kor": "Korea, Republic of", "country.kwt": "Kuwait", "country.lao": "Lao People's Democratic Republic", "country.lbn": "Lebanon", "country.lbr": "Liberia", "country.lby": "Libya", "country.lca": "Saint Lucia", "country.lie": "Liechtenstein", "country.lka": "Sri Lanka", "country.lso": "Lesotho", "country.ltu": "Lithuania", "country.lux": "Luxembourg", "country.lva": "Latvia", "country.mac": "Macao", "country.maf": "<PERSON> (French part)", "country.mar": "Morocco", "country.mco": "Monaco", "country.mda": "Moldova, Republic of", "country.mdg": "Madagascar", "country.mdv": "Maldives", "country.mex": "Mexico", "country.mhl": "Marshall Islands", "country.mkd": "Macedonia, Republic of", "country.mli": "Mali", "country.mlt": "Malta", "country.mmr": "Myanmar", "country.mne": "Montenegro", "country.mng": "Mongolia", "country.mnp": "Northern Mariana Islands", "country.moz": "Mozambique", "country.mrt": "Mauritania", "country.msr": "Montserrat", "country.mtq": "Martinique", "country.mus": "Mauritius", "country.mwi": "Malawi", "country.mys": "Malaysia", "country.myt": "Mayotte", "country.nam": "Namibia", "country.ncl": "New Caledonia", "country.ner": "Niger", "country.nfk": "Norfolk Island", "country.nga": "Nigeria", "country.nic": "Nicaragua", "country.niu": "Niue", "country.nld": "Netherlands", "country.nor": "Norway", "country.npl": "Nepal", "country.nru": "Nauru", "country.nzl": "New Zealand", "country.omn": "Oman", "country.pak": "Pakistan", "country.pan": "Panama", "country.pcn": "Pitcairn", "country.per": "Peru", "country.phl": "Philippines", "country.plw": "<PERSON><PERSON>", "country.png": "Papua New Guinea", "country.pol": "Poland", "country.pri": "Puerto Rico", "country.prk": "Korea, Democratic People's Republic of", "country.prt": "Portugal", "country.pry": "Paraguay", "country.pse": "Palestine, State of", "country.pyf": "French Polynesia", "country.qat": "Qatar", "country.registration.label": "Country of registration", "country.reu": "Réunion", "country.rou": "Romania", "country.rus": "Russian Federation", "country.rwa": "Rwanda", "country.sau": "Saudi Arabia", "country.sdn": "Sudan", "country.sen": "Senegal", "country.sgp": "Singapore", "country.sgs": "South Georgia and the South Sandwich Islands", "country.shn": "Saint Helena, Ascension and Tristan <PERSON>ha", "country.sjm": "Svalbard and <PERSON>", "country.slb": "Solomon Islands", "country.sle": "Sierra Leone", "country.slv": "El Salvador", "country.smr": "San Marino", "country.som": "Somalia", "country.spm": "Saint Pierre and Miquelon", "country.srb": "Serbia", "country.ssd": "South Sudan", "country.stp": "Sao Tome and Principe", "country.sur": "Suriname", "country.svk": "Slovakia", "country.svn": "Slovenia", "country.swe": "Sweden", "country.swz": "Swaziland", "country.sxm": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "country.syc": "Seychelles", "country.syr": "Syrian Arab Republic", "country.tca": "Turks and Caicos Islands", "country.tcd": "Chad", "country.tgo": "Togo", "country.tha": "Thailand", "country.tjk": "Tajikistan", "country.tkl": "Tokelau", "country.tkm": "Turkmenistan", "country.tls": "Timor-Leste", "country.ton": "Tonga", "country.tto": "Trinidad and Tobago", "country.tun": "Tunisia", "country.tur": "Turkey", "country.tuv": "Tuvalu", "country.twn": "Taiwan, Province of China", "country.tza": "Tanzania, United Republic of", "country.uga": "Uganda", "country.ukr": "Ukraine", "country.umi": "United States Minor Outlying Islands", "country.ury": "Uruguay", "country.usa": "United States", "country.uzb": "Uzbekistan", "country.vat": "Holy See (Vatican City State)", "country.vct": "Saint Vincent and the Grenadines", "country.ven": "Venezuela, Bolivarian Republic of", "country.vgb": "Virgin Islands, British", "country.vir": "Virgin Islands, U.S.", "country.vnm": "Viet Nam", "country.vut": "Vanuatu", "country.wlf": "Wallis and Futuna", "country.wsm": "Samoa", "country.yem": "Yemen", "country.zaf": "South Africa", "country.zmb": "Zambia", "country.zwe": "Zimbabwe", "createCSAccount.bodyMainText": "In order for you to be able to use ID06 Bolagsdeklaration, ID06 must create a Creditsafe account that belongs to you. The account can only be used for credit monitoring in ID06 Bolagsdeklaration. The account is included free of charge in the service.", "createCSAccount.changeUserDetailsLink": "Change user details", "createCSAccount.createCSAccountButton": "Create Creditsafe account", "createCSAccount.errorAccountAlreadyExists": "There is already an active Creditsafe account for this organisation. Go to Home and then back to ID06 Bolagsdeklaration to continue.", "createCSAccount.errorAccountPendingExists": "You have already created a Creditsafe account for this organisation. Go to Home and then back to ID06 Bolagsdeklaration to continue.", "createCSAccount.errorAutoaccount": "An error occurred while creating your Creditsafe account. Contact {emailLink} and we will help you further.", "createCSAccount.errorForeignCompany": "The company must register a Swedish tax number with ID06 in order to create a Creditsafe account. Contact an administrator for your company account and they can register the information. If you do not know who the administrator is, contact {emailLink} and we will help you further.", "createCSAccount.errorMessageFromCS": "Error message from Creditsafe:", "createCSAccount.headerMainTitle": "Create Creditsafe account", "createCSAccount.headerUserDetails": "USER DETAILS", "createCSAccount.userEmailAddress": "Email address:", "createCSAccount.userPhoneNumber": "Phone number:", "customerSupport.TnC": "GDPR and agreements", "customerSupport.TnCDesc": "ID06 General provisions and specific terms and conditions for ID06 Bolagsdeklaration.", "customerSupport.contactUsTitle": "CONTACT US", "customerSupport.customerSupportTitle": "CUSTOMER SUPPORT", "customerSupport.faq": "FAQ", "customerSupport.faqDesc": "Frequently asked questions about ID06 Bolagsdeklaration.", "customerSupport.legalTitle": "TERMS AND CONDITIONS", "customerSupport.userGuidePdf": "ID06 Bolagsdeklaration Användarguide.pdf", "customerSupport.userGuidePdfDesc": "Descriptions and instructions for how to use ID06 Bolagsdeklaration (only available in Swedish).", "customerSupport.userGuidesTitle": "USER GUIDES", "customerSupport.whatIsBolagsdeklaration": "ID06 Bolagsdeklaration is a company screening service provided by {id06Link}.", "details.closeDetails": "Close view", "idCardIconLabel": "Entrance Period", "loading.empty": "No items found.", "loading.failed": "Loading failed.", "loading.initial": "Please enter a query", "loading.loading": "Loading...", "openCompanyDetailsIconLabel": "Open company details", "openCompnyReportIconLabel": "Open company report", "paStatusLabel.confirmed": "Confirmed", "paStatusLabel.created": "Register", "paStatusLabel.inReview": "In review", "paStatusLabel.pa_created": "Created", "paStatusLabel.registered": "Review", "paStatusLabel.rejected": "Rejected", "paStatusLabel.waitingForRegistration": "Waiting for registration", "preannouncementDetails.cant_load_pa": "Can't load preannouncement", "preannouncementDetails.contactinfo_header": "Contact information", "preannouncementDetails.contracLongerThanSixMonths": "Any contract period longer than 6 months requires a permanent establishment", "preannouncementDetails.emailhelptext": "Email address will be used for sending preannouncement notifications.", "preannouncementDetails.foreman_header": "<PERSON><PERSON>", "preannouncementDetails.informant_customer_header": "Informant customer", "preannouncementDetails.informant_header": "Informant supplier", "preannouncementDetails.no": "No", "preannouncementDetails.paCanConfirmOverride": "Report could not be downloaded", "preannouncementDetails.paCanConfirmWait": "Report is downloading…", "preannouncementDetails.paCanConfirmWaitHelpText": "The report is downloaded in the background. Close the window and try again in a few minutes.", "preannouncementDetails.pa_active_cards": "Active cards", "preannouncementDetails.pa_assigned_to": "Assigned to", "preannouncementDetails.pa_assigned_to_date": "Assigned to date", "preannouncementDetails.pa_collective_agreement": "Is the company a member of an employers' organization or has the company signed a suspension agreement?", "preannouncementDetails.pa_collective_agreement_name": "Enter the name of the agreement", "preannouncementDetails.pa_collective_agreement_placeholder": "Example: construction machinery agreement", "preannouncementDetails.pa_company_business_id": "Business ID", "preannouncementDetails.pa_company_information_header": "Company information", "preannouncementDetails.pa_company_name": "Company name", "preannouncementDetails.pa_company_status": "Status", "preannouncementDetails.pa_company_status_cannot_be_determined": "Status cannot be determined because the report is\n                  missing. Check that there is support for monitoring the country\n                  of registration according to the {faq_link}. If there is support and the\n                  report is still missing, contact {mail_link}", "preannouncementDetails.pa_company_status_faq_link_text": "FAQ", "preannouncementDetails.pa_confirm": "Confirm", "preannouncementDetails.pa_confirm_by_buyer": "Confirm and submit", "preannouncementDetails.pa_confirmed_date": "Confirmation date", "preannouncementDetails.pa_confirmed_name": "Confirmed by", "preannouncementDetails.pa_construction_client": "Construction client", "preannouncementDetails.pa_contact_info_email": "Primary email address", "preannouncementDetails.pa_contact_info_email_confirm": "Confirm email address", "preannouncementDetails.pa_contract_information_header": "Contract information", "preannouncementDetails.pa_contract_type": "Contract type", "preannouncementDetails.pa_contractor": "Contractor", "preannouncementDetails.pa_country_of_regiser": "Country of registration", "preannouncementDetails.pa_customer": "Customer", "preannouncementDetails.pa_email_not_valid": "Please enter a valid email address.", "preannouncementDetails.pa_end_date": "End date", "preannouncementDetails.pa_foreman_email": "Email", "preannouncementDetails.pa_foreman_first_name": "First name", "preannouncementDetails.pa_foreman_last_name": "Last name", "preannouncementDetails.pa_foreman_on_site": "Will there be a foreman on site?", "preannouncementDetails.pa_foreman_phone_number": "Phone number", "preannouncementDetails.pa_informant": "Name", "preannouncementDetails.pa_informant_mail": "Email address", "preannouncementDetails.pa_informant_phone": "Phone number", "preannouncementDetails.pa_intro": "Use the form below to register information about your company and your agreement in the project. Also check the contract information provided by your customer. The information stated below will be reviewed by your customer, who will then submit the preannouncement. All companies above you in the supply chain will then review the data. When the preannouncement is confirmed, you will receive a notification.", "preannouncementDetails.pa_main_contractor": "Main contractor", "preannouncementDetails.pa_one_man_business": "Is the company a one-man business?", "preannouncementDetails.pa_one_man_business_helptext": "A one-man business is a company with one employee or a sole trader without employees.", "preannouncementDetails.pa_perm_place": "Is there a basis for the company to have a permanent establishment when the work begins?", "preannouncementDetails.pa_professional_work": "Professional work area", "preannouncementDetails.pa_project_info_header": "Project information", "preannouncementDetails.pa_project_name": "Project name", "preannouncementDetails.pa_received": "Received", "preannouncementDetails.pa_register": "Register", "preannouncementDetails.pa_register_close": "Close", "preannouncementDetails.pa_register_error": "Not all required fields have been filled", "preannouncementDetails.pa_reject": "Reject", "preannouncementDetails.pa_rejected_date": "Rejected date", "preannouncementDetails.pa_rejected_name": "Rejected by", "preannouncementDetails.pa_reviewer": "Reviewer", "preannouncementDetails.pa_start_date": "Start date", "preannouncementDetails.pa_status": "Status", "preannouncementDetails.pa_submitted_date": "Submitted date", "preannouncementDetails.pa_vat": "VAT", "preannouncementDetails.preannouncement_header": "Preannouncement", "preannouncementDetails.preannouncement_header_created": "Register preannouncement for ", "preannouncementDetails.preannouncement_header_registered": "Preannouncement for ", "preannouncementDetails.swTaxNumberHelptext": "Information registered by the supplier in the ID06 system.", "preannouncementDetails.swedishTaxNumber": "Swedish tax number", "preannouncementDetails.yes": "Yes", "project.editProjectButtonInTitle": "Edit project", "project.editProjectPanelTitle": "Edit project", "project.newProjectPanelTitle": "New project", "project.stateActive": "Active", "project.stateClosed": "Closed", "project.stateDraft": "Draft", "project.statuses.failed": "Can't load project statuses.", "project.statuses.nonPaedTotal": "Non-preannounced suppliers", "project.statuses.supplierTotal": "Suppliers in total", "project.statuses.visitorTotal": "Visiting companies", "projectDetails.addFirstSupplier": "Add the first supplier", "projectDetails.bulkSupplierAdd": "Add multiple suppliers", "projectDetails.bulkSupplierAddEmptyProject": "Add multiple suppliers", "projectDetails.detailsHeading": "Project information", "projectDetails.emptyProjectMessage": "There are no suppliers added to this project.", "projectDetails.endDateLabel": "End date:", "projectDetails.inlineAddSupplierFormTitle": "Add suppliers to the project", "projectDetails.internalIdLabel": "Internal project ID:", "projectDetails.loadingFailedMessage": "Loading failed", "projectDetails.projectMembers": "Project members", "projectDetails.projectMembers.noBolPermissions": "No access to service", "projectDetails.projectMembers.pendingInvitation": "Pending invitation", "projectDetails.relatedSupplier": "Project suppliers", "projectDetails.singleSupplierAdd": "Add supplier", "projectDetails.singleSupplierPreannounce": "Preannounce subsupplier", "projectDetails.startDateLabel": "Start date:", "projectDetails.stateLabel": "State:", "projectDetails.statusHeading": "Status", "projectDetails.supplyChainHeading": "Supply chain", "projectDetails.taxIdLabel": "Construction site ID:", "projectDetails.viewProjectReport": "View project report", "projectForm.addProjectClientLink": "Add project client", "projectForm.addedClientCanView": "The project's client should have full transparency in the project", "projectForm.addedClientCanViewInfoLabel": "If the project's client has been granted full transparency, it cannot be revoked later.", "projectForm.addedClientCannotView": "The project's client can not see and will not have access to the project", "projectForm.cancelAction": "Cancel", "projectForm.companyNameIsLabel": "is:", "projectForm.compulsoryNote": "marks a compulsory field", "projectForm.endDate": "End date", "projectForm.infoHeading": "Project information", "projectForm.internalProjectIdLabel": "Internal Project ID", "projectForm.mainContractorLabel": "Main contractor", "projectForm.noTaxIdLabel": "This project does not require a Construction Site ID", "projectForm.paCheckboxInfoLabel": "It is not possible to change projects afterwards to use ID06 Digital preannoucement.", "projectForm.projectClientLabel": "Project client", "projectForm.projectNameDescription": "Project name should be clear and descriptive of a project.", "projectForm.projectNameLabel": "Project name", "projectForm.requirePAcheckboxLabel": "This project uses ID06 Digital preannouncement", "projectForm.saveButton": "Save", "projectForm.startDate": "Start date", "projectForm.stateLabel": "State", "projectForm.taxIdDescription": "Internal Project ID (chosen freely) or Construction site ID (provided by the tax office) is required before the project can be made Active.", "projectForm.taxIdLabel": "Construction site ID", "projectReport.attentionSymbolDescription": "This company should provide additional information regarding the reported deviations or some information should be noted", "projectReport.enclosure": "Information presented in ID06 Bolagsdeklaration has been prepared by Creditsafe i Sverige AB (556514-4408), which is a credit reporting company authorized under the Credit Information Act.", "projectReport.failedOrNoPermissions": "Project report loading failed or you do not have permission to view the report.", "projectReport.incompleteSymbolDescription": "Information is missing or we are waiting for confirmation from Creditsafe", "projectReport.informationCoverage": "Information below is not covered by the publishing certificates.", "projectReport.investigateSymbolDescription": "This company does not fulfill the criteria and needs to be investigated", "projectReport.latestRegisteredChange": "Latest registered change: ", "projectReport.noReportRetrieved": "Status cannot be determined as the report has not been retrieved yet.", "projectReport.okSymbolDescription": "This company fulfills the criteria in ID06 Bolagsdeklaration", "projectReport.projectSuppliersDetails": "Detailed information about suppliers", "projectReport.projectSupplyChain": "Project supply chain", "projectReport.reportDescription": "Report description", "projectReport.reportSummary": "Summary", "projectReport.stopSymbolDescription": "VERIFY THE COMPANY!", "projectReport.supplierCriteriaForForeignCompanies": "Supplier criteria for foreign companies", "projectReport.supplierCriteriaForForeignCompanies.vatRegistration": "VAT registration: Registered to local VAT register", "projectReport.supplierCriteriaForSwedishCompanies": "Supplier criteria for Swedish companies", "projectReport.supplierCriteriaForSwedishCompanies.auditor": "Control of requirement for auditor: Has auditor", "projectReport.supplierCriteriaForSwedishCompanies.companyForm": "Company/Organizational form for Swedish companies: AB, EF or HB/KB", "projectReport.supplierCriteriaForSwedishCompanies.companyStatus": "Company status: No status impact noted", "projectReport.supplierCriteriaForSwedishCompanies.companyStatusActive": "Company status: Active", "projectReport.supplierCriteriaForSwedishCompanies.debtBalanceCompanyOrPrivate": "Debt balance company or private individual at Swedish Enforcement Authority: The company has no or small debts regarding company or private individuals", "projectReport.supplierCriteriaForSwedishCompanies.debtBalanceTaxesAndFees": "Debt balance taxes and fees at Swedish Enforcement Authority: The company has no tax debts or a smaller debt balance", "projectReport.supplierCriteriaForSwedishCompanies.fTaxControl": "F-tax control: Holds F-tax or FA-tax (sole trader)", "projectReport.supplierCriteriaForSwedishCompanies.payrollRegister": "Swedish payroll register: Registered in the payroll register", "projectReport.supplierCriteriaForSwedishCompanies.rating": "Rating: Moderate risk to very low risk for insolvency within 12 months", "projectReport.supplierCriteriaForSwedishCompanies.representativeCheck": "Representative check Swedish AB (LLC): No negative information found", "projectReport.supplierCriteriaForSwedishCompanies.vatRegister": "Swedish VAT register: Registered in the Swedish VAT-register or the organizational form is not normally registered for VAT", "projectReport.symbols": "Symbols", "projectSuppliers.removeDialog.removableSupplier": "Will be removed:", "projectSuppliers.removeDialog.removableSuppliers": "Will be removed ADDITIONALLY:", "projectTree.SupplierRoleDowngradeDialogComponent.heading": "The supplier cannot have role {previous_role} at this level of supply chain. By performing this move the role will be changed to supplier and privileges removed.", "projectTree.clientRole": "Project client", "projectTree.edit.exit": "Exit without saving", "projectTree.edit.save": "Save", "projectTree.edit.saveErrros": "Encountered an error when saving changes.", "projectTree.noRegisteredNonPaedSuppliers": "There are no non-preannounced suppliers", "projectTree.noRegisteredVisitors": "There are no registered visiting companies", "projectTree.noUnlinkedSuppliers": "There are no unlinked suppliers", "projectTree.nonPaedSuppliers": "Non-preannounced suppliers", "projectTree.removeDialog.removeButton": "Remove", "projectTree.removeDialog.removeSingleSupplier": "This action will remove supplier from the tree and cannot be undone (however you may add this supplier again).", "projectTree.removeDialog.removeSupplierWithChildren": "This action will remove supplier together with its {childrenCount} {childrenCount, plural, one {subsupplier} other {subsuppliers}} from the tree and cannot be undone.", "projectTree.removeDialog.title": "Remove supplier {name}", "projectTree.supplierRoleDowngrade.title": "Changing supplier role", "projectTree.supplierRoleDowngradeDialog.confirmAction": "OK", "projectTree.title": "Project:", "projectTree.unlinkedSuppliers": "Unlinked Suppliers", "projectTree.visitors": "Visiting companies", "projectUsers.actionsLabel": "Actions", "projectUsers.cancelAction": "Cancel", "projectUsers.changeSuccess": "Project members have been updated successfully.", "projectUsers.companyLabel": "Company", "projectUsers.deletDialog.contentMessage": "This action will remove user {email} with role {role} representing company {company} from the project. You may add this user to the project again.", "projectUsers.deletDialog.titleMessage": "Remove user {email} with role {role}?", "projectUsers.deleteUserLink": "Delete", "projectUsers.notifyLabel": "Notify", "projectUsers.removeUserLink": "Remove", "projectUsers.roleLabel": "Role", "projectUsers.saveButton": "Save", "projectUsers.userInfoLabel": "User information", "projectUsers.usersListEmpty": "List is empty", "projectUsers.usersListSelectPlaceholder": "Select user", "projects.addNewProject": "Add new project", "projects.addNewProjectCompact": "Add", "projects.contractorsColumn": "Suppliers", "projects.filter.searchLabel": "Search", "projects.filter.searchPlaceholder": "Project name or ID", "projects.filter.stateLabel": "State", "projects.filter.statusLabel": "Status", "projects.nameColumn": "Project", "projects.node.addSingleSupplier": "Add new supplier", "projects.node.beingMoved": "Selected for move", "projects.node.visitDate": "Visit date", "projects.node.visitPeriod": "Visiting period", "projects.node.print": "Print", "projects.node.undoText": "Undo move", "projects.nodemenu.addSupplier": "Add supplier", "projects.nodemenu.cancelMove": "<PERSON><PERSON> move", "projects.nodemenu.companyDetails": "View company", "projects.nodemenu.createNewPreannouncement": "Create new preannouncement", "projects.nodemenu.editSupplier": "Edit supplier", "projects.nodemenu.moveSupplier": "Move supplier", "projects.nodemenu.openPreannouncement": "Open preannouncement", "projects.nodemenu.placeSupplier": "Place supplier", "projects.nodemenu.preannounceSupplier": "Preannounce subsupplier", "projects.nodemenu.printNoStatuses": "Print without statuses", "projects.nodemenu.removeSupplier": "Remove supplier", "projects.nodemenu.rootAddSupplier": "Add supplier", "projects.nodemenu.viewReport": "View report", "projects.projectUsers.roleLabel": "Define role", "projects.projectUsers.searchLabel": "Find user", "projects.stateColumn": "State", "projects.statusColumn": "Status", "relatedProjects.endDateLabel": "End date", "relatedProjects.loadingFailed": "Loading failed", "relatedProjects.preannouncement": "Preannouncement", "relatedProjects.projectLabel": "Project", "relatedProjects.startDateLabel": "Start date", "relatedProjects.statusLabel": "Status", "relatedSuppliers.buyerLabel": "Buyer", "relatedSuppliers.contactPerson": "Contact Person", "relatedSuppliers.reportLabel": "Report", "relatedSuppliers.supplierLabel": "Supplier", "relatedSuppliers.commentsLabel": "Comments", "relatedSuppliers.unreadCommentAriaLabel": "Unread comments", "relatedSuppliers.readCommentAriaLabel": "Read comments", "roles.project.leader": "Leader", "roles.project.leader.description": "Can view project and manage users from own organisation", "roles.project.manager": "Manager", "roles.project.manager.description": "Can view project and manage users from own organisation", "roles.project.member": "Member", "roles.project.member.description": "Can view project", "roles.supplier.main_contractor": "Main contractor", "roles.supplier.main_contractor.description": "Read and edit access rights to the entire project supply chain", "roles.supplier.supervisor": "Supervisor", "roles.supplier.supervisor.description": "Read and edit access rights to the entire project supply chain", "roles.supplier.supplier": "Supplier", "roles.supplier.supplier.description": "No rights granted", "search.countryColumn": "Country", "search.message.noSubscription": "You are currently using the free version of the Report service which enables you to search for Reliable Partner reports. Subscribe to Report PRO to archive your reports automatically!", "search.message.subscribeButton": "Subscribe to Report PRO", "search.nameColumn": "Company", "search.pageheader": "Search for Reliable Partner reports", "search.ralaColumn": "RALA", "search.row.terminated": "Terminated", "search.row.terminated.tooltip": "Information source: Bisnode", "search.rpColumn": "Reliable Partner", "search.rpColumn.short": "RP", "search.tip.subscribed": "The Report PRO service is in use and the reports you download are archived automatically. The report archive is shown in the <a href=\"/#/companies\">Search history</a> tab.", "search.tooManyResults": "Too many results. Please narrow your search, if the company you searched is not shown in the results", "searchCompanyDetails.gotoParentCompany": "Go to parent company", "searchCompanyDetails.hasSubsidiaries": "The form of this company is a foreign organisation or branch. You will find the Reliable Partner Report in the information of the foreign parent company.", "searchCompanyDetails.multipleSubsidiaries": "Whoops, looks like something went wrong when fetching the parent company information. Please contact our customer service.", "searchCompanyDetails.noReportAvailable": "This company is not in the Reliable Partner service. No report available.", "searchCompanyDetails.zeckitDetailsHeading": "Zeckit information", "searchCompanyDetails.zeckitWhatIs": "What is <PERSON><PERSON><PERSON>?", "searchCompanyDetails.zeckitWhatIsLink": "/fi-en/our-services/zeckit", "stateLabel.active": "Active", "stateLabel.closed": "Closed", "stateLabel.draft": "Draft", "stateLabel.notClosed": "Not closed", "statusChart.viewEditEdit": "edit", "statusChart.viewEditView": "View/", "statusChart.visitorSliceTooltip": "Visiting company", "statusLabel.attention": "Attention", "statusLabel.incomplete": "Incomplete", "statusLabel.investigate": "Investigate", "statusLabel.ok": "OK", "statusLabel.stop": "Warning!", "submenu.companies": "Companies", "submenu.contacts": "Contacts", "submenu.home": "Bolagsfakta", "submenu.help": "Help", "submenu.projects": "Projects", "submenu.search": "Search", "submenu.searchHistory": "Search history", "submenu.subscription": "Subscription", "subscription.acceptCancellation": "I have read the warning above and wish to terminate the Report PRO service", "subscription.archiveWarning": "Only reports of Finnish and Estonian companies can be archived.", "subscription.cancel": "Terminate the Report PRO service subscription", "subscription.cancelWarning": "You are about to terminate your company’s Report PRO service. Please note that terminating the service will permanently delete the report archive and prevent any further use of the service within your company. <br /><br />If you confirm the termination of the service and the deletion of the archive, the right to use the service will end immediately. Any paid service fees will not be refunded if the service is terminated.", "subscription.cancelled": "You have successfully terminated your subscription to the Report PRO service.", "subscription.confirmationNotDoneTooltip": "You have to confirm the termination first", "subscription.created": "You have successfully subscribed to the service!", "subscription.error": "An error occurred. Please try again or contact customer service.", "subscription.hideDescription": "Hide service description", "subscription.loading": "Processing...", "subscription.marketing.first.content": "Report PRO is a service that archives downloaded Reliable Partner reports automatically. The reports contain the contractor’s liability data of Reliable Partner companies. In order to have your company's report available and more information about the Reliable Partner service, read more <a href=\"https://kampanja.vastuugroup.fi/reliable-partner-service\">here</a>.", "subscription.marketing.first.header": "What is Report PRO?", "subscription.marketing.second.content": "The service saves downloaded Reliable Partner reports into a report archive found under ‘Search history’. Only reports that are downloaded after you place your order will be available for viewing in the report archive.", "subscription.marketing.second.header": "How does the service work?", "subscription.marketing.third.content": "To subscribe to the service, simply click on ‘Subscribe Report PRO service’ below. If required, enter a subscription reference, accept the terms and conditions of use, and click on ‘Subscribe Report PRO service’.", "subscription.marketing.third.header": "How do I subscribe to the service?", "subscription.noPermission.active": "Report PRO service is active. You do not have permission to manage the subscription. Please contact the main user of your company in order to manage the subscription.", "subscription.noPermission.inactive": "You do not have permission to order the service. Please contact the main user of your company in order to order the service.", "subscription.order": "Subscribe to Report PRO service", "subscription.orderReference": "Order reference (optional):", "subscription.orderingHeader": "Service ordering", "subscription.price": "The annual fee is 130 € + VAT.", "subscription.showCancellation": "I wish to terminate my Report PRO subscription", "subscription.showDescription": "Show service description", "subscription.subscriptionActive": "Your company has an active Report PRO service subscription.", "subscription.subscriptionInactive": "Report PRO service subscription has been terminated. Subscribe now to archive the downloaded reports.", "subscription.termsNotAcceptedTooltip": "You have to accept the terms of use first", "subscription.termsOfService": "I accept {link} of the Report PRO service", "subscription.termsOfService.link": "https://www.tilaajavastuu.fi", "subscription.termsOfService.linkText": "Terms of use", "subscription.warning.address": "Your company doesn't have a contact address. Ordering the subscription might fail, so please fill in the contact address {link}.", "subscription.warning.addressandbilling": "Your company doesn't have a contact address and invoicing details. Ordering the subscription might fail, so please fill in the information {link}.", "subscription.warning.billing": "Your company doesn't have invoicing details. Ordering the subscription might fail, so please fill in the invoicing details {link}.", "subscription.warning.companyaccountlink": "in the company account", "supplier.contract_type.consulting": "Consulting", "supplier.contract_type.consulting.description": "E.g. constructor (representing the construction client), project management, safety, supervision, audit etc.", "supplier.contract_type.contracting": "Contracting", "supplier.contract_type.contracting.description": "Have responsibility for building, constructing or providing a service to the construction site", "supplier.contract_type.machine_equipment_leasing": "Machine or equipment leasing", "supplier.contract_type.machine_equipment_leasing.description": "Leasing without personnel using the machinery or equipment on site", "supplier.contract_type.materials_and_products": "Materials and products (with subcontractors)", "supplier.contract_type.materials_and_products.description": "Supplier will be hiring subcontractor to perform work on site (e.g. installation work) or need to hire a transportation firm for delivery", "supplier.contract_type.personnel_leasing": "Personnel leasing", "supplier.contract_type.personnel_leasing.description": "Only supplies personnel", "supplier.contract_type.transportation": "Transportation", "supplier.contract_type.transportation.description": "Only provides transportation and will not perform work on site", "supplier.contract_work_area.assembly": "Assembly", "supplier.contract_work_area.casting_all_materials": "Casting all materials", "supplier.contract_work_area.cleaning": "Cleaning", "supplier.contract_work_area.demolition": "Demolition", "supplier.contract_work_area.drywall": "Drywall", "supplier.contract_work_area.framework": "Framework", "supplier.contract_work_area.land_works": "Land works", "supplier.contract_work_area.masonry_and_plastering": "Masonry and plastering", "supplier.contract_work_area.other": "Other", "supplier.contract_work_area.sanitation": "Sanitation", "supplier.contract_work_area.scaffolding": "Scaffolding", "supplier.submenu.add_project_client": "Add project client", "supplier.submenu.add_subcontractor_form": "Add supplier under {companyName}", "supplier.submenu.contract_information_helptext": "To change information included in the preannouncement, a new preannouncement needs to be created.", "supplier.submenu.create_new_preannouncement_form_edit": "Create new preannouncement for {companyName}", "supplier.submenu.edit_project_client": "Edit project client", "supplier.submenu.preannounce_subcontractor_form": "Preannounce subsupplier to {companyName}", "supplier.submenu.subcontractor_form_edit": "Edit supplier {companyName}", "supplierBulkImport.clearAllBtn": "Clear all", "supplierBulkImport.companiesNotSubmitted": "{numberOfCompanies, number} companies not submitted", "supplierBulkImport.companiesReadyToBeSubmitted": "{numberOfCompanies, number} companies ready to be submitted", "supplierBulkImport.companiesSubmitInProgress": "Processing {numberOfCompanies, number} of {totalCompanies, number} total companies", "supplierBulkImport.companiesSubmitted": "{numberOfCompanies, number} companies submitted", "supplierBulkImport.companiesWillNotBeSubmitted": "{numberOfCompanies, number} companies will not be submitted", "supplierBulkImport.enterDataFormHelpText": "Enter up to 100 business IDs separate by commas or newlines. We will get the company information from official registries and you will review the company list in the next step before submitting", "supplierBulkImport.enterDataFormHelpTextVAT": "Enter up to 100 business IDs or VAT numbers separate by commas or newlines. We will get the company information from official registries and you will review the company list in the next step before submitting", "supplierBulkImport.enterWizardStep": "Enter data", "supplierBulkImport.genericJobError": "An error has occured while processing the import", "supplierBulkImport.genericSubmitError": "An error has occured when submitting data", "supplierBulkImport.govOrgIdTextareaLabel": "Business IDs", "supplierBulkImport.govOrgIdTextareaLabelVAT": "Business IDs or VAT numbers", "supplierBulkImport.reviewDataFormHelpText": "Only left column will be submitted to the system.", "supplierBulkImport.reviewWizardStep": "Review and submit", "supplierBulkImport.search_by_vat_info_not_supported_countries": "Search by VAT number is not supported for Sweden, Lithuania, Netherlands and Norway", "supplierBulkImport.submitWizardStep": "Done", "supplierBulkImport.wizardDoneStepDoneButton": "Close", "supplierBulkImport.wizardEnterDataNextBtn": "Next", "supplierBulkImport.wizardReviewCancelAndBackButton": "Cancel and go back to previous step", "supplierBulkImport.wizardReviewNextButton": "Next", "supplierBulkImport.wizardTitle": "Add suppliers", "supplierForm.VAT_label": "VAT", "supplierForm.button_next": "Next", "supplierForm.cancel_action": "Cancel", "supplierForm.company_bussiness_id_label": "Business ID", "supplierForm.company_bussiness_id_or_vat_label": "Business ID or VAT", "supplierForm.company_name_label": "Company name", "supplierForm.country_of_registration_label": "Country of registration", "supplierForm.endDate": "End date", "supplierForm.enter_contact_email_placeholder": "Fill in e-mail address for supplier", "supplierForm.field_is_required": "Required field.", "supplierForm.nocontractType_placeholder": "Select contract type", "supplierForm.norole_placeholder": "Please select supplier type", "supplierForm.startDate": "Start date", "supplierForm.supplier_contract_type": "Contract type", "supplierForm.supplier_role": "Supplier type", "supplierForm.workArea": "Professional work area (multiple can be selected)", "errors.validation.field.alphanumeric": "The field must contain only Latin letters or digits", "errors.validation.field.alphanumericWithAdditionalSymbols": "The field must contain only Latin letters, digits or one of the symbols: {additionalSymbolsJoined}", "errors.validation.field.atLeastOneAlphanumeric": "The field must contain at least one letter or digit", "errors.validation.field.billingAddress": "Please check that the e-invoicing address is filled out correctly", "errors.validation.field.blacklistedEmailAddress": "{email} email addresses are not allowed", "errors.validation.field.bulgarianOrganizationNumber": "Please enter a correct Bulgarian organisation number (e.g. *********)", "errors.validation.field.componentCustomValidation": "Field invalid", "errors.validation.field.componentCustomValidation.phoneNumber": "The phone number is invalid", "errors.validation.field.coordinationNumber": "Coordination number is invalid. (E.g. 19840584-7290)", "errors.validation.field.croatianOrganizationNumber": "The field must contain at least seven characters", "errors.validation.field.czechianOrganizationNumber": "Please enter a correct Czech organisation number (e.g. 00014915)", "errors.validation.field.danishOrganizationNumber": "Please enter a correct Danish organisation number (e.g. 21076600)", "errors.validation.field.dateFormat": "Please enter a correct date (yyyy-mm-dd)", "errors.validation.field.dateOnlyUntilToday": "Date must be no later than today", "errors.validation.field.email": "The email address format is incorrect", "errors.validation.field.error": "Field invalid", "errors.validation.field.estonianOrganizationNumber": "Please enter a correct Estonian organisation number (e.g. 12401738)", "errors.validation.field.exactNumberOfDigits": "The field must contain exactly {numberOfDigits} digits", "errors.validation.field.expiracyDateInvalid": "Date must be no earlier than Date of issue", "errors.validation.field.fTaxOrganizationNumber": "Please enter a correct F-tax number (123456-1234)", "errors.validation.field.faroeIslandsOrganizationNumber": "Please enter a correct Faroe Islands organisation number (e.g. 1234)", "errors.validation.field.finnishOrganizationCode": "Please enter a correct Finnish organisation number (e.g. 1234567-1)", "errors.validation.field.finnishPersonalNumber": "Please enter correct national Person ID number (E.g. 160768-867F)", "errors.validation.field.incorrectEmailsInString": "Please check these emails format: {incorrectEmails}", "errors.validation.field.latvianOrganizationNumber": "Please enter a correct Latvian organisation number (e.g. 40008012582)", "errors.validation.field.lithuanianOrganizationCode": "Please enter a correct Lithuanian organisation number (e.g. *********73, *********, 123456)", "errors.validation.field.match": "This field does not match", "errors.validation.field.match.passwords": "The passwords do not match. Please type in the same password twice.", "errors.validation.field.maxLength": "The field must contain no more than {maxLength} characters", "errors.validation.field.maxNumberOfEmailsIsImported": "Max {numberOfEmails} emails can be imported in one time. At the moment {currentNumberOfEmails} emails is submitted.", "errors.validation.field.minLength": "The field must contain at least {minLength} characters", "errors.validation.field.mismatch": "The fields should have different values", "errors.validation.field.mismatch.passwords": "The new password must be different from the old one", "errors.validation.field.nationalityCode": "Nationality code needs to be exact {codeLength} symbols length", "errors.validation.field.netherlandsOrganizationNumber": "Please enter a correct Netherlands organisation number (e.g. 67068006)", "errors.validation.field.norwegianOrganizationNumber": "Please enter a correct Norwegian organisation number (e.g. *********)", "errors.validation.field.organizationApplicationState": "Changing this status is not permitted", "errors.validation.field.password": "Minimum length of password is 8 characters", "errors.validation.field.password.hasSpaces": "The password cannot start or end in a whitespace.", "errors.validation.field.personNumberCountry": "During pilot, only users having Finnish identity number are accepted to use the service", "errors.validation.field.polishOrganizationNumber": "Please enter a correct Polish organisation number (e.g. 1234567890)", "errors.validation.field.required": "This field is required", "errors.validation.field.romanianOrganizationNumber": "Please enter a correct Romanian organisation number (e.g. J12/0101/1990)", "errors.validation.field.scannerIdCodeIsNotValid": "Please enter a valid scanner number", "errors.validation.field.slovakOrganizationNumber": "The field must contain at least seven characters", "errors.validation.field.spanishOrganizationNumber": "Please enter a correct Spanish organisation number (e.g. G79554275)", "errors.validation.field.swedishOrganizationCode": "Please enter a correct Swedish organisation number (e.g. 123456-1234)", "errors.validation.field.swedishPersonalNumber": "National Person ID number is invalid. (E.g. 19840524-7290)", "errors.validation.field.swedishFTaxNumber": "Please enter a correct F-tax number (123456-1234)", "errors.validation.form.error": "Please make sure that all required fields are filled out correctly", "registration.currentStepTitle": "Step {currentStep}", "registration.findCompany.SupportedCountries": "Information about countries we support in ID06 system", "registration.findCompany.accessCompany": "Access company", "registration.findCompany.back": "Back", "registration.findCompany.compLegalName": "Company legal name", "registration.findCompany.companyDetails": "Company details", "registration.findCompany.companyInformationAlreadyAvailable": "Company information already exists.", "registration.findCompany.companyNotFoundCreditinfo": "Company not found in Creditinfo. Search for another company or contact our", "registration.findCompany.companyNotFoundCreditsafe": "Company not found in Creditsafe. Search for another company or contact our", "registration.findCompany.companyNotFoundRegistry": "Company not found in registry. Search for another company or contact our", "registration.findCompany.orgNotSufficientInfo": "Company found, but company status is not available in Creditsafe. Search for another company or contact our", "registration.findCompany.orgInfoLimit": "There is currently no report on the company available from Creditsafe. Search for another company or contact our", "registration.findCompany.companyPhoneInRegistry": "Phone", "registration.findCompany.continue": "Continue", "registration.findCompany.correctOrgId": "correct the organization number", "registration.findCompany.countryOfRegistration": "Country of registration", "registration.findCompany.countryOfResidence": "Home country", "registration.findCompany.customerService": "customer service", "registration.findCompany.enterOrganizationId.est.required": "Please enter the correct Estonian registry code ({numberOfDigits} numbers)", "registration.findCompany.enterOrganizationId.lv.required": "Please enter the correct Latvian registration number (eleven numbers)", "registration.findCompany.enterOrganizationId.pl.required": "Please enter the correct Polish registration number (KRS)", "registration.findCompany.enterOrganizationIdEg": "Organisation number", "registration.findCompany.enterOrganizationIdEg.bg": "Nine numbers (e.g. *********)", "registration.findCompany.enterOrganizationIdEg.cz": "Eight numbers (e.g. 00014915)", "registration.findCompany.enterOrganizationIdEg.dk": "Eight numbers (e.g. 21076600)", "registration.findCompany.enterOrganizationIdEg.ee": "Eight numbers (e.g. 12345678)", "registration.findCompany.enterOrganizationIdEg.es": "Nine characters (e.g. G79554275)", "registration.findCompany.enterOrganizationIdEg.fi": "Nine characters (e.g. 1234567-1)", "registration.findCompany.enterOrganizationIdEg.fo": "Four characters (e.g. 1234)", "registration.findCompany.enterOrganizationIdEg.hr": "At least 7 characters (e.g. 1234567)", "registration.findCompany.enterOrganizationIdEg.lt": "Nine numbers (e.g. *********, 123456)", "registration.findCompany.enterOrganizationIdEg.lv": "Eleven numbers (e.g. ***********)", "registration.findCompany.enterOrganizationIdEg.nl": "Eight numbers (e.g. 67068006)", "registration.findCompany.enterOrganizationIdEg.no": "Nine numbers (e.g. *********)", "registration.findCompany.enterOrganizationIdEg.pl": "Ten numbers (e.g. 1234567890)", "registration.findCompany.enterOrganizationIdEg.ro": "At least 12 characters (e.g. J12/0101/1990)", "registration.findCompany.enterOrganizationIdEg.se": "Ten characters (e.g. 123456-1234)", "registration.findCompany.enterOrganizationIdEg.sk": "At least 7 characters (e.g. 12345/A or 1234567)", "registration.findCompany.enterOrganizationName": "Enter company name", "registration.findCompany.feesFilename": "ID06-AnnualFee.pdf", "registration.findCompany.findCompanyBtn": "Find company information", "registration.findCompany.findYourCompany": "Search for your company:", "registration.findCompany.forHelp": "for help:", "registration.findCompany.formHasErrors": "Please check company details", "registration.findCompany.formOfCompany": "Form of company", "registration.findCompany.ftax.description.link": "https://www.skatteverket.se/servicelankar/otherlanguages/inenglish/businessesandemployers/registeringabusiness/establishingyourforeignbusiness/rulesontaxregistrationforforeignentrepreneurs.4.5fc8c94513259a4ba1d800027632.html", "registration.findCompany.ftax.description.linkText": "Information from the Tax Agency on how foreign companies register with Swedish tax registration", "registration.findCompany.ftax.description1": "A Swedish F-tax must be registered by both foreign and Swedish companies, which is a requirement to be connected to the ID06 system in accordance with ID06 General provisions.", "registration.findCompany.ftax.description2": "If you are not registered for Swedish F-tax, state instead **000000-0000** and change your registration no later than 3 months to a correct tax registration number to avoid you being excluded from the ID06 system.", "registration.findCompany.ftax.description3": "A Swedish F-tax registration is carried out at the Swedish Tax Agency by the foreign company as a **foreign legal person** or as a **branch** of the foreign company.", "registration.findCompany.ftax.inputLabel": "F-tax number", "registration.findCompany.ifCorrect": "If correct, click on ''Register company'', else go back and", "registration.findCompany.ifYouAlreadyHaveAccount": "If you already have an account, ", "registration.findCompany.loadingInfoMessage": "Retrieving data from company online registry. This may take several minutes, please wait.", "registration.findCompany.orgHasNoSignatories": "This company is not eligible to register (no signatories information found in companies registry). Search for another company or contact our", "registration.findCompany.orgHasNotRegisteredFTAX": "This company is not eligible to register (company has not registered for F-Tax). Search for another company or contact our", "registration.findCompany.orgHasPaymentOverdue": "This company has payment overdue. Search for another company or contact our", "registration.findCompany.orgNeedsToBeEnrolledFirst": "To complete user validation, company must be enrolled first", "registration.findCompany.orgNeedsToBeWhitelistedInManualId.line1": "To enroll the company you must be whitelisted or verified as signatory during Manual ID verification.", "registration.findCompany.orgNeedsToBeWhitelistedInManualId.line2": "{searchForAnotherCompanyLink} or contact our customer service for help: <EMAIL> 010-480 92 00.", "registration.findCompany.orgNeedsToBeWhitelistedInManualId.line2.searchAnotherCompany": "Search for another company", "registration.findCompany.orgNotActive": "It looks like this company is not active ({reasonOfFailure}). Search for another company or contact our", "registration.findCompany.orgRegisteredOwnerNotSignatory": "Person is not signatory of the company {orgName} ({orgNumber})", "registration.findCompany.organizationId": "Company ID", "registration.findCompany.organizationId.est": "Registry code", "registration.findCompany.organizationId.fin": "Business ID", "registration.findCompany.organizationId.lt": "Legal entity code", "registration.findCompany.organizationId.lv": "Registration number", "registration.findCompany.organizationId.pl": "Registration number (KRS)", "registration.findCompany.organizationId.swe": "Organisation number", "registration.findCompany.organizationIdTitle": "Organisation number", "registration.findCompany.organizationIdTitle.bg": "Organisation number", "registration.findCompany.organizationIdTitle.cz": "Organisation number", "registration.findCompany.organizationIdTitle.dk": "Organisation number", "registration.findCompany.organizationIdTitle.ee": "Organisation number", "registration.findCompany.organizationIdTitle.es": "Organisation number", "registration.findCompany.organizationIdTitle.fi": "Organisation number", "registration.findCompany.organizationIdTitle.hr": "Organisation number", "registration.findCompany.organizationIdTitle.lt": "Organisation number", "registration.findCompany.organizationIdTitle.lv": "Organisation number", "registration.findCompany.organizationIdTitle.nl": "Organisation number", "registration.findCompany.organizationIdTitle.no": "Organisation number", "registration.findCompany.organizationIdTitle.pl": "Organisation number", "registration.findCompany.organizationIdTitle.ro": "Organisation number", "registration.findCompany.organizationIdTitle.se": "Organisation number", "registration.findCompany.organizationIdTitle.sk": "Organisation number", "registration.findCompany.organizationName": "Company name", "registration.findCompany.regNumber": "Company registration number", "registration.findCompany.registerCompany": "Register company", "registration.findCompany.registryFullAddress": "Registration address", "registration.findCompany.requestAccess": "Request access", "registration.findCompany.requestAccessSuccess": "Your request was sent to main users. Please wait for invitation email.", "registration.findCompany.searchDoneInRegistry": "Company information will be retrieved from business registry based on this information.", "registration.findCompany.signInHere": "sign in here", "registration.findCompany.vatNumber": "VAT number", "registration.findCompany.searchBy": "Search by", "registration.findCompany.registrationNumberRadio": "Registration number", "registration.findCompany.vatNumberRadio": "VAT number", "registration.findCompany.vatNumberRadioDisabledText": "Search by VAT number is not supported for this country", "submenu.dashboard": "DASHBOARD", "serviceRedirect.explanation.conclusion": "We will announce more about these new releases later.", "serviceRedirect.explanation.mainMessage": "After the first phase, a single user can have user permissions for several companies. For the moment, however, access to the Valvoja service is restricted to the first company registered by the user.", "serviceRedirect.explanation.title": "We are developing new features", "serviceRedirect.modal.cancelBtn": "Cancel", "serviceRedirect.modal.confirmBtn": "Continue to {name}", "serviceRedirect.modal.message1": "We are redirecting you to {service} service for the company ", "serviceRedirect.modal.message2": "For the moment, you can only use the Valvoja services of one company ", "serviceRedirect.modal.readMore": "(read more)", "serviceRedirect.modal.title": "WE ARE CHANGING YOUR COMPANY ACCOUNT", "time.now": "Just now", "tooltips.password.longPasswordException": "For passwords with at least 20 characters these rules are not applied.", "tooltips.password.passwordMustHave": "Your password must have:", "tooltips.password.passwordMustHave.atLeastOneNumber": "at least one number", "tooltips.password.passwordMustHave.atLeastTenCharacters": "at least 10 characters", "tooltips.password.passwordMustHave.upperAndLowerCaseLetters": "upper and lower case letters", "pagination.first": "First", "pagination.last": "Last", "organization.reminder.changeInvoicingType.changeInvoicing": "Start using electronic invoicing {here}", "organization.reminder.changeInvoicingType.changeInvoicingSettings": "Check your invoicing settings", "organization.reminder.changeInvoicingType.closeReminder": "Close this reminder", "organization.reminder.changeInvoicingType.here": "here", "organization.reminder.changeInvoicingType.info": "Your company has selected invoicing by mail. You can now also receive the invoice to your email address. "}