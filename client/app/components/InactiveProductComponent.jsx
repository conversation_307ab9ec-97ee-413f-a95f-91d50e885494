import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from './i18n/FormattedMessage';

const orderBolUrl = 'https://id06.se/id06-bolagsdeklaration/bestall-id06-bolagsdeklaration/';

const InactiveProductComponent = () => (
  <div id="inactive_product_page" className="container">
    <h1>
      <FormattedMessage
        id="basepage.licenseNeeded.header"
        defaultMessage="Action Required: License Needed"
      />
    </h1>
    <p>
      <FormattedMessage
        id="basepage.licenceNeeded.body"
        defaultMessage={
          'In order to use ID06 Bolagsdeklaration, your organization must purchase a license. ' + 
          'Visit {orderBolUrl} to learn how to order a license and ' + 
          'unlock all the benefits of this service.'
        }
        values={{
          lineBreak: <br />,
          orderBolUrl: (
            <a
              href={orderBolUrl}
              target="_blank"
              rel="noopener noreferrer"
              id="order-bol-url"
            >
              <FormattedMessage
                id="basepage.orderBolLinkText"
                defaultMessage="Order ID06 Bolagsdeklaration"
              />
            </a>
          )
        }}
      />
    </p>
  </div>
);

export default injectIntl(InactiveProductComponent);
