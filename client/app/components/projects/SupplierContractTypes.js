import { defineMessages } from 'react-intl';

export const CONTRACTING = 'contracting';
export const CONSULTING = 'consulting';
export const PERSONNEL_LEASING = 'personnel_leasing';
export const MACHINE_EQUIPMENT_LEASING = 'machine_equipment_leasing';
export const MATERIALS_AND_PRODUCTS = 'materials_and_products';
export const TRANSPORTATION = 'transportation';

export const ContractTypeMessages = defineMessages({
  contracting: {
    id: 'supplier.contract_type.contracting',
    description: 'Contract type - contracting',
    defaultMessage: 'Contracting',
  },
  consulting: {
    id: 'supplier.contract_type.consulting',
    description: 'Contract type - consulting',
    defaultMessage: 'Consulting',
  },
  personnel_leasing: {
    id: 'supplier.contract_type.personnel_leasing',
    description: 'Contract type - personnel_leasing',
    defaultMessage: 'Personnel leasing',
  },
  machine_equipment_leasing: {
    id: 'supplier.contract_type.machine_equipment_leasing',
    description: 'Contract type - machine_equipment_leasing',
    defaultMessage: 'Machine or equipment leasing',
  },
  materials_and_products: {
    id: 'supplier.contract_type.materials_and_products',
    description: 'Contract type - materials_and_products',
    defaultMessage: 'Materials and products (with subcontractors)',
  },
  transportation: {
    id: 'supplier.contract_type.transportation',
    description: 'Contract type - transportation',
    defaultMessage: 'Transportation',
  },
});

export const ContractTypeDescriptions = defineMessages({
  contracting: {
    id: 'supplier.contract_type.contracting.description',
    description: 'Contract type description - contracting',
    defaultMessage: `Have responsibility for building, constructing or providing
     a service to the construction site`,
  },
  consulting: {
    id: 'supplier.contract_type.consulting.description',
    description: 'Contract type description - consulting',
    defaultMessage: `E.g. constructor (representing the construction client),
      project management, safety, supervision, audit etc.`,
  },
  personnel_leasing: {
    id: 'supplier.contract_type.personnel_leasing.description',
    description: 'Contract type description - personnel_leasing',
    defaultMessage: 'Only supplies personnel',
  },
  machine_equipment_leasing: {
    id: 'supplier.contract_type.machine_equipment_leasing.description',
    description: 'Contract type description - machine_equipment_leasing',
    defaultMessage: 'Leasing without personnel using the machinery or equipment on site',
  },
  materials_and_products: {
    id: 'supplier.contract_type.materials_and_products.description',
    description: 'Contract type description - materials_and_products',
    defaultMessage: `Supplier will be hiring subcontractor
      to perform work on site (e.g. installation work) or
      need to hire a transportation firm for delivery`,
  },
  transportation: {
    id: 'supplier.contract_type.transportation.description',
    description: 'Contract type description - transportation',
    defaultMessage: 'Only provides transportation and will not perform work on site',
  },
});

export const SUPPLIER_CONTRACT_TYPES = {
  contracting: {
    value: CONTRACTING,
    title: ContractTypeMessages.contracting,
    description: ContractTypeDescriptions.contracting,
  },
  consulting: {
    value: CONSULTING,
    title: ContractTypeMessages.consulting,
    description: ContractTypeDescriptions.consulting,
  },
  personnel_leasing: {
    value: PERSONNEL_LEASING,
    title: ContractTypeMessages.personnel_leasing,
    description: ContractTypeDescriptions.personnel_leasing,
  },
  machine_equipment_leasing: {
    value: MACHINE_EQUIPMENT_LEASING,
    title: ContractTypeMessages.machine_equipment_leasing,
    description: ContractTypeDescriptions.machine_equipment_leasing,
  },
  materials_and_products: {
    value: MATERIALS_AND_PRODUCTS,
    title: ContractTypeMessages.materials_and_products,
    description: ContractTypeDescriptions.materials_and_products,
  },
  transportation: {
    value: TRANSPORTATION,
    title: ContractTypeMessages.transportation,
    description: ContractTypeDescriptions.transportation,
  },
};

export const ELIGIBLE_CONTRACT_TYPES = [
  SUPPLIER_CONTRACT_TYPES.consulting.value,
  SUPPLIER_CONTRACT_TYPES.contracting.value,
  SUPPLIER_CONTRACT_TYPES.personnel_leasing.value,
];

export const ALL_SUPPLIER_CONTRACT_TYPES = Object.keys(SUPPLIER_CONTRACT_TYPES);

export default ContractTypeMessages;
