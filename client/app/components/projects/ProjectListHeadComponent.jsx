import PropTypes from 'prop-types';
import React from 'react';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import { preventDefault } from '../../helpers/EventHandlers';
import AddProjectButton from '../projects/AddProjectButton';

class ProjectListHeadComponent extends React.Component {
  static get propTypes() {
    return {
      narrow: PropTypes.bool,
      showAddNarrow: PropTypes.bool,
      onClick: PropTypes.func,
      hideAddProject: PropTypes.bool,
      hideSuppliers: PropTypes.bool,
      hideStatus: PropTypes.bool,
    };
  }

  static get defaultProps() {
    return {
      narrow: false,
      showAddNarrow: false,
      hideAddProject: false,
      hideSuppliers: false,
      hideStatus: false,
    };
  }

  render() {
    const { narrow, showAddNarrow, hideAddProject, hideSuppliers, hideStatus } = this.props;

    return (
      <tr>
        <th>
          <a onClick={preventDefault} className="th-label pointer-events-none">
            <FormattedMessage
              id="projects.nameColumn"
              description="Project name table column heading"
              defaultMessage="Project"
            />
          </a>
          {!hideAddProject && showAddNarrow && (
            <AddProjectButton
              id="add_project_button_narrow"
              onClick={this.props.onClick}
              showAddCompact
            />
          )}
        </th>
        {!hideStatus && (
          <th className={narrow ? 'hidden' : null}>
            <a onClick={preventDefault} className="th-label pointer-events-none">
              <FormattedMessage
                id="projects.statusColumn"
                description="Project status table column heading"
                defaultMessage="Status"
              />
            </a>
          </th>
        )}
        {!hideSuppliers && (
          <th className={narrow ? 'hidden' : null}>
            <a onClick={preventDefault} className="th-label pointer-events-none">
              <FormattedMessage
                id="projects.contractorsColumn"
                description="Project contractor count table column heading"
                defaultMessage="Contractors"
              />
            </a>
          </th>
        )}
        <th className={narrow ? 'hidden' : null}>
          <span className="d-flex justify-content-between">
            <a onClick={preventDefault} className="th-label pointer-events-none">
              <FormattedMessage
                id="projects.stateColumn"
                description="Project state"
                defaultMessage="State"
              />
            </a>
            {!hideAddProject && (
              <span className="add-project-button right">
                <AddProjectButton id="add_project_button_main" onClick={this.props.onClick} />
              </span>
            )}
          </span>
        </th>
      </tr>
    );
  }
}

export default ProjectListHeadComponent;
