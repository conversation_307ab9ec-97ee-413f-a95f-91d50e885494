/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import InfiniteScroll from 'react-infinite-scroller';
import { nodeScrollIntoView } from '../../helpers/positioning';
import { featureActive } from '../../helpers/FeatureFlags';

import loadProjectList from '../../actions/actionCreators/ProjectListLoading';
import loadMoreProjectList from '../../actions/actionCreators/ProjectListLoadMore';
import detailsViewClose from '../../actions/actionCreators/DetailsViewClose';
import { projectStore, userActionsStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import ProjectListRowComponent from './ProjectListRowComponent';
import ListMessageRowComponent from '../shared/ListMessageRowComponent';
import { preventDefault } from '../../helpers/EventHandlers';
import ProjectListHeadComponent from '../projects/ProjectListHeadComponent';
import Spinner from '../shared/Spinner';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';
/* eslint-enable max-len */

class ProjectListComponent extends React.Component {
  static get propTypes() {
    return {
      narrow: PropTypes.bool,
      selectedItemId: PropTypes.string,
      router: routerPropType.isRequired,
    };
  }

  static get defaultProps() {
    return {
      narrow: false,
      selectedItemId: null,
    };
  }


  constructor(props) {
    super(props);
    this.projectSubscription = new StoreSubscription(projectStore, this.projectsChanged.bind(this));
    this.userActionsSubscription = new StoreSubscription(
      userActionsStore,
      this.userActionsChanged.bind(this)
    );
    this.state = this.mapStoreToState(projectStore.getState());
    this.userScrollInProgress = userActionsStore.getState().scrolling;
    this.prevSelectedItemId = null;
    this.selectedRowRef = React.createRef();
  }

  componentDidMount() {
    this.projectSubscription.activate();
    this.userActionsSubscription.activate();
    setTimeout(() => {
      loadProjectList(false);
    }, 0);
  }

  componentDidUpdate() {
    if (
      !this.userScrollInProgress &&
      this.props.narrow &&
      !this.state.loading &&
      !this.addProjectInProgress() &&
      this.props.selectedItemId !== this.prevSelectedItemId
    ) {
      // https://jira.tilaajavastuu.fi/browse/BOL-1766
      // if selected project does exist in the details view list,
      // scroll it into the view
      if (this.selectedRowRef.current) {
        nodeScrollIntoView(this.selectedRowRef);
        this.prevSelectedItemId = this.props.selectedItemId;
      } else if (this.state.loadMore) {
        // load next available page
        setTimeout(() => {
          loadMoreProjectList(true);
        }, 0);
      }
    }
  }

  componentWillUnmount() {
    this.projectSubscription.deactivate();
    this.userActionsSubscription.deactivate();
  }

  mapStoreToState(storeState) {
    return {
      loaded: storeState.loaded,
      loading: storeState.loading,
      projects: storeState.projects,
      loadMore: storeState.loadMore,
    };
  }

  projectsChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  userActionsChanged(storeState) {
    // https://jira.tilaajavastuu.fi/browse/BOL-1766
    // do not use state for scrolling property since this causes
    // firing of componentDidUpdate() during user scrolling and
    // breaks down 'scroll into view' behaviour
    this.userScrollInProgress = storeState.scrolling;
  }

  addProjectClicked(event) {
    preventDefault(event);
    detailsViewClose();
    this.props.router.navigate('/projects/new');
  }

  addProjectInProgress() {
    return this.props.router.location.pathname === '/projects/new';
  }

  rowClicked(project) {
    if (project.id === this.props.selectedItemId) {
      detailsViewClose();
      this.props.router.navigate('/projects');
    } else {
      this.props.router.navigate(`/projects/${project.id}`);
    }
  }

  loadMore() {
    if (!this.state.loading && this.state.loadMore) {
      setTimeout(loadMoreProjectList, 0);
    }
  }

  colSpan() {
    const many = featureActive('suppliers') ? 5 : 4;
    return this.props.narrow ? 1 : many;
  }

  render() {
    const { narrow } = this.props;
    const showAddNarrow = narrow && !this.addProjectInProgress();
    let rows = this.state.projects.map(project => {
      const selected = project.id === this.props.selectedItemId;
      return (
        <ProjectListRowComponent
          key={project.id}
          selected={selected}
          project={project}
          narrow={narrow}
          onClick={() => this.rowClicked(project)}
          ref={selected?this.selectedRowRef:null}
        />
      );
    });

    if (rows.length === 0) {
      let message;
      let className = '';

      if (this.state.loaded) {
        message = 'messageEmpty';
      } else if (this.state.loading) {
        message = 'messageLoading';
      } else {
        // Technically there are two possibilities at this point: we
        // failed to load the project list (state.failed is true) or we
        // never initiated the loading (state.failed is false).  But we're
        // calling loadProjectList() from componentDidMount(), so
        // the 2nd option is impossible.
        message = 'messageFailed';
        className = 'text-warning pointer-events-none';
      }
      rows = (
        <ListMessageRowComponent message={message} className={className} colSpan={this.colSpan()} />
      );
    }
    // XXX: failure to refresh the project list is not indicated in any way!

    return (
      <InfiniteScroll
        pageStart={0}
        loadMore={this.loadMore.bind(this)}
        hasMore={this.state.loadMore}
        loader={
          <div key={1}>
            <Spinner />
          </div>
        }
      >
        <table
          className={classNames(
            'table',
            'table--bol',
            'table-striped',
            'table-striped--bol',
            'table-hover',
            'project-list',
            // Note that more than one of these can be active at the same time:
            // - loaded means the initial load succeeded (eventually)
            // - failed means the last load failed
            // - loading means a load is in progress
            {
              loaded: this.state.loaded,
              failed: this.state.failed,
              loading: this.state.loading,
            }
          )}
        >
          <thead>
            <ProjectListHeadComponent
              showAddNarrow={showAddNarrow}
              narrow={narrow}
              onClick={this.addProjectClicked.bind(this)}
            />
          </thead>
          <tbody className="table-clickable-rows--bol">{rows}</tbody>
        </table>
      </InfiniteScroll>
    );
  }
}

export default withRouter(ProjectListComponent);
