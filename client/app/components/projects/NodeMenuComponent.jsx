import PropTypes from 'prop-types';
import React from 'react';
import { Dropdown, DropdownToggle, DropdownMenu } from 'reactstrap';
import classNames from 'classnames';

import { stopPropagation } from '../../helpers/EventHandlers';

class NodeMenuComponent extends React.Component {
  static get propTypes() {
    return {
      children: PropTypes.node,
      disableContextMenu: PropTypes.bool,
      nodeTitle: PropTypes.string,
    };
  }

  static get defaultProps() {
    return {
      disableContextMenu: false,
      nodeTitle: null,
    };
  }

  constructor(props) {
    super(props);
    this.state = {
      isMenuExpanded: false,
    };
  }

  toggleDropdown() {
    this.setState(prevState => ({
      isMenuExpanded: !prevState.isMenuExpanded,
    }));
  }

  someChildrenVisible() {
    return React.Children.toArray(this.props.children).some(c => c.props.visible);
  }

  renderDropDownToggle() {
    if (this.props.disableContextMenu) {
      return (
        <div>
          <i className="fa fa-caret-down" aria-hidden="true" />
        </div>
      );
    }
    return (
      <DropdownToggle
        tag="div"
        role="button"
        className={classNames({ 'p-4': !!this.props.nodeTitle })}
        onClick={stopPropagation}
      >
        {this.props.nodeTitle && <span className="pr-4">{this.props.nodeTitle}</span>}
        <i className="fa fa-caret-down" aria-hidden="true" />
      </DropdownToggle>
    );
  }

  render() {
    if (this.someChildrenVisible()) {
      return (
        <Dropdown
          className="node-menu-handle open"
          toggle={this.toggleDropdown.bind(this)}
          isOpen={this.state.isMenuExpanded}
        >
          {this.renderDropDownToggle()}
          <DropdownMenu>{this.props.children}</DropdownMenu>
        </Dropdown>
      );
    }
    return null;
  }
}

export default NodeMenuComponent;
