import { defineMessages } from 'react-intl';

const messages = defineMessages({
  active: {
    id: 'project.stateActive',
    description: 'Project state "active"',
    defaultMessage: 'Active',
  },
  closed: {
    id: 'project.stateClosed',
    description: 'Project state "closed"',
    defaultMessage: 'Closed',
  },
  draft: {
    id: 'project.stateDraft',
    description: 'Project state "draft"',
    defaultMessage: 'Draft',
  },
});

// eslint-disable-next-line import/prefer-default-export
export const translateState = (formatMessage, state) => formatMessage(messages[state]);
