import PropTypes from 'prop-types';
import React from 'react';
import { DropdownItem } from 'reactstrap';

class NodeMenuItem extends React.Component {
  static get defaultProps() {
    return {
      visible: false,
    };
  }

  static get propTypes() {
    return {
      visible: PropTypes.bool,
      onClick: PropTypes.func,
      className: PropTypes.string.isRequired,
      children: PropTypes.node,
    };
  }

  render() {
    if (!this.props.visible) return null;

    return (
      <DropdownItem className={this.props.className} onClick={this.props.onClick}>
        {this.props.children}
      </DropdownItem>
    );
  }
}

export default NodeMenuItem;
