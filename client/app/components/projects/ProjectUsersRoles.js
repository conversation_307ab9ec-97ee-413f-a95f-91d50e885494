import { defineMessages } from 'react-intl';

export const LEADER = 'LEADER';
export const MANAGER = 'MANAGER';
export const MEMBER = 'MEMBER';

export const ProjectRolesMessages = defineMessages({
  leader: {
    id: 'roles.project.leader',
    description: 'Role of project leader',
    defaultMessage: 'Leader',
  },
  manager: {
    id: 'roles.project.manager',
    description: 'Role of project manager',
    defaultMessage: 'Manager',
  },
  member: {
    id: 'roles.project.member',
    description: 'Role for project member',
    defaultMessage: 'Member',
  },
});

export const ProjectRolesDescriptions = defineMessages({
  leader: {
    id: 'roles.project.leader.description',
    description: 'Role of project leader described',
    defaultMessage: 'Can view project and manage users from own organisation',
  },
  manager: {
    id: 'roles.project.manager.description',
    description: 'Role of project manager described',
    defaultMessage: 'Can view project and manage users from own organisation',
  },
  member: {
    id: 'roles.project.member.description',
    description: 'Role for project member described',
    defaultMessage: 'Can view project',
  },
});

export const ROLE_OPTIONS = {
  LEADER: {
    value: LEADER,
    title: ProjectRolesMessages.leader,
    description: ProjectRolesDescriptions.leader,
  },
  MANAGER: {
    value: MANAGER,
    title: ProjectRolesMessages.manager,
    description: ProjectRolesDescriptions.manager,
  },
};

export default ProjectRolesMessages;
