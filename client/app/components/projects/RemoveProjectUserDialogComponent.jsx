import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../../components/i18n/FormattedMessage'

import Dialog from '../shared/Dialog';

const ConfirmRemoveContent = props => (
  <div>
    <p>
      <FormattedMessage
        id="projectUsers.deletDialog.contentMessage"
        description="Text in delete confirmation dialog"
        defaultMessage={
          'This action will remove user {email} with role {role} ' +
          'representing company {company} from the project. You may add ' +
          'this user to the project again.'
        }
        values={{
          email: props.userEmail,
          role: props.userRole,
          company: props.company,
        }}
      />
    </p>

    <button id="confirm_remove" className="btn btn-sm btn-primary" onClick={props.handleConfirm}>
      <FormattedMessage
        id="projectUsers.removeUserLink"
        description="Remove button for confirm project user remove dialog"
        defaultMessage="Remove"
      />
    </button>
    <button
      id="cancel_remove"
      className="btn btn-sm btn-primary right"
      onClick={props.handleCancel}
    >
      <FormattedMessage
        id="projectForm.cancelAction"
        description="Cancel removal of project supplier"
        defaultMessage="Cancel"
      />
    </button>
  </div>
);

const ConfirmRemoveTitle = (
  props // eslint-disable-line react/no-multi-comp
) => (
  <FormattedMessage
    id="projectUsers.deletDialog.titleMessage"
    description="Title for remove project user dialog"
    defaultMessage="Remove user {email} with role {role}?"
    values={{ email: props.userEmail, role: props.userRole }}
  />
);

const RemoveProjectUserDialogComponent = props => (
  // eslint-disable-line react/no-multi-comp
  <Dialog dialogType="CONFIRM_REMOVE_PROJECT_USER" placement={props.placement}>
    <ConfirmRemoveTitle />
    <ConfirmRemoveContent />
  </Dialog>
);

ConfirmRemoveContent.propTypes = {
  userEmail: PropTypes.string,
  userRole: PropTypes.string,
  company: PropTypes.string,
  handleConfirm: PropTypes.func,
  handleCancel: PropTypes.func,
};

ConfirmRemoveTitle.propTypes = {
  userEmail: PropTypes.string,
  userRole: PropTypes.string,
};

RemoveProjectUserDialogComponent.propTypes = {
  placement: PropTypes.string,
};

export default RemoveProjectUserDialogComponent;
