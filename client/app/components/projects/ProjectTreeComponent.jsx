/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { defineMessages, injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import classNames from 'classnames';
import ReactToPrint from '../shared/ReactToPrint';

import {
  projectViewStore,
  projectTreeEditStore,
  companyViewContextualStore,
} from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import CompanyDetailsContextualComponent from '../companies/CompanyDetailsContextualComponent';
import ProjectTreeRootComponent from './ProjectTreeRootComponent';
import ProjectTreeNodeComponent from './ProjectTreeNodeComponent';
import ProjectTreeSubHeaderComponent from './ProjectTreeSubHeaderComponent';
import AddSubcontractorContextBlock from './AddSubcontractorContextBlock';
import LoadingMessages from '../shared/LoadingMessages';
import { SUPPLIER_LINKED, SUPPLIER_UNLINKED, SUPPLIER_VISITOR } from '../../Constants';
import {
  filterTreeByStatus,
  annotateWithChildStatuses,
  supplierContainsSameCompany,
  isAncestor,
  countChildren,
  getFlatSuppliers,
} from '../../helpers/ProjectTree';
import cancelProjectTreeEditMode from '../../actions/actionCreators/CancelProjectTreeEditMode';
import projectTreeEditModeSave from '../../actions/actionCreators/ProjectTreeEditModeSave';
import projectSuppliersRemoveSupplier from '../../actions/actionCreators/ProjectSuppliersRemoveSupplier';
import RemoveFlatSupplierDialogComponent from './RemoveFlatSupplierDialogComponent';
import SupplierRoleDowngradeDialogComponent from './SupplierRoleDowngradeDialogComponent';
import AddSubcontractorFormComponent from './AddSubcontractorFormComponent';
import SubcontractorFormEditComponent from './SubcontractorEditComponent';
import projectTreeExpand from '../../actions/actionCreators/ProjectTreeExpand';
import NodeMenuComponent from './NodeMenuComponent';
import NodeMenuItem from './NodeMenuItem';
import printNoStatusesMenuClicked from '../../actions/actionCreators/PrintNoStatusesMenuClicked';
import ProjectReportHeader from '../reports/ProjectReportHeader';
import { featureActive } from '../../helpers/FeatureFlags';
import { intlPropType } from '../i18n/IntlPropTypes';
/* eslint-enable max-len */

const messages = defineMessages({
  visitors: {
    id: 'projectTree.visitors',
    description: 'Title for the Visitors tree node',
    defaultMessage: 'Visiting companies',
  },
  noRegisteredVisitors: {
    id: 'projectTree.noRegisteredVisitors',
    description: 'Placeholder text for empty visitor nodes list',
    defaultMessage: 'There are no registered visiting compnies',
  },
  unlinkedSuppliers: {
    id: 'projectTree.unlinkedSuppliers',
    description: 'Title for the Unlinked Suppliers tree node',
    defaultMessage: 'Unlinked Suppliers',
  },
  noUnlinkedSuppliers: {
    id: 'projectTree.noUnlinkedSuppliers',
    description: 'Placeholder text for empty unlinked supplier nodes list',
    defaultMessage: 'There are no unlinked suppliers',
  },
  nonPaedSuppliers: {
    id: 'projectTree.nonPaedSuppliers',
    description: 'Title for the non-preannounced suppliers tree node',
    defaultMessage: 'Non-preannounced suppliers',
  },
  noRegisteredNonPaedSuppliers: {
    id: 'projectTree.noRegisteredNonPaedSuppliers',
    description: 'Placeholder text for empty non-preannounced suppliers list',
    defaultMessage: 'There are no Non-preannounced suppliers',
  },
  clientRole: {
    id: 'projectTree.clientRole',
    description: 'Role of project root node - client',
    defaultMessage: 'Project client',
  },
});

class ProjectTreeComponent extends React.Component {
  static get propTypes() {
    return {
      projectId: PropTypes.string,
      headerRef: PropTypes.shape({ current: PropTypes.elementType }),
      expandedTree: PropTypes.bool,
      projectReportMode: PropTypes.bool,
      intl: intlPropType.isRequired,
    };
  }

  static get defaultProps() {
    return {
      expandedTree: false,
      projectReportMode: false,
    };
  }

  constructor(props) {
    super(props);
    this.treeSubscription = new StoreSubscription(projectViewStore, this.storesChanged.bind(this));
    this.projectTreeEditStore = new StoreSubscription(
      projectTreeEditStore,
      this.storesChanged.bind(this)
    );
    this.companyViewContextualStore = new StoreSubscription(
      companyViewContextualStore,
      this.storesChanged.bind(this)
    );
    this.state = this.mapStoreToState(
      projectViewStore.getState(),
      projectTreeEditStore.getState(),
      companyViewContextualStore.getState()
    );
  }

  componentDidMount() {
    this.treeSubscription.activate();
    this.projectTreeEditStore.activate();
    this.companyViewContextualStore.activate();
  }

  componentWillUnmount() {
    this.treeSubscription.deactivate();
    this.projectTreeEditStore.deactivate();
    this.companyViewContextualStore.deactivate();
  }

  onEditExit(event) {
    event.preventDefault();
    cancelProjectTreeEditMode();
  }

  onEditSave(event) {
    event.preventDefault();
    projectTreeEditModeSave(true);
  }

  onEditSaveAndClose(event) {
    event.preventDefault();
    projectTreeEditModeSave();
  }

  // eslint-disable-next-line react/sort-comp
  mapStoreToState(treeStoreState, editStoreState, detailsStoreState) {
    const projectTree = editStoreState.editMode
      ? editStoreState.projectTree
      : treeStoreState.project_tree;
    return {
      project_tree: projectTree,

      project_id: treeStoreState.selected_project_id,
      project_name: treeStoreState.name,
      project_tree_status_filter: treeStoreState.project_tree_status_filter,
      add_subcontractor_is_open: treeStoreState.add_subcontractor_is_open,
      add_subcontractor_target_ref: treeStoreState.add_subcontractor_target_ref,
      add_subcontractor_company_name: treeStoreState.add_subcontractor_company_name,
      add_subcontractor_supplier_id: treeStoreState.add_subcontractor_supplier_id,
      add_subcontractor_supplier_type: treeStoreState.add_subcontractor_supplier_type,
      add_subcontractor_is_root: treeStoreState.add_subcontractor_is_root,
      subcontractor_edit_is_open: treeStoreState.subcontractor_edit_is_open,
      subcontractor_edit_supplier_data: treeStoreState.subcontractor_edit_supplier_data,
      isPaRestarting: treeStoreState.isPaRestarting,
      confirmation_dialog_is_open: treeStoreState.confirmation_dialog_tree_is_open,
      confirmation_dialog_tree_is_open: treeStoreState.confirmation_dialog_tree_is_open,
      company_details_target_ref: detailsStoreState.company_details_target_ref,
      company_details_tree_is_open: detailsStoreState.company_details_tree_is_open,
      company_details_company_id: detailsStoreState.company_details_company_id,
      company_details_company_name: detailsStoreState.company_details_company_name,
      permissions: treeStoreState.permissions,
      project_tree_expanded: treeStoreState.project_tree_expanded || this.props.expandedTree,
      project_tree_expand_update_disable: treeStoreState.project_tree_expand_update_disable,

      loaded: treeStoreState.project_tree_loaded,
      loading: treeStoreState.project_tree_loading,
      failed: treeStoreState.project_tree_failed,

      editMode: editStoreState.editMode,
      movedSupplier: editStoreState.movedSupplier,
      movedSupplierRole: editStoreState.movedSupplierRole,
      saveInProgress: editStoreState.saveInProgress,
      errors: editStoreState.errors,
      moveActions: editStoreState.moveActions,
      printNoStatuses: treeStoreState.printNoStatuses,

      isPAEnabled: featureActive('pre_announcements') && treeStoreState.pa_form_enabled,
    };
  }

  storesChanged() {
    this.setState(
      this.mapStoreToState(
        projectViewStore.getState(),
        projectTreeEditStore.getState(),
        companyViewContextualStore.getState()
      )
    );
  }

  isLoaded() {
    return this.state.project_id === this.props.projectId && this.state.project_tree;
  }

  isContextMenuDisabled() {
    return (
      this.state.add_subcontractor_is_open ||
      this.state.subcontractor_edit_is_open ||
      this.state.confirmation_dialog_is_open ||
      this.state.confirmation_dialog_tree_is_open ||
      this.state.company_details_tree_is_open ||
      this.state.saveInProgress ||
      this.state.loading
    );
  }

  isExpandUpdateDisabled() {
    return (
      this.isContextMenuDisabled() ||
      this.state.editMode ||
      this.state.project_tree_expand_update_disable
    );
  }

  areStatusesHidden() {
    return this.state.printNoStatuses;
  }

  hasPlaceAction(movedSupplierId, targetSupplier, supplierType = SUPPLIER_LINKED) {
    if (
      !(
        targetSupplier.permissions.includes('add_supplier') ||
        (supplierType === SUPPLIER_UNLINKED &&
          targetSupplier.permissions.includes('add_unlinked_supplier'))
      )
    ) {
      return false;
    }
    if (!this.state.movedSupplier || this.state.movedSupplier === targetSupplier.supplier_id) {
      return false;
    }
    if (isAncestor(this.state.project_tree.root, movedSupplierId, targetSupplier.supplier_id)) {
      return false;
    }
    return !supplierContainsSameCompany(
      this.state.project_tree.root,
      movedSupplierId,
      targetSupplier.supplier_id,
      supplierType
    );
  }

  canUndoMove(action) {
    if (!action) {
      return false;
    }
    if (isAncestor(this.state.project_tree.root, action.supplier_id, action.previous_parent_id)) {
      return false;
    }
    return !supplierContainsSameCompany(
      this.state.project_tree.root,
      action.supplier_id,
      action.previous_parent_id,
      action.previous_supplier_type
    );
  }

  buildSupplierRemoveAction(supplier) {
    if (!this.state.editMode && supplier.permissions.includes('remove_supplier')) {
      return targetRef =>
        projectSuppliersRemoveSupplier(
          targetRef,
          this.props.projectId,
          supplier.supplier_id,
          supplier.supplier_rev,
          supplier.company_name,
          countChildren(supplier.children_status_counts),
          getFlatSuppliers(
            supplier.suppliers,
            supplier.unlinked_suppliers,
            supplier.project_responsible_org_name,
            supplier.company_id
          )
        );
    }
    return null;
  }

  renderChildren(node, depth, expandAll) {
    const children = node.suppliers
      .filter(supplier => supplier.company_id != null)
      .map(supplier => (
        <ProjectTreeNodeComponent
          key={supplier.supplier_id}
          depth={depth}
          status={supplier.own_status}
          name={supplier.company_name}
          govOrgId={supplier.gov_org_id}
          vatNumber={supplier.vat_number}
          country={supplier.country}
          role={supplier.supplier_role}
          contractType={supplier.contract_type}
          contractStartDate={supplier.contract_start_date}
          contractEndDate={supplier.contract_end_date}
          contractWorkAreas={supplier.contract_work_areas}
          isOneManCompany={supplier.is_one_man_company}
          hasCollectiveAgreement={supplier.has_collective_agreement}
          collectiveAgreementName={supplier.collective_agreement_name}
          reportAvailable={supplier.report_available}
          contacts={supplier.supplier_contacts}
          childrenStatusCounts={supplier.children_status_counts}
          worstSubtreeStatus={supplier.worst_subtree_status}
          expanded={expandAll || !supplier.own_status}
          companyId={supplier.company_id}
          supplierId={supplier.supplier_id}
          supplierRev={supplier.supplier_rev}
          supplierType={SUPPLIER_LINKED}
          supplierPermissions={supplier.permissions}
          hasAddAction={supplier.permissions.includes('add_supplier') && !this.state.editMode}
          hasEditAction={supplier.permissions.includes('update_supplier') && !this.state.editMode}
          hasMoveAction={
            supplier.permissions.includes('move_supplier') && !this.state.movedSupplier
          }
          hasPlaceAction={this.hasPlaceAction(this.state.movedSupplier, supplier)}
          hasCancelAction={this.state.editMode && this.state.movedSupplier === supplier.supplier_id}
          hasUndoAction={
            this.state.editMode &&
            !this.state.movedSupplier &&
            this.state.moveActions.hasOwnProperty(supplier.supplier_id)
          }
          hasDetailsAction={supplier.permissions.includes('view_supplier')}
          undoActionDisabled={!this.canUndoMove(this.state.moveActions[supplier.supplier_id])}
          handleRemoveAction={this.buildSupplierRemoveAction(supplier)}
          moved={this.state.editMode && this.state.movedSupplier === supplier.supplier_id}
          movedSupplierRole={this.state.movedSupplierRole}
          disableContextMenu={this.isContextMenuDisabled()}
          disableExpandUpdate={this.isExpandUpdateDisabled()}
          hideStatuses={this.areStatusesHidden()}
          hasPreannounceAction={supplier.permissions.includes('preannounce_supplier')}
          preannouncementId={supplier.pa_id}
          hasCreateNewPreannouncementAction={supplier.permissions.includes(
            'create_new_preannouncement'
          )}
        >
          {this.renderChildren(supplier, depth + 1, expandAll)}
        </ProjectTreeNodeComponent>
      ));
    return children;
  }

  renderEllipsisChildren(tree, depth, expandAll) {
    const children = tree.suppliers
      .filter(supplier => supplier.company_id == null)
      .map((supplier, idx) => (
        <ProjectTreeNodeComponent
          key={idx}
          depth={depth}
          ellipsis
          expanded
          name="&hellip;"
          hasAddAction={false}
          hasEditAction={false}
          hasMoveAction={false}
          hasPlaceAction={false}
          supplierType={SUPPLIER_LINKED}
          disableContextMenu={this.isContextMenuDisabled()}
          disableExpandUpdate={this.isExpandUpdateDisabled()}
          hideStatuses={this.areStatusesHidden()}
        >
          {this.renderChildren(supplier, depth + 1, expandAll)}
        </ProjectTreeNodeComponent>
      ));
    return children;
  }

  renderNoNodesText(titleId, textId) {
    const { formatMessage } = this.props.intl;

    if (this.props.projectReportMode) {
      return (
        <div className="no-nodes-title">
          {formatMessage(messages[titleId])}
          <div className="no-nodes-text">{formatMessage(messages[textId])}</div>
        </div>
      );
    }
    return null;
  }

  renderUnlinked(tree, depth) {
    const { formatMessage } = this.props.intl;
    const canAddUnlinked =
      tree.permissions.includes('add_unlinked_supplier') && !this.state.editMode;
    if (
      tree.unlinked_suppliers.length > 0 ||
      this.state.movedSupplier ||
      (canAddUnlinked && !this.props.projectReportMode)
    ) {
      return (
        <ProjectTreeNodeComponent
          classNames="unlinked-marker"
          depth={depth}
          unlinked
          hasStatusIcon={false}
          name={formatMessage(messages.unlinkedSuppliers)}
          supplierType={SUPPLIER_UNLINKED}
          hasAddAction={canAddUnlinked}
          hasEditAction={false}
          hasMoveAction={false}
          hasPlaceAction={this.hasPlaceAction(this.state.movedSupplier, tree, SUPPLIER_UNLINKED)}
          disableContextMenu={false}
          disableExpandUpdate={this.isExpandUpdateDisabled()}
        />
      );
    }
    return this.renderNoNodesText('unlinkedSuppliers', 'noUnlinkedSuppliers');
  }

  renderUnlinkedChildren(tree, depth, expandAll) {
    const children = tree.unlinked_suppliers.map(supplier => (
      <ProjectTreeNodeComponent
        classNames="unlinked"
        key={supplier.supplier_id}
        depth={depth + 1}
        status={supplier.own_status}
        name={supplier.company_name}
        govOrgId={supplier.gov_org_id}
        vatNumber={supplier.vat_number}
        country={supplier.country}
        role={supplier.supplier_role}
        contractType={supplier.contract_type}
        contractStartDate={supplier.contract_start_date}
        contractEndDate={supplier.contract_end_date}
        contractWorkAreas={supplier.contract_work_areas}
        isOneManCompany={supplier.is_one_man_company}
        hasCollectiveAgreement={supplier.has_collective_agreement}
        collectiveAgreementName={supplier.collective_agreement_name}
        reportAvailable={supplier.report_available}
        contacts={supplier.supplier_contacts}
        childrenStatusCounts={supplier.children_status_counts}
        worstSubtreeStatus={supplier.worst_subtree_status}
        expanded={expandAll}
        companyId={supplier.company_id}
        supplierId={supplier.supplier_id}
        supplierRev={supplier.supplier_rev}
        supplierType={SUPPLIER_UNLINKED}
        supplierPermissions={supplier.permissions}
        hasAddAction={supplier.permissions.includes('add_supplier') && !this.state.editMode}
        hasEditAction={supplier.permissions.includes('update_supplier') && !this.state.editMode}
        hasMoveAction={supplier.permissions.includes('move_supplier') && !this.state.movedSupplier}
        hasPlaceAction={this.hasPlaceAction(this.state.movedSupplier, supplier)}
        hasCancelAction={this.state.editMode && this.state.movedSupplier === supplier.supplier_id}
        hasUndoAction={
          this.state.editMode &&
          !this.state.movedSupplier &&
          this.state.moveActions.hasOwnProperty(supplier.supplier_id)
        }
        hasDetailsAction={!!supplier.company_id}
        undoActionDisabled={!this.canUndoMove(this.state.moveActions[supplier.supplier_id])}
        handleRemoveAction={this.buildSupplierRemoveAction(supplier)}
        moved={this.state.editMode && this.state.movedSupplier === supplier.supplier_id}
        movedSupplierRole={this.state.movedSupplierRole}
        disableContextMenu={this.isContextMenuDisabled()}
        disableExpandUpdate={this.isExpandUpdateDisabled()}
        hideStatuses={this.areStatusesHidden()}
      >
        {this.renderChildren(supplier, depth + 2, expandAll)}
      </ProjectTreeNodeComponent>
    ));
    return children;
  }

  renderVisitors(tree, depth) {
    const { formatMessage } = this.props.intl;

    if (tree.visitors.length > 0) {
      return (
        <ProjectTreeNodeComponent
          classNames="visitor-marker"
          depth={depth}
          hasStatusIcon={false}
          name={formatMessage(messages.visitors)}
        />
      );
    }
    return this.renderNoNodesText('visitors', 'noRegisteredVisitors');
  }

  renderVisitorsChildren(tree, depth, expandAll) {
    let children;
    if (featureActive('non_paed_suppliers')) {
      children = tree.visitors
        .filter(s => s.visitor_type !== 'nonpaed')
        .map(supplier => this.renderVisitorNode(supplier, depth, expandAll));
    } else {
      children = tree.visitors.map(supplier => this.renderVisitorNode(supplier, depth, expandAll));
    }

    return children;
  }

  renderNonPaedTrigger() {
    const { formatMessage } = this.props.intl;
    return <div className="sub-header">{formatMessage(messages.nonPaedSuppliers)}</div>;
  }

  renderNonPaedSuppliersChildren(tree, depth, expandAll) {
    const children = tree.visitors
      .filter(s => s.visitor_type === 'nonpaed')
      .map(supplier => this.renderVisitorNode(supplier, depth, expandAll));

    return children;
  }

  renderVisitorNode(supplier, depth, expandAll) {
    return (
      <ProjectTreeNodeComponent
        classNames="visitor"
        key={supplier.supplier_id}
        depth={depth + 1}
        status={supplier.own_status}
        name={supplier.company_name}
        govOrgId={supplier.gov_org_id}
        vatNumber={supplier.vat_number}
        country={supplier.country}
        // eslint-disable-next-line jsx-a11y/aria-role
        role=""
        reportAvailable={supplier.report_available}
        contacts={null}
        childrenStatusCounts={null}
        worstSubtreeStatus={null}
        firstVisited={supplier.first_visited}
        lastVisited={supplier.last_visited}
        expanded={expandAll}
        companyId={supplier.company_id}
        supplierId={supplier.supplier_id}
        supplierRev={supplier.supplier_rev}
        supplierType={SUPPLIER_VISITOR}
        hasAddAction={false}
        hasEditAction={false}
        hasMoveAction={false}
        hasPlaceAction={false}
        hasCancelAction={false}
        hasUndoAction={false}
        hasDetailsAction={!!supplier.company_id}
        undoActionDisabled={false}
        handleRemoveAction={this.buildSupplierRemoveAction(supplier)}
        disableContextMenu={this.isContextMenuDisabled()}
        disableExpandUpdate={this.isExpandUpdateDisabled()}
        hideStatuses={this.areStatusesHidden()}
      >
        {this.renderChildren(supplier, depth + 2, expandAll)}
      </ProjectTreeNodeComponent>
    );
  }

  renderEditErrors() {
    if (!this.state.editMode || !this.state.errors.length) {
      return null;
    }
    return (
      <div className="row d-flex justify-content-center">
        <div className="edit-errors-text">
          <FormattedMessage
            id="projectTree.edit.saveErrros"
            description="Edit tree mode Error indicator text"
            defaultMessage="Encountered an error when saving changes."
          />
        </div>
      </div>
    );
  }

  renderTreeActions() {
    if (!this.state.editMode) {
      return null;
    }
    const actionsDisabled = this.state.saveInProgress;
    return (
      <div className="modal-actions d-flex align-items-center edit-project-tree-actions">
        <div className="justify-self-one-third-width" />
        <div>
          <button
            id="edit_tree_save"
            className="btn btn-sm btn-primary"
            role="button"
            onClick={this.onEditSaveAndClose.bind(this)}
            disabled={actionsDisabled}
          >
            <FormattedMessage
              id="projectTree.edit.save"
              description="Edit tree mode 'Save' button label"
              defaultMessage="Save"
            />
          </button>
        </div>
        <div id="edit_tree_exit" className="justify-self-one-third-width justify-self-right">
          <a
            href="#"
            onClick={actionsDisabled ? () => {} : this.onEditExit.bind(this)}
          >
            <i className="fa fa-times fa-pr--bol" />
            <FormattedMessage
              id="projectTree.edit.exit"
              description="Edit tree mode exit without saving action label"
              defaultMessage="Exit without saving"
            />
          </a>
        </div>
      </div>
    );
  }

  renderModalContent(wrappedContent) {
    return (
      <div
        id="context-modal-dialog"
        className="modal in-project-tree"
        tabIndex="-1"
        data-focus="false"
        aria-hidden="true"
        style={{ display: 'block' }}
      >
        {wrappedContent}
      </div>
    );
  }

  renderAddSubcontractorForm() {
    let childComponent;
    if (this.state.add_subcontractor_is_open) {
      childComponent = AddSubcontractorFormComponent;
    } else if (this.state.subcontractor_edit_is_open) {
      childComponent = SubcontractorFormEditComponent;
    } else {
      return null;
    }

    const addSubcontractorForm = (
      <AddSubcontractorContextBlock
        childComponent={childComponent}
        targetRef={this.props.headerRef}
        isOpen
        companyName={this.state.add_subcontractor_company_name}
        supplierData={this.state.subcontractor_edit_supplier_data}
        supplierId={this.state.add_subcontractor_supplier_id}
        supplierType={this.state.add_subcontractor_supplier_type}
        projectId={this.state.project_id}
        isRoot={this.state.add_subcontractor_is_root}
        permissions={this.state.permissions}
        placement="bottom"
        isPreannouncing={this.state.isPAEnabled && !this.state.add_subcontractor_is_root}
        isPaRestarting={this.state.isPaRestarting}
        isRelativeRestartPaModal={false}
      />
    );
    return this.renderModalContent(addSubcontractorForm);
  }

  renderCompanyDetailsContextual() {
    if (!this.state.company_details_tree_is_open) {
      return null;
    }
    const companyDetailsContext = (
      <CompanyDetailsContextualComponent
        targetRef={this.props.headerRef}
        isOpen={this.state.company_details_tree_is_open}
        companyId={this.state.company_details_company_id}
        companyName={this.state.company_details_company_name}
        placement="bottom"
      />
    );
    return this.renderModalContent(companyDetailsContext);
  }

  renderReactToPrint(trigger, noStatuses) {
    return (
      <ReactToPrint
        trigger={() => trigger()}
        onHandleClick={() => {
          printNoStatusesMenuClicked(noStatuses);
          projectTreeExpand(true);
        }}
        content={() => this.componentRef}
        bodyClass="project-hierarchy-tree"
        removeAfterPrint
      />
    );
  }

  renderPrintMenuItem() {
    return (
      <NodeMenuItem visible className="dropdown-print-no-statuses">
        <FormattedMessage
          id="projects.nodemenu.printNoStatuses"
          description="Print supplier tree with no statuses and no counts"
          defaultMessage="Print without statuses"
        />
      </NodeMenuItem>
    );
  }

  renderPrintMenu() {
    return (
      <NodeMenuComponent disableContextMenu={this.isContextMenuDisabled()}>
        <NodeMenuItem visible className="dropdown-empty-item" />
        {this.renderReactToPrint(this.renderPrintMenuItem, true)}
      </NodeMenuComponent>
    );
  }

  renderPrintLink() {
    return (
      <a className="node-action action-link print-link" href="#">
        <i className="fa fa-print fa-pr--bol" aria-hidden="true" />
        <FormattedMessage
          id="projects.node.print"
          description="Print supplier tree"
          defaultMessage="Print"
        />
      </a>
    );
  }

  renderPrintToolbar() {
    return (
      <div className="print-node">
        <div className="node-content w-100 text-right">
          {this.renderReactToPrint(this.renderPrintLink, false)}
          {this.renderPrintMenu()}
        </div>
      </div>
    );
  }

  renderNonPaedSuppliers(tree, depth, expandAll) {
    const children = tree.visitors.filter(supplier => supplier.visitor_type === 'nonpaed');
    if (!children.length) {
      return this.renderNoNodesText('nonPaedSuppliers', 'noRegisteredNonPaedSuppliers');
    }
    return (
      <ProjectTreeSubHeaderComponent
        classNames="collapsible-suppliers"
        trigger={this.renderNonPaedTrigger()}
        expanded={expandAll}
      >
        {this.renderNonPaedSuppliersChildren(tree, depth, expandAll)}
      </ProjectTreeSubHeaderComponent>
    );
  }

  render() {
    const { formatMessage } = this.props.intl;

    if (!this.isLoaded()) {
      return (
        <div id="project-tree" className="project-hierarchy-tree loading">
          {formatMessage(LoadingMessages.messageLoading)}
        </div>
      );
    }

    let tree = this.state.project_tree.root;
    if (!tree) {
      return null;
    }

    let expandAll = false;
    if (this.state.project_tree_status_filter) {
      tree = filterTreeByStatus(tree, this.state.project_tree_status_filter);
      tree = annotateWithChildStatuses(tree);
      expandAll = true;
    }
    if (this.state.project_tree_expanded) {
      expandAll = true;
    }

    return (
      <div
        id="project-tree"
        className={classNames({
          'project-hierarchy-tree': true,
          loaded: this.state.loaded,
          failed: this.state.failed,
          loading: this.state.loading,
          'edit-mode': this.state.editMode,
        })}
      >
        <div
          // eslint-disable-next-line no-return-assign
          ref={el => (this.componentRef = el)}
          className="project-tree-contents"
        >
          <ProjectReportHeader className="print-header" name={this.state.project_name} />
          <ul className="root">
            {!this.state.editMode && (
              <li className="print-marker print-toolbar">{this.renderPrintToolbar()}</li>
            )}
            <ProjectTreeRootComponent
              name={tree.project_responsible_org_name}
              role={formatMessage(messages.clientRole)}
              worstSubtreeStatus={tree.worst_subtree_status}
              hasAddAction={tree.permissions.includes('add_supplier') && !this.state.editMode}
              hasPlaceAction={this.hasPlaceAction(this.state.movedSupplier, tree)}
              disableContextMenu={this.isContextMenuDisabled()}
            >
              {this.renderChildren(tree, 2, expandAll)}
              {this.renderEllipsisChildren(tree, 2, expandAll)}
            </ProjectTreeRootComponent>
            {this.renderUnlinked(tree, 0)}
            {this.renderUnlinkedChildren(tree, 0, expandAll)}
            {this.renderVisitors(tree, 0)}
            {this.renderVisitorsChildren(tree, 0, expandAll)}
            {featureActive('non_paed_suppliers') && this.renderNonPaedSuppliers(tree, 0, expandAll)}
          </ul>
          {this.renderEditErrors()}
        </div>
        {this.renderAddSubcontractorForm()}
        {this.renderCompanyDetailsContextual()}
        {this.state.confirmation_dialog_tree_is_open &&
          this.renderModalContent(<RemoveFlatSupplierDialogComponent placement="bottom" />)}
        <SupplierRoleDowngradeDialogComponent placement="bottom" />
        {this.renderTreeActions()}
      </div>
    );
  }
}

export default injectIntl(ProjectTreeComponent);
