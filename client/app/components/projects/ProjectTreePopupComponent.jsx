import React from 'react';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import projectTreeClosed from '../../actions/actionCreators/ProjectTreeClosed';
import ProjectTreeComponent from './ProjectTreeComponent';
import { projectViewStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
// eslint-disable-next-line max-len
import subcontractorFormCloseClicked from '../../actions/actionCreators/SubcontractorFormCloseClicked';
import closeDialog from '../../actions/actionCreators/CloseDialog';
import cancelProjectTreeEditMode from '../../actions/actionCreators/CancelProjectTreeEditMode';
import companyDetailsCloseClicked from '../../actions/actionCreators/CompanyDetailsCloseClicked';

class ProjectTreePopupComponent extends React.Component {
  constructor(props) {
    super(props);
    this.projectViewSub = new StoreSubscription(projectViewStore, this.storeChanged.bind(this));
    this.state = this.mapStoreToState(projectViewStore.getState());
  }

  componentDidMount() {
    this.projectViewSub.activate();
    $(this.projectTreePopup).on('hidden.bs.modal', projectTreeClosed);
  }

  componentWillUnmount() {
    this.projectViewSub.deactivate();
    $(this.projectTreePopup).off('hidden.bs.modal', projectTreeClosed);

    // Make sure modal is closed when unmounting
    $(this.projectTreePopup).modal('hide');
  }

  onClose() {
    // Close top level modals as well
    subcontractorFormCloseClicked();
    closeDialog();
    cancelProjectTreeEditMode();
    companyDetailsCloseClicked();
  }

  mapStoreToState(storeState) {
    return {
      projectId: storeState.selected_project_id,
      projectName: storeState.name,
    };
  }

  storeChanged(/* storeState */) {
    this.setState(this.mapStoreToState(projectViewStore.getState()));
  }

  render() {
    if (!this.state.projectId) {
      return null;
    }

    return (
      <div>
        <div
          id="project-tree-popup"
          className="modal project-tree"
          role="dialog"
          aria-labelledby="PopupModalLabel"
          aria-hidden="true"
          data-backdrop="false"
          tabIndex="-1"
          data-focus="false"
          ref={projectTreePopup => {
            this.projectTreePopup = projectTreePopup;
          }}
        >
          <div className="modal-dialog project-tree-modal-dialog">
            <div id="project-tree-content" className="modal-content" role="document">
              <div
                ref={targetRef => {
                  this.headerTargetRef = targetRef;
                }}
                className="modal-header d-flex justify-content-between"
              >
                <h4 className="modal-title pointer-events-none" id="PopupModalLabel">
                  <FormattedMessage
                    id="projectTree.title"
                    description="Title of the project tree popup"
                    defaultMessage="Project: "
                  />{' '}
                  <span>{this.state.projectName}</span>
                </h4>
                <div
                  role="button"
                  data-dismiss="modal"
                  aria-label="Close"
                  onClick={this.onClose.bind(this)}
                >
                  <i className="fa fa-close" />
                </div>
              </div>
              <div className="modal-body">
                <ProjectTreeComponent
                  projectId={this.state.projectId}
                  headerRef={this.headerTargetRef}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default ProjectTreePopupComponent;
