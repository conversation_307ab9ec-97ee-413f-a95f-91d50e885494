import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { featureActive } from '../../helpers/FeatureFlags';
import IdCardIconComponent from '../shared/IdCardIconComponent';
import StatusIconComponent from '../shared/StatusIconComponent';
import StatusMessages from '../shared/StatusMessages';
import { intlPropType } from '../i18n/IntlPropTypes';

class ProjectStatusChartLegendComponent extends React.Component {
  static get propTypes() {
    return {
      statuses: PropTypes.array.isRequired,
      visitorTotal: PropTypes.number.isRequired,
      nonPaedTotal: PropTypes.number.isRequired,
      handleOnClick: PropTypes.func,
      intl: intlPropType.isRequired
    };
  }

  render() {
    const { formatMessage } = this.props.intl;
    const { visitorTotal, nonPaedTotal, statuses } = this.props;
    const supplierTotal = statuses.reduce((sum, el) => sum + el.contractors, 0);
    return (
      <table id="project-status-chart-legend">
        <tbody>
          {statuses
            .filter(item => item.contractors > 0)
            .map(status => (
              <tr key={status.value} onClick={() => this.props.handleOnClick()}>
                <td className={`status-cell status-${status.value}`}>
                  <StatusIconComponent status={status.value} />
                </td>
                <td className="text-align">{status.contractors}</td>
                <td className={`status-cell status-${status.value}`}>
                  {formatMessage(StatusMessages[status.value])}
                </td>
              </tr>
            ))}
          <tr>
            <td className="status-cell" />
            <td className="text-align">{supplierTotal}</td>
            <td className="status-cell total">
              <FormattedMessage
                id="project.statuses.supplierTotal"
                description="Text indicating total suppliers"
                defaultMessage="Suppliers in total"
              />
            </td>
          </tr>
          {featureActive('visitors') && (
            <React.Fragment>
              <tr className="status-row visitors">
                <td>
                  <IdCardIconComponent />
                </td>
                <td className="status-cell text-align total">{visitorTotal}</td>
                <td className="status-cell visitors">
                  <FormattedMessage
                    id="project.statuses.visitorTotal"
                    description="Text indicating total of visitor nodes"
                    defaultMessage="Visiting companies"
                  />
                </td>
              </tr>
              {featureActive('non_paed_suppliers') && (
                <tr className="status-row nonpaed">
                  <td>
                    <IdCardIconComponent />
                  </td>
                  <td className="text-align">{nonPaedTotal}</td>
                  <td>
                    <FormattedMessage
                      id="project.statuses.nonPaedTotal"
                      description="Text indicating total of non-preannounced suppliers"
                      defaultMessage="Non-preannounced suppliers"
                    />
                  </td>
                </tr>
              )}
            </React.Fragment>
          )}
        </tbody>
      </table>
    );
  }
}

export default injectIntl(ProjectStatusChartLegendComponent);
