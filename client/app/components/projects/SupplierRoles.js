import { defineMessages } from 'react-intl';

export const MAIN_CONTRACTOR = 'main_contractor';
export const SUPERVISOR = 'supervisor';
export const SUPPLIER = 'supplier';

export const SupplierRolesMessages = defineMessages({
  main_contractor: {
    id: 'roles.supplier.main_contractor',
    description: 'Role of project supplier main_contractor',
    defaultMessage: 'Main contractor',
  },
  supervisor: {
    id: 'roles.supplier.supervisor',
    description: 'Role of project supplier supervisor',
    defaultMessage: 'Supervisor',
  },
  supplier: {
    id: 'roles.supplier.supplier',
    description: 'Role for project supplier supplier',
    defaultMessage: 'Supplier',
  },
});

export const SupplierRolesDescriptions = defineMessages({
  main_contractor: {
    id: 'roles.supplier.main_contractor.description',
    description: 'Role of project supplier main contractor described',
    defaultMessage: 'Read and edit access rights to the entire project supply chain',
  },
  supervisor: {
    id: 'roles.supplier.supervisor.description',
    description: 'Role of project supplier supervisor described',
    defaultMessage: 'Read and edit access rights to the entire project supply chain',
  },
  supplier: {
    id: 'roles.supplier.supplier.description',
    description: 'Role for project supplier supplier described',
    defaultMessage: 'No rights granted',
  },
});

export const SUPPLIER_ROLE_OPTIONS = {
  main_contractor: {
    value: MAIN_CONTRACTOR,
    title: SupplierRolesMessages.main_contractor,
    description: SupplierRolesDescriptions.main_contractor,
  },
  supervisor: {
    value: SUPERVISOR,
    title: SupplierRolesMessages.supervisor,
    description: SupplierRolesDescriptions.supervisor,
  },
  supplier: {
    value: SUPPLIER,
    title: SupplierRolesMessages.supplier,
    description: SupplierRolesDescriptions.supplier,
  },
};

export const SUPPLIER_ROLES = Object.keys(SUPPLIER_ROLE_OPTIONS);

export default SupplierRolesMessages;
