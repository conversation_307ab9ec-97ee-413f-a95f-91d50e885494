/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { defineMessages, injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import Datetime from 'react-datetime';

import { getISOCountry } from '../../helpers/Countries';
import TypeaheadInputComponent from '../shared/TypeaheadInputComponent';
import UserMessages from './ProjectUsersMessages';
import addSubcontractorsContactSearchChanged from '../../actions/actionCreators/AddSubcontractorsContactSearchChanged';
import { SUPPLIER_ROLE_OPTIONS, SUPPLIER_ROLES } from './SupplierRoles';
import LocalizedText from '../i18n/LocalizedText';
import SearchableField from '../shared/SearchableField';
import searchFieldChanged from '../../actions/actionCreators/SearchFieldChanged';
import searchFieldClearClicked from '../../actions/actionCreators/SearchFieldClearClicked';
import { ALL_SUPPLIER_CONTRACT_TYPES, SUPPLIER_CONTRACT_TYPES } from './SupplierContractTypes';
import supplierContractTypeChanged from '../../actions/actionCreators/SupplierContractTypeChanged';
import supplierRoleChanged from '../../actions/actionCreators/SupplierRoleChanged';
import DropdownMenuComponent from '../shared/dropdown/DropDownMenuComponent';
import ContractWorkAreasMsgs, { CONTRACT_WORK_AREAS } from './ContractWorkAreas';
import StoreSubscription from '../../helpers/StoreSubscription';
import { subcontractorFormStore } from '../../stores/Stores';
import supplierWorkAreasChanged from '../../actions/actionCreators/SupplierContractWorkAreasChanged';
import supplierContractStartDateChanged from '../../actions/actionCreators/SupplierContractStartDateChanged';
import supplierContractEndDateChanged from '../../actions/actionCreators/SupplierContractEndDateChanged';
import { intlPropType } from '../i18n/IntlPropTypes';
/* eslint-enable max-len */

export const SubcontractorFormMessages = defineMessages({
  companyBusinessIdLabel: {
    id: 'supplierForm.company_bussiness_id_label',
    description: 'Add supplier find Business ID label',
    defaultMessage: 'Business ID',
  },
  companyVATLabel: {
    id: 'supplierForm.VAT_label',
    description: 'Display supplier VAT label',
    defaultMessage: 'VAT',
  },
  companyBusinessIdOrVatLabel: {
    id: 'supplierForm.company_bussiness_id_or_vat_label',
    description: 'Add supplier find Business ID or VAT label',
    defaultMessage: 'Business ID or VAT',
  },
  addClientBusinessIDorVatLabel: {
    id: 'addSupplier.enter_company_business_id_label_for_add_client',
    description: 'Add client placeholder for Business ID or VAT',
    defaultMessage: 'Company Business ID or VAT',
  },
  companyNameLabel: {
    id: 'supplierForm.company_name_label',
    description: 'Add/edit subcontractor Company Name label',
    defaultMessage: 'Company name',
  },
  companyCountryLabel: {
    id: 'supplierForm.country_of_registration_label',
    description: 'Add/edit subcontractor Country of registration label',
    defaultMessage: 'Country of registration',
  },
  companyRolePlaceholder: {
    id: 'supplierForm.norole_placeholder',
    description: 'Add supplier supplier role empty placeholder',
    defaultMessage: 'Please select supplier type',
  },
  contractTypePlaceholder: {
    id: 'supplierForm.nocontractType_placeholder',
    description: 'Add supplier supplier contract type empty placeholder',
    defaultMessage: 'Select contract type',
  },
  enterSupplierContactEmail: {
    id: 'supplierForm.enter_contact_email_placeholder',
    description: 'Add/edit supplier: placeholder to enter company contact person email',
    defaultMessage: 'Fill in e-mail address for supplier',
  },
  contactPersonFindAdd: {
    id: 'addSupplier.contactPersonFindAdd',
    description: 'Find supplier contact person or add email field label',
    defaultMessage: 'Search for contact person or add email',
  },
  contactPersonFindAddPaProject: {
    id: 'addSupplier.contactPersonFindAddPaProject',
    description: 'Find supplier contact person or add email field label on pa projects',
    defaultMessage: 'Search for contact person or add email for preannouncements',
  },
  clearSearchTooltip: {
    id: 'addSupplier.clear_search_tooltip',
    description: 'Add supplier clear search button tooltip',
    defaultMessage: 'Clear search',
  },
  searchInProgressTooltip: {
    id: 'addSupplier.search_in_progress_tooltip',
    description: 'Add supplier: search in progress button tooltip',
    defaultMessage: 'Search in progress',
  },
  email_not_valid: {
    id: 'addSubcontractorForm.contactPersonSearch.emailNotValid',
    description: 'Error message for non valid email',
    defaultMessage: 'Please enter a valid email address',
  },
  contactPersonFindAddForClientLabel: {
    id: 'addSupplier.contact_person_find_add_for_client_label',
    description: 'Contact person input label for add client',
    defaultMessage: 'Contact person who should be given access to the project',
  },
  contactPersonFindAddForClientPlaceholder: {
    id: 'addSupplier.contact_person_find_add_for_client_placeholder',
    description: 'Contact person input placeholder for add client',
    defaultMessage: 'Fill in e-mail address for the project client',
  },
});
const messages = SubcontractorFormMessages;

export const SubcontractorFormFieldRO = props => (
  /* Render a read-only field for SubcontractorForm. */
  <div className="form-group pointer-events-none">
    <label className="form-control-label form-control-label-sm">{props.label}</label>
    <div className="form-control-static form-control-sm" id={props.fieldId}>
      {props.value}
    </div>
  </div>
);

SubcontractorFormFieldRO.propTypes = {
  fieldId: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
};

export const fieldErrorClass = (fieldName, errors) => {
  const fieldErrors = errors && errors[fieldName];
  return fieldErrors ? ' has-danger' : '';
};

export const FieldErrorMsg = props => {
  // eslint-disable-line react/no-multi-comp
  const fieldErrors = props.errors && props.errors[props.fieldName];
  if (!fieldErrors) {
    return null;
  }

  return (
    <div className="form-control-label form-control-label-sm">
      {fieldErrors.map((error, idx) => (
        <LocalizedText key={idx} translations={error} />
      ))}
    </div>
  );
};

FieldErrorMsg.propTypes = {
  errors: PropTypes.object,
  fieldName: PropTypes.string,
};

export const DropdownToggler = (
  props // eslint-disable-line react/no-multi-comp
) => (
  <button
    role="button"
    className={classNames(
      'form-control',
      'form-control-sm',
      'dropdown-toggle',
      'd-flex',
      'align-items-center'
    )}
    type="button"
    id={props.fieldId}
    disabled={props.disabled}
    aria-haspopup="true"
    aria-expanded="true"
  >
    <span className="text-left">{props.defaultValue}</span>
  </button>
);

DropdownToggler.propTypes = {
  fieldId: PropTypes.string.isRequired,
  defaultValue: PropTypes.string.isRequired,
  disabled: PropTypes.bool,
};

class SubcontractorFormRoleDropdown extends React.Component {
  // eslint-disable-line react/no-multi-comp

  static get propTypes() {
    return {
      togglerFieldId: PropTypes.string,
      roles: PropTypes.arrayOf(PropTypes.string).isRequired,
      roleSelected: PropTypes.oneOf(SUPPLIER_ROLES),
      toggleCallback: PropTypes.func,
      errors: PropTypes.shape({
        supplier_role: PropTypes.array,
      }),
      intl: intlPropType.isRequired
    };
  }

  options() {
    const options = {};
    this.props.roles.forEach(role => {
      options[role] = SUPPLIER_ROLE_OPTIONS[role];
    });

    return Object.values(options);
  }

  renderToggler() {
    const { formatMessage } = this.props.intl;
    const { roleSelected } = this.props;

    return (
      <DropdownToggler
        fieldId={this.props.togglerFieldId}
        defaultValue={
          roleSelected
            ? formatMessage(SUPPLIER_ROLE_OPTIONS[roleSelected].title)
            : formatMessage(messages.companyRolePlaceholder)
        }
      />
    );
  }

  renderHelp() {
    const option = SUPPLIER_ROLE_OPTIONS[this.props.roleSelected];
    if (!option) {
      return null;
    }

    const { formatMessage } = this.props.intl;

    return (
      <div className="field-help form-control-feedback pointer-events-none">
        <i className="fa fa-info-circle pr-3" />
        {formatMessage(option.description)}
      </div>
    );
  }

  render() {
    return (
      <div
        className={classNames('form-group', {
          'has-danger': this.props.errors && this.props.errors.supplier_role,
        })}
      >
        <label
          className="form-control-label form-control-label-sm"
          htmlFor="company_roles_dropdown"
        >
          <span className="text--red">*&nbsp;</span>
          <FormattedMessage
            id="supplierForm.supplier_role"
            description="Add/edit supplier supplier role label"
            defaultMessage="Supplier type"
          />
        </label>
        <DropdownMenuComponent
          id="company_roles_dropdown"
          options={this.options()}
          callback={supplierRoleChanged}
          toggler={this.renderToggler()}
          toggleCallback={this.props.toggleCallback}
          className="dropdown-role dropdown-role-"
        />
        {this.renderHelp()}
        <FieldErrorMsg fieldName="supplier_role" errors={this.props.errors} />
      </div>
    );
  }
}

class SubcontractorFormCountryDropdown extends React.Component {
  static DEFAULT_COUNTRY = 'se';

  static get propTypes() {
    return {
      // eslint-disable-next-line react/no-unused-prop-types
      togglerFieldId: PropTypes.string,
      reference: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
      onChange: PropTypes.oneOfType([PropTypes.func]),
      className: PropTypes.string,
      countries: PropTypes.array,
      disabled: PropTypes.bool,
      selectedCountry: PropTypes.string,
    };
  }

  static get defaultProps() {
    return {
      disabled: false,
      selectedCountry: SubcontractorFormCountryDropdown.DEFAULT_COUNTRY,
    };
  }

  componentDidMount() {
    if (this.countryRef) {
      this.onStateChange();
    }
  }

  onStateChange() {
    this.props.reference(this.countryRef.value);
    this.props.onChange(this.countryRef.value);
  }

  getISOcountryAlpha2(countryCode) {
    if (!countryCode) return SubcontractorFormCountryDropdown.DEFAULT_COUNTRY;
    const country = getISOCountry(countryCode);
    if (!country) return SubcontractorFormCountryDropdown.DEFAULT_COUNTRY;
    return country.alpha2.toLowerCase();
  }

  renderOptions(options) {
    return options.map(option => (
      <option key={option.code} value={option.code}>
        {option.name}
      </option>
    ));
  }

  render() {
    return (
      <div className={this.props.className}>
        <label htmlFor="country-dropdown" className="form-control-label form-control-label-sm">
          <span>
            <FormattedMessage
              id="country.registration.label"
              description="Label for supplier country"
              defaultMessage="Country of registration"
            />
          </span>
        </label>
        <select
          id="country-dropdown"
          className="form-control"
          onChange={this.onStateChange.bind(this)}
          ref={state => {
            this.countryRef = state;
          }}
          defaultValue={this.getISOcountryAlpha2(this.props.selectedCountry)}
          role="button"
          style={{ fontSize: '0.875rem' }}
          disabled={this.props.disabled}
        >
          {this.renderOptions(this.props.countries, '')}
        </select>
      </div>
    );
  }
}

class SubcontractorFormContractTypeDropdown extends React.Component {
  // eslint-disable-line react/no-multi-comp

  static get propTypes() {
    return {
      togglerFieldId: PropTypes.string,
      typeSelected: PropTypes.oneOf(ALL_SUPPLIER_CONTRACT_TYPES),
      toggleCallback: PropTypes.func,
      disabled: PropTypes.bool,
      errors: PropTypes.shape({
        contract_type: PropTypes.array,
      }),
      intl: intlPropType.isRequired
    };
  }

  renderToggler() {
    const { formatMessage } = this.props.intl;
    const { typeSelected } = this.props;

    return (
      <DropdownToggler
        fieldId={this.props.togglerFieldId}
        defaultValue={
          typeSelected
            ? formatMessage(SUPPLIER_CONTRACT_TYPES[typeSelected].title)
            : formatMessage(messages.contractTypePlaceholder)
        }
        disabled={this.props.disabled}
      />
    );
  }

  renderHelp() {
    const option = SUPPLIER_CONTRACT_TYPES[this.props.typeSelected];
    if (!option) {
      return null;
    }

    const { formatMessage } = this.props.intl;

    return (
      <div className="field-help form-control-feedback pointer-events-none">
        <i className="fa fa-info-circle pr-3" />
        {formatMessage(option.description)}
      </div>
    );
  }

  render() {
    return (
      <div
        className={classNames('form-group', {
          'has-danger': this.props.errors && this.props.errors.contract_type,
        })}
      >
        <label
          className="form-control-label form-control-label-sm"
          htmlFor="contract_types_dropdown"
        >
          <span className="text--red">*&nbsp;</span>
          <FormattedMessage
            id="supplierForm.supplier_contract_type"
            description="Add/edit contract type label"
            defaultMessage="Contract type"
          />
        </label>

        <DropdownMenuComponent
          id="contract_types_dropdown"
          options={Object.values(SUPPLIER_CONTRACT_TYPES)}
          callback={supplierContractTypeChanged}
          disabled={this.props.disabled}
          toggler={this.renderToggler()}
          toggleCallback={this.props.toggleCallback}
          className="dropdown-contract-type dropdown-contract-type-"
        />
        {this.renderHelp()}
        <FieldErrorMsg fieldName="contract_type" errors={this.props.errors} />
      </div>
    );
  }
}

class ContractDateRange extends React.Component {

  static get propTypes() {
    return {
      selectedContractStartDate: PropTypes.any,
      selectedContractEndDate: PropTypes.any,
      disabled: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  static defaultProps = {
    selectedContractStartDate: null,
    selectedContractEndDate: null,
    disabled: false,
  };

  constructor(props) {
    super(props);

    this.formSubscription = new StoreSubscription(
      subcontractorFormStore,
      this.formChanged.bind(this)
    );
    this.state = this.mapStoreToState(subcontractorFormStore.getState());
  }

  componentDidMount() {
    this.formSubscription.activate();
  }

  componentWillUnmount() {
    this.formSubscription.deactivate();
  }

  onStartDateChange(event) {
    let value;
    if (event.format) {
      // We will get moment object if date is picked from react-datetime picker.
      value = event.format('YYYY-MM-DD');
    }
    supplierContractStartDateChanged(value);
  }

  onEndDateChange(event) {
    let value;
    if (event.format) {
      // We will get moment object if date is picked from react-datetime picker.
      value = event.format('YYYY-MM-DD');
    }
    supplierContractEndDateChanged(value);
  }

  mapStoreToState(projectFormState, extraState = {}) {
    return {
      errors: projectFormState.errors,
      ...extraState,
    };
  }

  formChanged(/* storeState */) {
    this.setState(this.mapStoreToState(subcontractorFormStore.getState()));
  }

  renderReadonly(value) {
    return <div className="rtdPicker">{value}</div>;
  }

  renderDatetime(inputProps, value, onChange) {
    const { locale } = this.props.intl;
    return (
      <Datetime
        inputProps={inputProps}
        value={value}
        onChange={onChange}
        closeOnSelect
        timeFormat={false}
        dateFormat="YYYY-MM-DD"
        locale={locale}
        closeOnClickOutside
      />
    );
  }

  render() {
    const required = this.props.disabled ? null : <span className="text--red">*&nbsp;</span>;
    return (
      <div>
        <div
          className={classNames('form-group', {
            'has-danger': this.state.errors && this.state.errors.contract_start_date,
          })}
        >
          <label
            htmlFor="contract_start_date"
            className="w-100 form-control-label form-control-label-sm"
          >
            {required}
            <FormattedMessage
              id="supplierForm.startDate"
              description="Contract start date label"
              defaultMessage="Start date"
            />
          </label>
          {this.renderDatetime(
            {
              id: 'contract_start_date',
              name: 'contract_start_date',
              disabled: this.props.disabled,
            },
            this.props.selectedContractStartDate,
            this.onStartDateChange.bind(this)
          )}
          <FieldErrorMsg fieldName="contract_start_date" errors={this.state.errors} />
        </div>
        <div
          className={classNames('form-group', {
            'has-danger': this.state.errors && this.state.errors.contract_end_date,
          })}
        >
          <label
            htmlFor="contract_end_date"
            className="w-100 form-control-label form-control-label-sm"
          >
            {required}
            <FormattedMessage
              id="supplierForm.endDate"
              description="Contract end date label"
              defaultMessage="End date"
            />
          </label>
          {this.renderDatetime(
            {
              id: 'contract_end_date',
              name: 'contract_end_date',
              className: 'form-control',
              disabled: this.props.disabled,
            },
            this.props.selectedContractEndDate,
            this.onEndDateChange.bind(this)
          )}
          <FieldErrorMsg fieldName="contract_end_date" errors={this.state.errors} />
        </div>
      </div>
    );
  }
}

class WorkAreasCheckboxes extends React.Component {

  static get propTypes() {
    return {
      selectedWorkAreas: PropTypes.array,
      disabled: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  static defaultProps = {
    selectedWorkAreas: [],
    disabled: false,
  };

  constructor(props) {
    super(props);

    this.formSubscription = new StoreSubscription(
      subcontractorFormStore,
      this.formChanged.bind(this)
    );
    this.state = this.mapStoreToState(subcontractorFormStore.getState());
  }

  componentDidMount() {
    this.formSubscription.activate();
  }

  componentWillUnmount() {
    this.formSubscription.deactivate();
  }

  onVariantsChange(event) {
    const selectedWorkArea = event.target.value;
    let newWorkAreas = [
      ...(this.props.selectedWorkAreas || []),
      ...(this.state.contractWorkAreas || []),
    ];
    if (event.target.checked) {
      newWorkAreas = [...newWorkAreas, selectedWorkArea];
    } else {
      newWorkAreas = newWorkAreas.filter(e => e !== selectedWorkArea);
    }
    supplierWorkAreasChanged(newWorkAreas);
  }

  mapStoreToState(projectFormState, extraState = {}) {
    return {
      contractWorkAreas: projectFormState.contractWorkAreas,
      errors: projectFormState.errors,
      ...extraState,
    };
  }

  formChanged(/* storeState */) {
    this.setState(this.mapStoreToState(subcontractorFormStore.getState()));
  }

  renderVariants(workAreas) {
    const { formatMessage } = this.props.intl;

    return Object.values(workAreas).map((area, i) => (
      <div key={i}>
        <label
          id={`work-areas-label-wrapper-${area}`}
          className="custom-control custom-control-extra custom-checkbox"
        >
          <input
            id={`work-areas-checkbox-${i}`}
            type="checkbox"
            name={area}
            onChange={this.onVariantsChange.bind(this)}
            value={area}
            disabled={this.props.disabled}
            className="custom-control-input"
            checked={
              Array.isArray(this.state.contractWorkAreas) &&
              this.state.contractWorkAreas.includes(area)
            }
          />
          <span className="custom-control-indicator custom-form-indicator-extra" />
          <span className="custom-control-description text-fzsm">
            {formatMessage(ContractWorkAreasMsgs[area])}
          </span>
        </label>
      </div>
    ));
  }

  render() {
    const required = this.props.disabled ? null : <span className="text--red">*&nbsp;</span>;
    return (
      <div
        className={classNames('form-group', {
          'has-danger': this.state.errors && this.state.errors.contract_work_areas,
        })}
      >
        <label
          className="w-100 mb-2 form-control-label form-control-label-sm"
          htmlFor="supplierForm.workArea"
        >
          {required}
          <FormattedMessage
            id="supplierForm.workArea"
            description="Contract work areas label"
            defaultMessage="Professional work area"
          />
        </label>
        {this.renderVariants(CONTRACT_WORK_AREAS)}
        <FieldErrorMsg fieldName="contract_work_areas" errors={this.state.errors} />
      </div>
    );
  }
}

class EmailInput extends React.Component {
  // eslint-disable-line react/no-multi-comp

  static get propTypes() {
    return {
      onChange: PropTypes.func,
      errors: PropTypes.object,
      contactSelected: PropTypes.object,
      intl: intlPropType.isRequired
    };
  }

  getErrors() {
    let { errors } = this.props;
    errors = errors && errors.supplier_contacts;
    errors = errors && errors[0];
    return errors;
  }

  render() {
    const { formatMessage } = this.props.intl;
    const email = this.props.contactSelected && this.props.contactSelected.supplier_contact_email;
    const errorClass = fieldErrorClass('supplier_contact_email', this.getErrors());
    return (
      <div>
        <div className={`form-group mb-3${errorClass}`}>
          <label className="form-control-label form-control-label-sm" htmlFor="contact_email">
            <FormattedMessage
              id="addSupplier.contact_email_label"
              description="Add supplier contact person email label"
              defaultMessage="Supplier contact person email"
            />
          </label>
          <input
            className="form-control form-control-sm"
            type="text"
            id="contact_email"
            name="supplier_contact_email"
            placeholder={formatMessage(messages.enterSupplierContactEmail)}
            onChange={this.props.onChange}
            defaultValue={email || ''}
          />
          <FieldErrorMsg fieldName="supplier_contact_email" errors={this.getErrors()} />
        </div>
      </div>
    );
  }
}

class EmailSearchInput extends React.Component {
  // eslint-disable-line react/no-multi-comp

  static get propTypes() {
    return {
      companyUsers: PropTypes.arrayOf(PropTypes.object),
      contactSelected: PropTypes.object,
      errors: PropTypes.object,
      intl: intlPropType.isRequired
    };
  }

  onContactChange(event) {
    addSubcontractorsContactSearchChanged(event);
  }

  getContactPersonsOptions() {
    const { formatMessage } = this.props.intl;
    const usersInDropdown = this.props.companyUsers.map((user, index) => ({
      id: index,
      label: user.registered
        ? user.full_name
        : `(${formatMessage(UserMessages.pending_invitation)}) ${user.email}`,

      additional_text: user.has_bol_permission
        ? null
        : formatMessage(UserMessages.no_bol_permissions),
      email: user.registered ? user.email : null,
      contract_resource_id: user.contract_resource_id,
      org_id: user.org_id,
    }));

    return usersInDropdown;
  }

  getSelectedOption(options) {
    const { contactSelected } = this.props;
    if (!contactSelected || !options.length) return [];

    const allSelected = options.filter(o => o.email === contactSelected.supplier_contact_email);

    return allSelected.length ? [allSelected[0]] : [];
  }

  getErrors() {
    return this.props.errors;
  }

  render() {
    const options = this.getContactPersonsOptions();
    const selected = this.getSelectedOption(options);

    const errorClass = fieldErrorClass('supplier_contacts', this.getErrors());
    return (
      <div>
        <div className={`form-group mb-3${errorClass}`}>
          <label className="form-control-label form-control-label-sm" htmlFor="contact_email">
            <FormattedMessage
              id="addSupplier.contactPersonSelect"
              description="Select supplier contact person label"
              defaultMessage="Select supplier contact person"
            />
          </label>

          <TypeaheadInputComponent
            className="resize-inputs-wrapper-sm"
            id="contact_email"
            name="contact_email"
            onChange={this.onContactChange.bind(this)}
            disabled={false}
            options={options}
            multiple={false}
            selected={selected}
            defaultSelected={[]}
            emptyLabel=""
            placeholder=""
            maxResults={1000}
          />
          <FieldErrorMsg fieldName="supplier_contacts" errors={this.getErrors()} />
        </div>
      </div>
    );
  }
}

class SearchableFieldAction extends React.Component {
  static get propTypes() {
    return {
      search_in_progress: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  render() {
    const { formatMessage } = this.props.intl;
    return this.props.search_in_progress ? (
      <i
        className="fa fa-spin fa-spinner fa-lg text-primary"
        title={formatMessage(messages.searchInProgressTooltip)}
      />
    ) : (
      <i
        className="fa fa-times-circle text-muted"
        title={formatMessage(SubcontractorFormMessages.clearSearchTooltip)}
      />
    );
  }
}

class EmailVerifyInput extends React.Component {
  // eslint-disable-line react/no-multi-comp

  static get propTypes() {
    return {
      query: PropTypes.string,
      search_in_progress: PropTypes.bool,
      found: PropTypes.bool,
      not_found: PropTypes.bool,
      results: PropTypes.array,
      errors: PropTypes.array,
      required: PropTypes.bool,
      isProjectClient: PropTypes.bool,
      isPASubsupplier: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  onContactEmailChange(event) {
    const email = event.target.value;
    if (!email) {
      searchFieldClearClicked('contact_person_search_field');
      return;
    }

    let errors = null;
    const trimmedEmail = email.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
    if (!this.validEmail(trimmedEmail)) {
      const { locale, formatMessage } = this.props.intl;
      errors = { email: [{ [locale]: formatMessage(messages.email_not_valid) }] };
    }

    searchFieldChanged(trimmedEmail, 'contact_person_search_field', errors);
  }

  onContactPersonSearchClear(event) {
    event.preventDefault();
    searchFieldClearClicked('contact_person_search_field');
  }

  validEmail(email) {
    // eslint-disable-next-line max-len, no-useless-escape
    const re = /^(([^<>()\\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;
    return re.test(email);
  }

  renderSearchContactPersonError(errors) {
    if (!errors) {
      return null;
    }

    return <FieldErrorMsg fieldName="error" errors={{ error: errors }} />;
  }

  renderSearchContactPersonResult() {
    const { found, not_found: notFound } = this.props;

    if (!found && !notFound) {
      // search did not happen
      return null;
    }

    const userInfo = found ? this.props.results[0] : null;

    // let's check various error conditions
    const userIsInactive = userInfo && !userInfo.is_active;
    const userHasNoAccess = userInfo && !userInfo.has_bol_permission;

    if (this.props.isPASubsupplier && (notFound || userIsInactive || userHasNoAccess)) {
      const statusClass = 'alert-info';
      /* eslint-disable max-len */
      const problemMsg = (
        <FormattedMessage
          id="addSupplier.contact_person_waiting_for_supplier_confirmation"
          description="Contact person will be informed later message"
          defaultMessage="An email will be sent to the email address once the supplier is confirmed."
        />
      );
      /* eslint-enable max-len */
      return (
        <div className={classNames('form-control-feedback', statusClass)}>
          <i className="fa fa-info-circle pr-3" />
          {problemMsg}
        </div>
      );
    } else if (notFound) {
      const statusClass = 'alert-warning';
      const problemMsg = (
        <FormattedMessage
          id="addSupplier.contact_person_not_found"
          description="Contact person with this email not found message"
          defaultMessage="Contact not found. This user will not have access to service."
        />
      );
      return (
        <div className={classNames('form-control-feedback', statusClass)}>
          <i className="fa fa-info-circle pr-3" />
          {problemMsg}
        </div>
      );
    } else if (found) {
      let statusClass = 'alert-success';
      let problemMsg = '';
      if (userIsInactive) {
        statusClass = ' alert-warning';

        problemMsg = (
          <FormattedMessage
            id="addSupplier.contact_person_found_not_active"
            description="Contact person with this email found but not active message"
            defaultMessage="The user is disabled and will not be able to access the service."
          />
        );
      } else if (userHasNoAccess) {
        statusClass = ' alert-warning';
        problemMsg = (
          <FormattedMessage
            id="addSupplier.contact_person_found_without_permission"
            description="Contact person with this email found without BOL permission message"
            defaultMessage="This user has no access to the service."
          />
        );
      }

      return (
        <div
          className={classNames(
            'form-control-feedback',
            'form-group',
            'd-flex',
            statusClass
          )}
        >
          <i className="fa fa-info-circle pr-3" />
          <span>
            {problemMsg}{' '}
            <FormattedMessage
              id="addSupplier.contact_person_found"
              description="Contact person with this email found message"
              defaultMessage="Contact found"
            />
            <span>:</span>
            <br />
            {userInfo.full_name ? `${userInfo.full_name} ` : ''}
            {`<${userInfo.email}>`}
          </span>
        </div>
      );
    }

    return null;
  }

  render() {
    const { formatMessage } = this.props.intl;

    let formGroupClasses = 'form-group';
    if (this.props.errors) {
      formGroupClasses += ' has-danger';
    }

    const emailLabel = this.props.isProjectClient
      ? formatMessage(messages.contactPersonFindAddForClientLabel)
      : formatMessage(messages.contactPersonFindAdd);
    const emailLabelWithPa = this.props.isProjectClient
      ? formatMessage(messages.contactPersonFindAddForClientPlaceholder)
      : formatMessage(messages.contactPersonFindAddPaProject);

    return (
      <SearchableField
        className={formGroupClasses}
        id="contact_person_search_id"
        label={this.props.required ? emailLabelWithPa : emailLabel}
        input_value={this.props.query}
        onChange={this.onContactEmailChange.bind(this)}
        placeholder={
          this.props.isProjectClient
            ? formatMessage(messages.contactPersonFindAddForClientPlaceholder)
            : formatMessage(messages.enterSupplierContactEmail)
        }
        renderErrors={this.renderSearchContactPersonError(this.props.errors)}
        renderExtra={this.renderSearchContactPersonResult()}
        required={this.props.required}
      >
        <a
          href="#"
          className="input-group-addon bg-white no-decoration"
          id="clear_search_id"
          onClick={this.onContactPersonSearchClear.bind(this)}
        >
          <SearchableFieldActionWrapper search_in_progress={this.props.search_in_progress} />
        </a>
      </SearchableField>
    );
  }
}

class SubcontractorFormGeneralError extends React.Component {
  // eslint-disable-line react/no-multi-comp

  static get propTypes() {
    return {
      errors: PropTypes.object,
    };
  }

  render() {
    if (!this.props.errors) {
      return null;
    }

    if (this.props.errors.hasOwnProperty('message')) {
      // Generic HTTP errors end up as errors == {message: {en: '...', sv: '...'}}.
      return (
        <div className="text-danger mt-2">
          <LocalizedText translations={this.props.errors.message} />
        </div>
      );
    }
    if (this.props.errors.hasOwnProperty('general')) {
      // Generic HTTP errors end up as errors == {general: '...'}.
      return <div className="text-danger mt-2">{this.props.errors.general}</div>;
    }
    return null;
  }
}

/* eslint-disable max-len */
const SubcontractorFormRoleDropdownWrapper = injectIntl(SubcontractorFormRoleDropdown);
const SubcontractorFormCountryDropdownWrapper = injectIntl(SubcontractorFormCountryDropdown);
const SubcontractorFormContractTypeDropdownWrapper = injectIntl(SubcontractorFormContractTypeDropdown);
const ContractDateRangeWrapper = injectIntl(ContractDateRange);
const WorkAreasCheckboxesWrapper = injectIntl(WorkAreasCheckboxes);
const EmailInputWrapper = injectIntl(EmailInput);
const EmailSearchInputWrapper = injectIntl(EmailSearchInput);
const SearchableFieldActionWrapper = injectIntl(SearchableFieldAction);
const EmailVerifyInputWrapper = injectIntl(EmailVerifyInput);
const SubcontractorFormGeneralErrorWrapper = injectIntl(SubcontractorFormGeneralError);

export {
  SubcontractorFormRoleDropdownWrapper as SubcontractorFormRoleDropdown,
  SubcontractorFormCountryDropdownWrapper as SubcontractorFormCountryDropdown,
  SubcontractorFormContractTypeDropdownWrapper as SubcontractorFormContractTypeDropdown,
  ContractDateRangeWrapper as ContractDateRange,
  WorkAreasCheckboxesWrapper as WorkAreasCheckboxes,
  EmailInputWrapper as EmailInput,
  EmailSearchInputWrapper as EmailSearchInput,
  SearchableFieldActionWrapper as SearchableFieldAction,
  EmailVerifyInputWrapper as EmailVerifyInput,
  SubcontractorFormGeneralErrorWrapper as SubcontractorFormGeneralError
}

