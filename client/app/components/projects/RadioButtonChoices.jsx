/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';
/* eslint-enable max-len */

const RadioButtonChoices = ({ options, value, onChange, disabled, name, isBoolean, intl }) => {
  const { formatMessage } = intl;
  return (
    <div>
      {options.map(option => (
        <div key={isBoolean ? option.value.toString() : option.value}>
          <input
            type="radio"
            value={isBoolean ? option.value.toString() : option.value}
            name={name}
            id={option.id ? option.id : option.value}
            checked={isBoolean ? value === option.value : value === option.value.toString()}
            disabled={disabled}
            onChange={() => onChange(option.value)}
          />
          <label
            className="form-control-label inline"
            htmlFor={option.id ? option.id : option.value}
          >
            {formatMessage(option.message)}
          </label>
        </div>
      ))}
    </div>
  );
};

RadioButtonChoices.propTypes = {
  disabled: PropTypes.bool,
  name: PropTypes.string,
  options: PropTypes.array,
  onChange: PropTypes.func,
  value: PropTypes.any,
  isBoolean: PropTypes.bool,
  intl: PropTypes.object,
};

export default injectIntl(RadioButtonChoices);
