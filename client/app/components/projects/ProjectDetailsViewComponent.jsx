/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import {
  projectViewStore,
  projectStore,
  projectInternalIdSaveStore,
  companyViewContextualStore,
} from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import { featureActive } from '../../helpers/FeatureFlags';
import { isTreeEmpty, getFlatSuppliers } from '../../helpers/ProjectTree';
import ProjectModalComponent from './ProjectModalComponent';
import ProjectStatusChartComponent from './ProjectStatusChartComponent';
import { SUPPLIER_LINKED } from '../../Constants';
import AddSubcontractorInlineForm from './AddSubcontractorInlineForm';
import selectProject from '../../actions/actionCreators/SelectProject';
import projectInternalIdFormOpen from '../../actions/actionCreators/ProjectInternalIdFormOpen';
import projectInternalIdFormClose from '../../actions/actionCreators/ProjectInternalIdFormClose';
import projectInternalIdFormSave from '../../actions/actionCreators/ProjectInternalIdFormSave';
import addSubcontractorsOpenInline from '../../actions/actionCreators/AddSubcontractorsOpenInline';
import openBulkSupplierImport from '../../actions/actionCreators/OpenBulkSupplierImport';
import detailsViewClose from '../../actions/actionCreators/DetailsViewClose';
import openAddSingleSupplierView from '../../actions/actionCreators/AddSingleSupplierViewOpen';
import TabsComponent from '../shared/TabsComponent';
import TabComponent from '../shared/TabComponent';
import RelatedSuppliersListComponent from './RelatedSuppliersListComponent';
import ProjectUsersListComponent from './ProjectUsersListComponent';
import RemoveFlatSupplierDialogComponent from './RemoveFlatSupplierDialogComponent';
import RemoveProjectUserDialogComponent from './RemoveProjectUserDialogComponent';
import Spinner from '../shared/Spinner';
import CompanyDetailsContextualComponent from '../companies/CompanyDetailsContextualComponent';
import companyDetailsCloseClicked from '../../actions/actionCreators/CompanyDetailsCloseClicked';
import ProjectDetailsComponent from './ProjectDetailsComponent';
import subcontractorFormCloseClicked from '../../actions/actionCreators/SubcontractorFormCloseClicked';
import { getActiveOrganisationId } from '../../helpers/ApiClient';
import AddSubcontractorContextBlock from './AddSubcontractorContextBlock';
import SubcontractorFormEditComponent from './SubcontractorEditComponent';
import contextModalContainerClose from '../../actions/actionCreators/ContextModalContainerClose';
import AddedProjectClientBlockedModal from './AddedClientBlockedModal';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';

/* eslint-enable max-len */

class ProjectDetailsViewComponent extends React.Component {
  static get propTypes() {
    return {
      params: PropTypes.object.isRequired,
      formatting: PropTypes.string,
      router: routerPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.projectViewSub = new StoreSubscription(projectViewStore, this.storeChanged.bind(this));
    this.projectSub = new StoreSubscription(projectStore, this.storeChanged.bind(this));
    this.projectInternalIdSub = new StoreSubscription(
      projectInternalIdSaveStore,
      this.storeChanged.bind(this)
    );
    this.companyViewContextualStore = new StoreSubscription(
      companyViewContextualStore,
      this.storeChanged.bind(this)
    );
    this.state = this.mapStoreToState(
      projectViewStore.getState(),
      projectStore.getState(),
      projectInternalIdSaveStore.getState(),
      companyViewContextualStore.getState()
    );
  }

  componentDidMount() {
    this.projectViewSub.activate();
    this.projectSub.activate();
    this.projectInternalIdSub.activate();
    this.companyViewContextualStore.activate();
    this.loadProjectDetails();
    setTimeout(this.closeModals, 0);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.params.itemId !== this.props.params.itemId) {
      this.loadProjectDetails();
      setTimeout(projectInternalIdFormClose, 0);
      setTimeout(this.closeModals, 0);
    }
  }

  componentWillUnmount() {
    this.projectSub.deactivate();
    this.projectViewSub.deactivate();
    this.projectInternalIdSub.deactivate();
    this.companyViewContextualStore.deactivate();
    setTimeout(projectInternalIdFormClose, 0);
    setTimeout(this.closeModals, 0);
  }

  /* eslint-disable react/sort-comp */
  mapStoreToState(storeState, projectStoreState, internalIdState, detailsStoreState) {
    const projectTreeEmpty =
      storeState.project_tree_loaded && !storeState.project_tree_loading
        ? isTreeEmpty(storeState.project_tree.root)
        : false;

    /* eslint-disable indent */
    const suppliers =
      storeState.project_tree_loaded && storeState.project_tree.root
        ? getFlatSuppliers(
            storeState.project_tree.root.suppliers,
            storeState.project_tree.root.unlinked_suppliers,
            storeState.project_tree.root.project_responsible_org_name,
            storeState.project_tree.root.company_id
          )
        : [];
    /* eslint-enable indent */

    return {
      projectId: storeState.selected_project_id,
      name:
        storeState.name ||
        (projectStoreState.projectById[storeState.selected_project_id] || {}).name,
      internalId: storeState.project_id,
      taxId: storeState.tax_id,
      state: storeState.state,
      startDate: storeState.start_date,
      endDate: storeState.end_date,
      permissions: storeState.permissions,
      projectTreeEmpty,
      loading: storeState.project_tree_loading,
      loaded: storeState.project_tree_loaded && !storeState.added_client_blocked,
      failedToLoad: storeState.project_tree_failed,
      inlineAddSubcontractorOpen: storeState.inline_add_subcontractor_is_open,
      bulkSupplierAddOpen: storeState.bulk_supplier_add_is_open,
      singleSupplierAddOpen: storeState.single_supplier_add_is_open,
      internalIdForm: {
        formOpened: internalIdState.formOpened,
        formSaveInProgress: internalIdState.saveInProgress,
        formSaveFailed: internalIdState.saveFailed,
        formSaveSuccess: internalIdState.saveSuccess,
        errors: internalIdState.errors,
        errorsMetaData: internalIdState.errorsMetaData,
      },
      suppliers,
      company_details_target_ref: detailsStoreState.company_details_target_ref,
      company_details_is_open: detailsStoreState.company_details_is_open,
      company_details_company_id: detailsStoreState.company_details_company_id,
      company_details_company_name: detailsStoreState.company_details_company_name,
      confirmation_dialog_is_open: storeState.confirmation_dialog_is_open,
      isPAFormEnabledForProject: storeState.pa_form_enabled,
      subcontractor_edit_target_ref: storeState.subcontractor_edit_target_ref,
      subcontractor_edit_is_open: storeState.subcontractor_edit_is_open,
      subcontractor_edit_supplier_data: storeState.subcontractor_edit_supplier_data,
      subcontractor_edit_clicked_from_related: storeState.subcontractor_edit_clicked_from_related,
      isPaRestarting: storeState.isPaRestarting,
      addedClientBlocked: storeState.added_client_blocked,
      projectCreatorName: storeState.created_by_org_name,
      addedClientName: storeState.added_client_name,
    };
  }

  storeChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(
        projectViewStore.getState(),
        projectStore.getState(),
        projectInternalIdSaveStore.getState(),
        companyViewContextualStore.getState()
      )
    );
  }

  closeModals() {
    $('.modal:visible').modal('hide');
    companyDetailsCloseClicked();
    contextModalContainerClose();
  }

  isTreeEmpty() {
    return this.state.projectTreeEmpty || this.state.inlineAddSubcontractorOpen;
  }

  loadProjectDetails() {
    setTimeout(() => selectProject({ id: this.props.params.itemId }), 0);
  }

  openEditProject() {
    this.props.router.navigate(`/projects/${this.props.params.itemId}/edit`);
  }

  getCompanySuppliers(suppliers) {
    if (!suppliers) {
      return [];
    }

    const activeOrgId = getActiveOrganisationId();
    return suppliers.filter(s => s.organization.company_id === activeOrgId);
  }

  onDetailsClose() {
    projectInternalIdFormClose();
    this.closeModals();
    detailsViewClose();
    this.props.router.navigate('/projects');
  }

  handleInternalIdSubmit(event) {
    event.preventDefault();
    const formData = {
      project_id: event.target.internal_id.value,
    };
    projectInternalIdFormSave(this.state.projectId, formData);
  }

  handleInternalIdCancel(event) {
    event.preventDefault();
    projectInternalIdFormClose();
  }

  renderEditAction() {
    if (this.state.permissions.includes('edit_project')) {
      return (
        <div
          id="edit_project_button_title"
          className="ml-4"
          role="button"
          onClick={this.openEditProject.bind(this)}
        >
          <i className="fa fa-pencil fa-pr--bol" />
          <FormattedMessage
            id="project.editProjectButtonInTitle"
            description="Edit project button label in title bar."
            defaultMessage="Edit project"
          />
        </div>
      );
    }

    return null;
  }

  renderStatusChartSection() {
    if (!featureActive('suppliers')) return null;

    if (this.isTreeEmpty()) {
      return this.renderEmptyStatusChartSection(classNames('col-lg-6 col-xs-12'));
    }

    return (
      <div className={classNames('col-lg-7 col-xs-12')}>
        <div className="row pointer-events-none">
          <div className="col-5 card-title text-uppercase">
            <FormattedMessage
              id="projectDetails.statusHeading"
              description="Piechart legennd heading"
              defaultMessage="Project status"
            />
          </div>
          <div className="col-7 card-title text-center text-uppercase">
            <FormattedMessage
              id="projectDetails.supplyChainHeading"
              description="Piechart heading"
              defaultMessage="Supply chain"
            />
          </div>
        </div>
        <div>
          <ProjectStatusChartComponent />
        </div>
      </div>
    );
  }

  renderBulkSupplierMenu(message) {
    if (this.state.permissions.includes('bulk_import')) {
      return (
        <a
          className="add-multiple-link"
          href="#"
          onClick={(e) => {
            e.preventDefault(); openBulkSupplierImport(this.state.projectId)
          }}>
          <i className="fa fa-plus fa-pr--bol" />
          {message}
        </a>
      );
    }

    return null;
  }

  addSingleSupplierLink(message, companySupplierId = null) {
    return (
      <a
        id="add-single-link"
        href="#"
        onClick={(e) => {
          e.preventDefault();
          openAddSingleSupplierView(this.state.projectId, companySupplierId)
        }}
      >
        <i className="fa fa-plus fa-pr--bol" />
        {message}
      </a>
    );
  }

  renderAddSingleSupplierLink() {
    if (!this.state.permissions.includes('add_project_supplier')) {
      return null;
    }

    // Overzealous: should be checked by add_project_supplier
    if (!this.state.isPAFormEnabledForProject) {
      return null;
    }

    const companySuppliers = this.getCompanySuppliers(this.state.suppliers);

    let link = null;

    if (companySuppliers.length === 0) {
      // Will be a adding under root
      link = this.addSingleSupplierLink(
        <FormattedMessage
          id="projectDetails.singleSupplierAdd"
          description="Project details single supplier add link"
          defaultMessage="Add supplier"
        />,
        null
      );
    } else if (companySuppliers.length === 1) {
      const companySupplierId = companySuppliers[0].supplier_id;
      link = this.addSingleSupplierLink(
        <FormattedMessage
          id="projectDetails.singleSupplierPreannounce"
          description="Project details preannounce supplier link"
          defaultMessage="Preannounce subsupplier"
        />,
        companySupplierId
      );
    } else if (companySuppliers.length > 1) {
      // Multiple company supplier positions - refusing to guess one
      // Overzealous: should be checked by add_project_supplier
      link = null;
    }

    return link;
  }

  renderEmptyStatusChartSection() {
    if (this.state.inlineAddSubcontractorOpen) {
      return (
        <div className="col-lg-6 col-xs-12">
          <AddSubcontractorInlineForm
            supplierType={SUPPLIER_LINKED}
            projectId={this.state.projectId}
            permissions={this.state.permissions}
            closeCallback={subcontractorFormCloseClicked}
            isRoot
            extraClassNames={['grey']}
          />
        </div>
      );
    }
    return (
      <div className="col-lg-6 col-xs-12">
        <div className="card grey py-5 text-center empty-project-placeholder">
          <div className="pointer-events-none">
            <FormattedMessage
              id="projectDetails.emptyProjectMessage"
              description="Empty project message displayed in project details"
              defaultMessage="There are no suppliers added to this project."
            />
          </div>
          <div className="tree-icon mt-4">
            <i className="fa fa-sitemap fa-2x" />
          </div>
          <div className="mt-4">
            <a href="#" className="action" onClick={(e)=>{
              e.preventDefault()
              addSubcontractorsOpenInline()
            }}>
              <i className="fa fa-plus fa-pr--bol" />
              <FormattedMessage
                id="projectDetails.addFirstSupplier"
                description="Add first supplier message displayed when project is empty"
                defaultMessage="Add the first supplier"
              />
            </a>
          </div>
          <div className="mt-4">
            {this.renderBulkSupplierMenu(
              <FormattedMessage
                id="projectDetails.bulkSupplierAddEmptyProject"
                description="Project details bulk supplier add link"
                defaultMessage="Add multiple suppliers"
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  renderTabs() {
    const rowClass = this.props.formatting !== 'context-block' ? 'row' : '';
    // When project details are empty we hide "Project suppliers" tab so we
    // want "Project members" tab to be active.
    // Since project members is the only tab in that case
    // and for two tabs case first tab is "Project suppliers"
    // always set initial tab to 0
    const initialActiveTab = 0;
    return (
      <TabsComponent rowClass={rowClass} activeTab={initialActiveTab}>
        <TabComponent
          enabled={this.state.suppliers.length > 0 && featureActive('suppliers')}
          navClass="related-suppliers-tab"
          title={
            <FormattedMessage
              id="projectDetails.relatedSupplier"
              description="Label for project related suppliers"
              defaultMessage="Project suppliers"
            />
          }
        >
          <RelatedSuppliersListComponent
            projectId={this.state.projectId}
            suppliers={this.state.suppliers}
            selectedPA={
              this.props.params.itemId === this.state.projectId ? this.props.params.paId : null
            }
            permissions={this.state.permissions}
            renderBulkSupplierMenu={this.renderBulkSupplierMenu.bind(this)}
            renderAddSingleSupplierLink={this.renderAddSingleSupplierLink.bind(this)}
            isPAFormEnabledForProject={this.state.isPAFormEnabledForProject}
          />
        </TabComponent>
        <TabComponent
          enabled={featureActive('suppliers')}
          navClass="project-users-tab"
          title={
            <FormattedMessage
              id="projectDetails.projectMembers"
              description="Label for project members tab"
              defaultMessage="Project members"
            />
          }
        >
          <ProjectUsersListComponent
            projectId={this.state.projectId}
            permissions={this.state.permissions}
          />
        </TabComponent>
      </TabsComponent>
    );
  }

  renderModalContent(wrappedContent) {
    return (
      <div
        id="context-modal-dialog"
        className="modal in-project-details"
        tabIndex="-1"
        data-focus="false"
        aria-hidden="true"
        style={{ display: 'block' }}
      >
        {wrappedContent}
      </div>
    );
  }

  renderCompanyDetailsContextual() {
    if (!this.state.company_details_is_open) {
      return null;
    }
    const companyDetailsContext = (
      <CompanyDetailsContextualComponent
        targetRef={this.state.company_details_target_ref}
        isOpen={this.state.company_details_is_open}
        companyId={this.state.company_details_company_id}
        companyName={this.state.company_details_company_name}
        placement="left"
      />
    );
    return this.renderModalContent(companyDetailsContext);
  }

  renderCreateNewPa() {
    if (
      !this.state.subcontractor_edit_clicked_from_related ||
      !this.state.subcontractor_edit_is_open
    ) {
      return null;
    }

    const supplierData = this.state.subcontractor_edit_supplier_data;

    const addSubcontractorForm = (
      <AddSubcontractorContextBlock
        childComponent={SubcontractorFormEditComponent}
        targetRef={this.state.subcontractor_edit_target_ref}
        isOpen
        companyName={supplierData.companyName}
        supplierData={this.state.subcontractor_edit_supplier_data}
        supplierId={supplierData.supplierId}
        supplierType={supplierData.supplierType}
        projectId={this.state.projectId}
        isRoot={false}
        permissions={supplierData.permissions}
        placement="left"
        isPreannouncing
        isPaRestarting={this.state.isPaRestarting}
        isRelativeRestartPaModal
      />
    );
    return this.renderModalContent(addSubcontractorForm);
  }

  render() {
    if (this.state.loaded) {
      return (
        <ProjectModalComponent
          title={this.state.name}
          editAction={this.renderEditAction()}
          onDetailsClose={this.onDetailsClose.bind(this)}
          extraClassNames={{
            'project-details-view tabbed-details-view details-view': true,
          }}
        >
          <div>
            <div
              id="project_details_View"
              className={classNames('row u-border details-overview', {
                'details-loading': this.state.loading,
              })}
            >
              <div
                className={classNames('col-xs-12', {
                  'col-lg-5': !this.isTreeEmpty(),
                  'col-lg-6': this.isTreeEmpty(),
                })}
              >
                <div className="card noborder">
                  <div className="card-title text-uppercase pointer-events-none">
                    <FormattedMessage
                      id="projectDetails.detailsHeading"
                      description="Section subheading in the project popup"
                      defaultMessage="Project information"
                    />
                  </div>
                  <ProjectDetailsComponent
                    project={this.state}
                    internalIdForm={this.state.internalIdForm}
                    onInternalIdFormTriggerClick={projectInternalIdFormOpen}
                    onInternalIdFormSubmit={this.handleInternalIdSubmit.bind(this)}
                    onInternalIdFormCancel={this.handleInternalIdCancel.bind(this)}
                  />
                </div>
              </div>
              {this.renderStatusChartSection()}
            </div>

            {this.renderTabs()}
          </div>
          {this.renderCompanyDetailsContextual()}
          {this.renderCreateNewPa()}
          {this.state.confirmation_dialog_is_open &&
            this.renderModalContent(<RemoveFlatSupplierDialogComponent placement="left" />)}
          <RemoveProjectUserDialogComponent placement="left" />
        </ProjectModalComponent>
      );
    }
    if (
      featureActive('add_project_client') &&
      this.state.addedClientBlocked &&
      !this.state.loading
    ) {
      return (
        <AddedProjectClientBlockedModal
          title={this.state.name}
          projectId={this.props.params.itemId}
          onDetailsClose={this.onDetailsClose.bind(this)}
          extraClassNames={{
            'project-details-view tabbed-details-view details-view': true,
          }}
          projectCreatorName={this.state.projectCreatorName}
          addedClientName={this.state.addedClientName}
        />
      );
    }
    const loadingMessage = this.state.loading ? (
      <Spinner />
    ) : (
      <FormattedMessage
        id="projectDetails.loadingFailedMessage"
        description="Loading failed message in the project details box"
        defaultMessage="Loading failed"
      />
    );
    return (
      <ProjectModalComponent
        title={this.state.name || loadingMessage}
        onDetailsClose={this.onDetailsClose.bind(this)}
        extraClassNames={{
          'project-details-view tabbed-details-view details-view': true,
        }}
      >
        <div id="project_details_View" className="row details-loading u-border">
          <div className="col-12">
            <div className="row">{loadingMessage}</div>
          </div>
        </div>
      </ProjectModalComponent>
    );
  }
}

export default injectIntl(withRouter(ProjectDetailsViewComponent));
