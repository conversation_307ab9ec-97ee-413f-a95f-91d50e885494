/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl, defineMessages } from 'react-intl';
import { Link } from 'react-router-dom';
import Datetime from 'react-datetime';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import ProjectModalComponent from './ProjectModalComponent';
import LocalizedText from '../i18n/LocalizedText';
import { translateState } from './Messages';
import { projectViewStore, projectSaveStore, authStore } from '../../stores/Stores';
import saveProjectData from '../../actions/actionCreators/SaveProjectData';
import selectProject from '../../actions/actionCreators/SelectProject';
import projectFormClose from '../../actions/actionCreators/ProjectFormClose';
import projectFormOpen from '../../actions/actionCreators/ProjectFormOpen';
import projectFormStateChanged from '../../actions/actionCreators/ProjectFormStateChanged';
import projectFormEndDateChanged from '../../actions/actionCreators/ProjectFormEndDateChanged';
import projectFormPAFormEnabledChanged from '../../actions/actionCreators/ProjectFormPAEnabledChanged';

import StoreSubscription from '../../helpers/StoreSubscription';
import { preventDefault } from '../../helpers/EventHandlers';
import { featureActive } from '../../helpers/FeatureFlags';
import addSubcontractorsMenuClicked from '../../actions/actionCreators/AddSubcontractorsMenuClicked';
import projectClientEditMenuClicked from '../../actions/actionCreators/ProjectClientEditMenuClicked';
import { ORG_ROLE_CLIENT, ORG_ROLE_MAIN_CONTRACTOR, SUPPLIER_LINKED } from '../../Constants';
import AddSubcontractorContextBlock from './AddSubcontractorContextBlock';
import projectFormCreatorRoleChanged from '../../actions/actionCreators/ProjectFormCreatorRoleChanged';
import ProjectClientFormComponent from './ProjectClientFormComponent';
import RadioButtonChoices from './RadioButtonChoices';
import Spinner from '../shared/Spinner';
import {withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';
/* eslint-enable max-len */

const ID_REQUIRED_STATES = ['active'];

const messages = defineMessages({
  addedClientCanView: {
    id: 'projectForm.addedClientCanView',
    description: 'added client can view radio option label',
    defaultMessage: 'added client can view',
  },
  addedClientCannotView: {
    id: 'projectForm.addedClientCannotView',
    description: 'added client cannot view radio option label',
    defaultMessage: 'added client cannot view',
  },
});

export const mapStoreToState = (
  viewStoreState,
  saveStoreState,
  authStoreState,
  isProjectEditMode) => {
  const { selectedRepresentationIndex } = authStoreState.auth_info.topMenuParameters;
  const activeOrgName =
    authStoreState.auth_info.topMenuParameters.allRepresentations[selectedRepresentationIndex];
  const state = {
    name: viewStoreState.name,
    selectedProjectId: viewStoreState.selected_project_id,
    internalProjectId: viewStoreState.project_id,
    tax_id: viewStoreState.tax_id,
    state: viewStoreState.state,
    loading: viewStoreState.project_tree_loading,
    loaded: viewStoreState.project_tree_loaded && !viewStoreState.added_client_blocked,
    failedToLoad: viewStoreState.project_tree_failed || viewStoreState.added_client_blocked,
    start_date: viewStoreState.start_date,
    end_date: viewStoreState.end_date,
    auto_end_date: viewStoreState.auto_end_date,
    errors: saveStoreState.errors,
    saveInProgress: saveStoreState.saveInProgress,
    pa_form_enabled: viewStoreState.pa_form_enabled,
    projectCreatorRole: viewStoreState.project_creator_role,
    addedClientName: viewStoreState.added_client_name,
    addedClientCompanyId: viewStoreState.added_client_company_id,
    addedClientCompanyExternalId: viewStoreState.added_client_company_external_id,
    addedClientContactPersonEmail: viewStoreState.added_client_contact_person_email,
    addedClientContactPersonId: viewStoreState.added_client_contact_person_id,
    addedClientContactPersonFullName: viewStoreState.added_client_contact_person_full_name,
    add_subcontractor_is_open: viewStoreState.add_subcontractor_is_open,
    projectClientEditIsOpen: viewStoreState.project_client_edit_is_open,
    add_subcontractor_supplier_id: viewStoreState.add_subcontractor_supplier_id,
    add_subcontractor_company_name: viewStoreState.add_subcontractor_company_name,
    subcontractor_edit_supplier_data: viewStoreState.subcontractor_edit_supplier_data,
    add_subcontractor_supplier_type: viewStoreState.add_subcontractor_supplier_type,
    permissions: viewStoreState.permissions,
    createdByOrgName: viewStoreState.created_by_org_name
      ? viewStoreState.created_by_org_name
      : activeOrgName,
    addedClientCanViewInitial: viewStoreState.added_client_can_view,
  };
  // BOL-5988 prevent state.addedClientCanView from change its value during project saving
  if (isProjectEditMode && !saveStoreState.saveInProgress) {
    state.addedClientCanView = viewStoreState.added_client_can_view;
  }
  return state;
}

class ProjectFormComponent extends React.Component {
  static get propTypes() {
    return {
      params: PropTypes.object.isRequired,
      viewStoreState: PropTypes.object,
      saveStoreState: PropTypes.object,
      authStoreState: PropTypes.object,
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.viewStoreSub = new StoreSubscription(projectViewStore, this.storesChanged.bind(this));
    this.saveStoreSub = new StoreSubscription(projectSaveStore, this.storesChanged.bind(this));
    this.authStoreSub = new StoreSubscription(authStore, this.storesChanged.bind(this));
    this.state = mapStoreToState(
      projectViewStore.getState(),
      projectSaveStore.getState(),
      authStore.getState(),
      this.isProjectEditMode()
    );
    this.state = {
      ...this.state,
      no_tax_id: this.state.internalProjectId && !this.state.tax_id,
    };

    if(this.props.viewStoreState && this.props.saveStoreState && this.props.authStoreState) {
      mapStoreToState(
        this.props.viewStoreState,
        props.saveStoreState,
        this.props.authStoreState,
        this.isProjectEditMode())
    }
  }

  componentDidMount() {
    this.viewStoreSub.activate();
    this.saveStoreSub.activate();
    this.authStoreSub.activate();
    this._onAddedClientCanViewChange(false);
    this.loadProjectDetails();
    setTimeout(projectFormOpen, 0);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.params.itemId !== this.props.params.itemId) {
      this.loadProjectDetails();
    }
  }

  componentWillUnmount() {
    this.viewStoreSub.deactivate();
    this.saveStoreSub.deactivate();
    this.authStoreSub.deactivate();
    setTimeout(projectFormClose, 0);
  }

  onSubmit(event) {
    let projectData = {
      name: this.projectNameField.value,
      tax_id: this.taxIdField.value,
      project_id: this.internalIdField.value ? this.internalIdField.value : '',
      no_tax_id: this.noTaxIdField.checked,
      state: this.stateField.value,
      start_date: this.startDateField.value || '',
      end_date: this.state.end_date,
      pa_form_enabled: featureActive('pre_announcements') ? this.state.pa_form_enabled : null,
    };

    if (featureActive('add_project_client')) {
      projectData = {
        ...projectData,
        client_contact_person_email: this.state.addedClientContactPersonEmail,
      };

      projectData = {
        ...projectData,
        client_contact_person_id: this.state.addedClientContactPersonId,
      };

      if (!this.isProjectEditMode()) {
        projectData = {
          ...projectData,
          client_company_id: this.state.addedClientCompanyId,
          project_creator_role: this.state.projectCreatorRole,
        };
      }

      if (featureActive('block_project_client')) {
        projectData = {
          ...projectData,
          added_client_can_view: this.state.addedClientCanView,
        };
      }

      if (!this.props.params.itemId) {
        projectData = {
          ...projectData,
          client_company_external_id: this.state.addedClientCompanyExternalId,
        };
      }
    }

    preventDefault(event);
    saveProjectData(projectData, this.props.params.itemId, this.props.router.navigate);
  }

  onDetailsClose() {
    projectFormClose();
    this.props.router.navigate('/projects');
  }

  getStartDate() {
    let startDate = '';
    if (this.isProjectEditMode()) {
      startDate = this.state.start_date || '';
    } else {
      startDate = new Date().toISOString().slice(0, 10);
    }

    return startDate;
  }

  loadProjectDetails() {
    const projectId = this.props.params.itemId;
    if (this.isProjectEditMode() && projectViewStore.getState().selected_project_id !== projectId) {
      setTimeout(() => selectProject({ id: projectId }), 0);
    }
  }

  storesChanged() {
    this.setState(
      mapStoreToState(
        projectViewStore.getState(),
        projectSaveStore.getState(),
        authStore.getState(),
        this.isProjectEditMode()
      )
    );
  }

  hasError(fieldName) {
    return !!this.state.errors[fieldName];
  }

  _onAddedClientCanViewChange(value) {
    this.setState({
      addedClientCanView: value,
    });
  }

  _onProjectClientChange(e) {
    // change state for projectCreatorRole in ProjectViewStore
    projectFormCreatorRoleChanged(e.target.value);
  }

  _onNoTaxIdChange(event) {
    this.setState({
      no_tax_id: event.target.checked,
    });
  }

  _onRequirePAChange(event) {
    this.setState({
      pa_form_enabled: event.target.checked,
    });
    projectFormPAFormEnabledChanged(event.target.checked);
  }

  _onStateChange(event) {
    projectFormStateChanged(event.target.value, this.state.end_date, this.state.auto_end_date);
  }

  _onEndDateChange(event) {
    let value;

    if (event.target) {
      // We will get event.target.value if react-datetime picker is not loaded.
      ({ value } = event.target);
    } else if (event.format) {
      // We will get moment object if date is picked from react-datetime picker.
      value = event.format('YYYY-MM-DD');
    } else {
      // We will get string if entered date is invalid.
      value = event;
    }

    projectFormEndDateChanged(value);
  }

  isProjectEditMode() {
    // Is this a new project form or an edit existing project form?
    return !!this.props.params.itemId;
  }

  addProjectClientLink() {
    const addCallback = (e) => {
      e.preventDefault();
      addSubcontractorsMenuClicked(this.contextBlockTargetRef, {
        companyName: null,
        supplierId: null,
        supplierType: SUPPLIER_LINKED,
        isRoot: true,
        projectCreator: this.state.projectCreatorRole,
      });
    };

    return (
      <a id="add-project-client-link" href="#" onClick={(e) => addCallback(e)}>
        <i className="fa fa-plus fa-pr--bol" />
        <FormattedMessage
          id="projectForm.addProjectClientLink"
          description="Project form: add project client link name"
          defaultMessage="Add project client"
        />
      </a>
    );
  }

  editProjectClientLink() {
    const editCallback = (e) => {
      e.preventDefault();
      projectClientEditMenuClicked(this.contextBlockTargetRef, {
        companyName: null,
        supplierId: null,
        supplierType: SUPPLIER_LINKED,
        isRoot: true,
        projectCreator: this.state.projectCreatorRole,
      });
    };

    return (
      <div id="projectForm.project-client" className="name-email-vertical">
        <div id="projectForm.client-details" className="details inline-block">
          <div id="projectForm.client-name" className="name">
            {this.state.addedClientName}
          </div>
          <div id="projectForm.client-email" className="email">
            {this.state.addedClientContactPersonFullName
              ? `${this.state.addedClientContactPersonFullName} `
              : ''}
            {this.renderClientEmail()}
          </div>
        </div>
        <div id="projectForm.client-edit" className="inline-block top">
          <a
            id="projectForm.project-client-edit-link"
            href="#"
            onClick={(e) => editCallback(e)}
          >
            <i className="fa fa-pencil fa-pl--bol" />
          </a>
        </div>
      </div>
    );
  }

  renderClientEmail() {
    if (this.state.addedClientContactPersonEmail) {
      return <React.Fragment>&lt;{this.state.addedClientContactPersonEmail}&gt;</React.Fragment>;
    }
    return null;
  }

  renderSubcontractorForm() {
    let childComponent;
    if (this.state.add_subcontractor_is_open || this.state.projectClientEditIsOpen) {
      childComponent = ProjectClientFormComponent;
    } else {
      return null;
    }

    const projectClientForm = (
      <AddSubcontractorContextBlock
        childComponent={childComponent}
        targetRef={this.contextBlockTargetRef}
        isOpen
        companyName={this.state.add_subcontractor_company_name}
        supplierData={this.state.subcontractor_edit_supplier_data}
        supplierId={this.state.add_subcontractor_supplier_id}
        supplierType={this.state.add_subcontractor_supplier_type}
        projectId={this.state.selectedProjectId}
        isRoot={this.state.add_subcontractor_is_root}
        permissions={this.state.permissions}
        placement="bottom"
        isRelativeRestartPaModal={false}
        isAddProjectClient={this.state.add_subcontractor_is_open}
        isEditProjectClient={this.state.projectClientEditIsOpen}
      />
    );
    return (
      <div
        id="context-modal-dialog"
        className="modal in-project-details"
        tabIndex="-1"
        data-focus="false"
        aria-hidden="true"
        style={{ display: 'block' }}
      >
        {projectClientForm}
      </div>
    );
  }

  renderProjectClientWidget() {
    if (this.state.addedClientName) {
      return <div>{this.editProjectClientLink()}</div>;
    }
    return <div>{this.addProjectClientLink()}</div>;
  }

  renderError(fieldName) {
    if (!this.hasError(fieldName)) {
      return null;
    }

    return (
      <div className="err-msg pointer-events-none">
        {this.state.errors[fieldName].map((error, idx) => (
          <LocalizedText key={idx} translations={error} />
        ))}
      </div>
    );
  }

  renderTaxIdRequired() {
    if (!this.state.no_tax_id) {
      if (ID_REQUIRED_STATES.includes(this.state.state)) {
        return <span className="text--red">*&nbsp;</span>;
      }
    }

    return null;
  }

  renderProjectIdRequired() {
    if (this.state.no_tax_id) {
      if (ID_REQUIRED_STATES.includes(this.state.state)) {
        return <span className="text--red">*&nbsp;</span>;
      }
    }

    return null;
  }

  renderProjectClientChoice() {
    const isProjectEditMode = this.isProjectEditMode();

    if (!featureActive('add_project_client')) {
      return null;
    }
    const addedClientCanViewOptions = [
      {
        value: false,
        message: messages.addedClientCannotView,
        id: 'added_client_cannot_view_input',
      },
      {
        value: true,
        message: messages.addedClientCanView,
        id: 'added_client_can_view_input',
      },
    ];
    return (
      <div className="row">
        <div
          className={classNames('col-md-4', 'col-xl-3', 'form-group', {
            'has-danger': this.hasError('project_creator_role'),
          })}
        >
          <span className="text--red">*&nbsp;</span>
          <label className="form-control-label inline" id="projectForm.creator_is_label">
            {this.state.createdByOrgName}&nbsp;
            <FormattedMessage
              id="projectForm.companyNameIsLabel"
              description="Project form: `<company name> is:` label"
              defaultMessage="is:"
            />
          </label>
          <div id="project_client_option">
            <input
              type="radio"
              value={ORG_ROLE_CLIENT}
              name="project_client"
              id="project_client"
              checked={this.state.projectCreatorRole === ORG_ROLE_CLIENT}
              disabled={isProjectEditMode}
              onChange={this._onProjectClientChange.bind(this)}
            />
            <label className="form-control-label inline" htmlFor="project_client">
              <FormattedMessage
                id="projectForm.projectClientLabel"
                description="Project form: Project client label"
                defaultMessage="Project client"
              />
            </label>
          </div>
          <div id="project_main_contractor_option">
            <input
              type="radio"
              value={ORG_ROLE_MAIN_CONTRACTOR}
              name="main_contractor"
              id="main_contractor"
              checked={this.state.projectCreatorRole === ORG_ROLE_MAIN_CONTRACTOR}
              disabled={isProjectEditMode}
              onChange={this._onProjectClientChange.bind(this)}
            />
            <label className="form-control-label inline" htmlFor="main_contractor">
              <FormattedMessage
                id="projectForm.mainContractorLabel"
                description="Project form: Main contractor label"
                defaultMessage="Main contractor"
              />
            </label>
          </div>
          {this.renderError('project_creator_role')}
        </div>
        {this.state.projectCreatorRole === ORG_ROLE_MAIN_CONTRACTOR && (
          <div className="col-md-8 col-xl-9 form-group">
            <div
              className={classNames({
                'has-danger': this.hasError('client_company_id'),
              })}
            >
              <span className="text--red">*&nbsp;</span>
              <label className="form-control-label inline">
                <FormattedMessage
                  id="projectForm.projectClientLabel"
                  description="Project form: Project client label"
                  defaultMessage="Project client"
                />
              </label>
              {this.renderProjectClientWidget()}
              {this.renderError('client_company_id')}
            </div>
            {featureActive('block_project_client') && (
              <div className="row">
                <div className="col-xl-8 form-group">
                  <RadioButtonChoices
                    options={addedClientCanViewOptions}
                    value={!!this.state.addedClientCanView}
                    name="addedClientViewProjects"
                    disabled={this.state.addedClientCanViewInitial}
                    onChange={this._onAddedClientCanViewChange.bind(this)}
                    isBoolean
                  />
                </div>
                <div className="col-xl-4 form-group pointer-events-none">
                  <div
                    id="added_client_can_view_info_text"
                    className="field-help d-flex justify-content-start"
                  >
                    <i className="fa fa-info-circle pr-3" />
                    <FormattedMessage
                      id="projectForm.addedClientCanViewInfoLabel"
                      description="added client can see projects checkbox info text"
                      defaultMessage={
                        "If the project's client has been granted full transparency, " +
                        'it cannot be revoked later'
                      }
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        {this.renderError('Please select project role')}
      </div>
    );
  }

  renderFields() {
    const { formatMessage, locale } = this.props.intl;
    const isProjectEditMode = this.isProjectEditMode();
    return (
      <div id="form_content">
        <div
          className="row"
          ref={targetRef => {
            this.contextBlockTargetRef = targetRef;
          }}
        >
          <div
            className={classNames('form-group', 'col-xl-9', {
              'has-danger': this.hasError('name'),
            })}
          >
            <span className="text--red">*&nbsp;</span>
            <label htmlFor="project_name" className="form-control-label">
              <FormattedMessage
                id="projectForm.projectNameLabel"
                description="Project form: Project name label"
                defaultMessage="Project name"
              />
            </label>
            <input
              type="text"
              id="project_name"
              name="project_name"
              defaultValue={this.state.name}
              ref={elem => {
                this.projectNameField = elem;
              }}
              className="form-control"
            />
            {this.renderError('name')}
          </div>
          <div className="col-xl-3 form-group pointer-events-none">
            <div className="form-control-label hidden-lg-down">&nbsp;</div>
            <div className="field-help d-flex justify-content-start">
              <i className="fa fa-info-circle pr-3" />
              <FormattedMessage
                id="projectForm.projectNameDescription"
                description="Project form: Project name description"
                defaultMessage="Project name should be clear and descriptive of a project."
              />
            </div>
          </div>
        </div>
        {this.renderProjectClientChoice()}
        <div className="row">
          <div
            className={classNames('col-md-4', 'col-xl-3', 'form-group', {
              'has-danger': this.hasError('state'),
            })}
          >
            <span className="text--red">*&nbsp;</span>
            <label htmlFor="state" className="form-control-label">
              <FormattedMessage
                id="projectForm.stateLabel"
                description="Project form: state label"
                defaultMessage="State"
              />
            </label>
            <select
              id="state"
              name="state"
              role="button"
              className="form-control select"
              ref={elem => {
                this.stateField = elem;
              }}
              defaultValue={this.state.state || 'draft'}
              onChange={this._onStateChange.bind(this)}
            >
              <option value="active">{translateState(formatMessage, 'active')}</option>
              <option value="closed">{translateState(formatMessage, 'closed')}</option>
              <option value="draft">{translateState(formatMessage, 'draft')}</option>
            </select>
            {this.renderError('state')}
          </div>
          <div
            className={classNames('col-md-4', 'col-xl-3', 'form-group', {
              'has-danger': this.hasError('start_date'),
            })}
          >
            <label htmlFor="start_date" className="form-control-label">
              <FormattedMessage
                id="projectForm.startDate"
                description="Project form: Start date label"
                defaultMessage="Start date"
              />
            </label>
            <Datetime
              inputProps={{
                id: 'start_date',
                name: 'start_date',
                className: 'form-control',
                ref: elem => {
                  this.startDateField = elem;
                },
              }}
              initialValue={this.getStartDate()}
              closeOnSelect
              timeFormat={false}
              dateFormat="YYYY-MM-DD"
              locale={locale}
              className="d-inline"
            />
            {this.renderError('start_date')}
          </div>
          <div
            className={classNames('col-md-4', 'col-xl-3', 'form-group', {
              'has-danger': this.hasError('end_date'),
            })}
          >
            <label htmlFor="end_date" className="form-control-label">
              <FormattedMessage
                id="projectForm.endDate"
                description="Project form: End date label"
                defaultMessage="End date"
              />
            </label>
            <Datetime
              inputProps={{
                id: 'end_date',
                name: 'end_date',
                className: 'form-control',
                ref: elem => {
                  this.endDateField = elem;
                },
              }}
              value={this.state.end_date || ''}
              onChange={this._onEndDateChange.bind(this)}
              closeOnSelect
              timeFormat={false}
              dateFormat="YYYY-MM-DD"
              locale={locale}
            />
            {this.renderError('end_date')}
          </div>
        </div>
        <div className="row">
          <div
            className={classNames(
              'col-md-4',
              'col-xl-3',
              'form-group',
              'd-flex',
              'flex-column',
              'align-items-start',
              { 'has-danger': this.hasError('project_id') }
            )}
          >
            <div className="hidden-xl-up mt-auto" />
            <label htmlFor="internal_project_id" className="form-control-label">
              {this.renderProjectIdRequired()}
              <FormattedMessage
                id="projectForm.internalProjectIdLabel"
                description="Project form: Internal Project ID label"
                defaultMessage="Internal Project ID"
              />
            </label>
            <input
              type="text"
              id="internal_project_id"
              name="internal_project_id"
              defaultValue={this.state.internalProjectId}
              className="form-control"
              ref={elem => {
                this.internalIdField = elem;
              }}
            />
            {this.renderError('project_id')}
          </div>
          <div
            className={classNames(
              'col-md-4',
              'col-xl-3',
              'form-group',
              'd-flex',
              'flex-column',
              'align-items-start',
              { 'has-danger': this.hasError('tax_id') }
            )}
          >
            <div className="hidden-xl-up mt-auto" />
            <label htmlFor="tax_id" className="form-control-label">
              {this.renderTaxIdRequired()}
              <FormattedMessage
                id="projectForm.taxIdLabel"
                description="Project form: Construction site ID label"
                defaultMessage="Construction site ID"
              />
            </label>
            <input
              type="text"
              id="tax_id"
              name="tax_id"
              defaultValue={this.state.tax_id}
              className="form-control"
              ref={elem => {
                this.taxIdField = elem;
              }}
            />
            {this.renderError('tax_id')}
          </div>
          <div className="col-xl-3" />
          <div className="col-xl-3 form-group pointer-events-none">
            <div className="form-control-label hidden-lg-down">&nbsp;</div>
            <div className="field-help d-flex justify-content-start">
              <i className="fa fa-info-circle pr-3" />
              <FormattedMessage
                id="projectForm.taxIdDescription"
                description="Project form: Construction site ID description"
                defaultMessage={
                  'Internal Project ID (chosen freely) or ' +
                  'Construction site ID (provided by the tax office) ' +
                  'is required before the project can be made Active.'
                }
              />
            </div>
          </div>
        </div>
        <div className="row">
          <span className="col-12 form-group">
            <label className="custom-control custom-checkbox">
              <input
                id="no_tax_id"
                name="no_tax_id"
                type="checkbox"
                className="custom-control-input"
                defaultChecked={!!this.state.no_tax_id}
                onChange={this._onNoTaxIdChange.bind(this)}
                ref={elem => {
                  this.noTaxIdField = elem;
                }}
              />
              <span className="custom-control-indicator" />
              <span className="custom-control-description">
                {/* eslint-disable max-len */}
                <FormattedMessage
                  id="projectForm.noTaxIdLabel"
                  description="Project form: Construction site ID is not used label"
                  defaultMessage="This project does not require Construction Site ID"
                />
                {/* eslint-enable max-len */}
              </span>
            </label>
          </span>
        </div>
        {featureActive('pre_announcements') && (
          <div className="row">
            <span className="col-xl-9 form-group">
              <label id="pa_form_enabled_label" className="custom-control custom-checkbox">
                <input
                  id="pa_form_enabled"
                  name="pa_form_enabled"
                  type="checkbox"
                  className="custom-control-input"
                  defaultChecked={!!this.state.pa_form_enabled}
                  onChange={this._onRequirePAChange.bind(this)}
                  disabled={isProjectEditMode && featureActive('pa_form_checkbox_disabled')}
                />
                <span className="custom-control-indicator" />
                <span className="custom-control-description">
                  {/* eslint-disable max-len */}
                  <FormattedMessage
                    id="projectForm.requirePAcheckboxLabel"
                    description="Project form: project that will require ID06 Digital preannouncement checkbox label"
                    defaultMessage="This project uses ID06 Digital preannouncement"
                  />
                  {/* eslint-enable max-len */}
                </span>
              </label>
            </span>
            <div className="col-xl-3 form-group pointer-events-none">
              <div
                id="pa_form_enabled_info_text"
                className="field-help d-flex justify-content-start"
              >
                <i className="fa fa-info-circle pr-3" />
                <FormattedMessage
                  id="projectForm.paCheckboxInfoLabel"
                  description="Project form: Construction site ID description"
                  defaultMessage={
                    'It is not possible to change projects afterwards ' +
                    'to require preannouncement forms.'
                  }
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  renderActions() {
    const cancelLink = this.isProjectEditMode()
      ? `/projects/${this.props.params.itemId}`
      : '/projects';
    return (
      <div className="row">
        <div className="col-md-12 d-flex justify-content-center pt-4">
          <button
            id="project_form_save_button"
            className="btn btn-sm btn-primary mr-3"
            role="button"
            disabled={this.state.saveInProgress}
            type="submit"
          >
            <FormattedMessage
              id="projectForm.saveButton"
              description="Add project Save button label"
              defaultMessage="Save"
            />
          </button>
          <Link id="project_form_cancel" className="btn btn-sm btn-link" to={cancelLink}>
            <FormattedMessage
              id="projectForm.cancelAction"
              description="Project Form cancel action"
              defaultMessage="Cancel"
            />
          </Link>
        </div>
      </div>
    );
  }

  renderModalContent() {
    if (this.state.loading) {
      return <Spinner />;
    }
    if (this.state.failedToLoad) {
      return (
        <div id="project_form_failed_to_load">
          <FormattedMessage
            id="projectDetails.loadingFailedMessage"
            description="Loading failed message in the project details box"
            defaultMessage="Loading failed"
          />
        </div>
      );
    }
    return (
      <div className="details-overview">
        <div id="project_form_view">
          <span className="card-title text-uppercase pointer-events-none">
            <FormattedMessage
              id="projectForm.infoHeading"
              description="Section subheading in the project popup"
              defaultMessage="Project information"
            />
          </span>
          <span className="field-help pointer-events-none">
            <span className="text--red">&nbsp;&nbsp;&nbsp;*&nbsp;</span>
            <em>
              <FormattedMessage
                id="projectForm.compulsoryNote"
                description="Compulsory field note"
                defaultMessage="marks a compulsory field"
              />
            </em>
          </span>
          <form id="project_form" className="modal-form pt-4" onSubmit={this.onSubmit.bind(this)}>
            {this.renderFields()}
            {this.renderActions()}
          </form>
        </div>
        {this.renderSubcontractorForm()}
      </div>
    );
  }

  render() {
    const title = this.isProjectEditMode() ? (
      <FormattedMessage
        id="project.editProjectPanelTitle"
        description="Edit project panel title."
        defaultMessage="Edit project"
      />
    ) : (
      <FormattedMessage
        id="project.newProjectPanelTitle"
        description="New project panel title."
        defaultMessage="New project"
      />
    );
    return (
      <ProjectModalComponent
        title={title}
        onDetailsClose={this.onDetailsClose.bind(this)}
        extraClassNames={{ 'details-view': true }}
        extraBodyClassNames={{ 'u-border': true }}
      >
        {this.renderModalContent()}
      </ProjectModalComponent>
    );
  }
}

export default injectIntl(withRouter(ProjectFormComponent));
