import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import { featureActive } from '../../helpers/FeatureFlags';
import LocalizedText from '../i18n/LocalizedText';
import DetailsListItem from '../shared/DetailsListItem';
import { translateState } from './Messages';
import { intlPropType } from '../i18n/IntlPropTypes';

class ProjectDetailsComponent extends React.Component {
  static get propTypes() {
    return {
      printMode: PropTypes.bool,
      project: PropTypes.object.isRequired,
      internalIdForm: PropTypes.object,
      onInternalIdFormTriggerClick: PropTypes.func,
      onInternalIdFormSubmit: PropTypes.func,
      onInternalIdFormCancel: PropTypes.func,
      intl: intlPropType.isRequired
    };
  }

  static get defaultProps() {
    return {
      printMode: false,
      internalIdForm: {},
      onInternalIdFormTriggerClick: () => {},
      onInternalIdFormSubmit: () => {},
      onInternalIdFormCancel: () => {},
    };
  }

  hasInternalIdFormError = fieldName => !!this.props.internalIdForm.errors[fieldName];

  renderProjectIdEditInlineTrigger = () => (
    <span id="edit_internal_id_inline">
      <a href="#" onClick={(event) => {
        event.preventDefault();
        this.props.onInternalIdFormTriggerClick();
      }}>
        <i className="fa fa-pencil fa-pr--bol" />
      </a>
    </span>
  );

  renderProjectIdEditInlineForm = () => {
    const {
      project,
      internalIdForm,
      onInternalIdFormSubmit,
      onInternalIdFormCancel,
    } = this.props;
    const errorClass = this.hasInternalIdFormError('internalId') ? 'has-danger' : '';

    return (
      <span className="project-id">
        <form
          className={`inline ${errorClass}`}
          id="project_internalid_update"
          onSubmit={onInternalIdFormSubmit}
        >
          <input
            className="form-control"
            type="text"
            id="internal_id"
            name="internal_id"
            defaultValue={project.internalId}
          />
          <div className="inline-block d-flex align-items-center">
            <button
              className="btn btn-minimal btn-primary"
              id="internal_id_save"
              role="button"
              type="submit"
              disabled={internalIdForm.formSaveInProgress}
            >
              <i className="fa fa-check pr-0 pt-1 pb-1" />
            </button>
            <button
              className="btn btn-minimal btn-primary"
              id="internal_id_cancel"
              role="button"
              type="button"
              disabled={internalIdForm.formSaveInProgress}
              onClick={onInternalIdFormCancel}
            >
              <i className="fa fa-close pr-0 pt-1 pb-1" />
            </button>
          </div>
          <div className="clearfix" />
        </form>
      </span>
    );
  };

  renderInternalIdFormErrors = () => (
    <div className="row">
      <div className="col-lg-12 has-danger">
        <span>
          {this.props.internalIdForm.errors.project_id.map((error, idx) => (
            <LocalizedText key={idx} translations={error} />
          ))}
        </span>
      </div>
    </div>
  );

  render() {
    const { formatMessage } = this.props.intl;
    const { printMode, project } = this.props;
    const showReportLink = !printMode && featureActive('project_report');
    const enableEditing = !printMode && project.permissions.includes('edit_project');
    const enableEditingInternalId =
      !printMode && project.permissions.includes('edit_project_internal_id');
    const standaloneUrl = window.bolfak_config.standalone_url.replace(/\/$/, '');
    return (
      <div className="details-list--details-list">
        <DetailsListItem classNames="tax-id" printMode={printMode}>
          <FormattedMessage
            id="projectDetails.taxIdLabel"
            description="Label in front of the Construction site ID"
            defaultMessage="Construction site ID:"
          />
          {project.taxId}
        </DetailsListItem>
        <DetailsListItem classNames="state" printMode={printMode}>
          <FormattedMessage
            id="projectDetails.stateLabel"
            description="Label in front of the state"
            defaultMessage="State:"
          />
          {project.state ? translateState(formatMessage, project.state) : null}
        </DetailsListItem>
        <DetailsListItem classNames="internal-id" clickable={!printMode} printMode={printMode}>
          <FormattedMessage
            id="projectDetails.internalIdLabel"
            description="Label in front of the project ID"
            defaultMessage="Internal project ID:"
          />
          <span id="project-id" className="project-id">
            {enableEditingInternalId &&
              (project.internalIdForm.formOpened ? (
                this.renderProjectIdEditInlineForm()
              ) : (
                <span>
                  <span className="mr-4 pointer-events-none">{project.internalId}</span>
                  {this.renderProjectIdEditInlineTrigger()}
                </span>
              ))}
            {!enableEditingInternalId && (
              <span className="mr-4 pointer-events-none">{project.internalId}</span>
            )}
          </span>
        </DetailsListItem>
        {enableEditing &&
          this.hasInternalIdFormError('project_id') &&
          this.renderInternalIdFormErrors()}
        <DetailsListItem classNames="start-date" printMode={printMode}>
          <FormattedMessage
            id="projectDetails.startDateLabel"
            description="Label in front of project start date"
            defaultMessage="Start date:"
          />
          {project.startDate}
        </DetailsListItem>
        <DetailsListItem classNames="end-date" printMode={printMode}>
          <FormattedMessage
            id="projectDetails.endDateLabel"
            description="Label in front of project end date"
            defaultMessage="End date:"
          />
          {project.endDate}
        </DetailsListItem>
        {showReportLink && (
          <a
            href={`${standaloneUrl}/#/projects/${project.projectId}/report`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <i id="projectDetails.projectReportIcon" className="fa fa-file-pdf-o fa-pr--bol" />
            <FormattedMessage
              id="projectDetails.viewProjectReport"
              description="Text for project report link"
              defaultMessage="View project report"
            />
          </a>
        )}
      </div>
    );
  }
}

export default injectIntl(ProjectDetailsComponent);
