/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';

import Context<PERSON>lock from '../shared/ContextBlock';
import subcontractorFormCloseClicked from '../../actions/actionCreators/SubcontractorFormCloseClicked';
/* eslint-enable max-len */

class AddSubcontractorContextBlock extends React.Component {
  static get propTypes() {
    return {
      childComponent: PropTypes.func.isRequired,
      isOpen: PropTypes.bool,
      targetRef: PropTypes.object,
      companyName: PropTypes.node,
      supplierId: PropTypes.string,
      supplierType: PropTypes.string.isRequired,
      supplierData: PropTypes.object,
      projectId: PropTypes.string,
      isRoot: PropTypes.bool,
      permissions: PropTypes.arrayOf(PropTypes.string),
      placement: PropTypes.string,
      isPreannouncing: PropTypes.bool,
      isPaRestarting: PropTypes.bool,
      isRelativeRestartPaModal: PropTypes.bool,
      isAddProjectClient: PropTypes.bool,
      isEditProjectClient: PropTypes.bool,
    };
  }

  static get defaultProps() {
    return {
      isAddProjectClient: false,
      isEditProjectClient: false,
    };
  }

  constructor(props) {
    super(props);
    this.state = {
      enableContextBlockToggle: true,
    };
  }

  isPaRestartingModifier() {
    return {
      flip: {
        enabled: false,
      },
      preventOverflow: {
        enabled: true,
        escapeWithReference: false,
      },
      offset: {
        enabled: this.props.isRelativeRestartPaModal,
        offset: '0px, 200px',
      },
    };
  }

  handleToggle() {
    if (this.state.enableContextBlockToggle) {
      subcontractorFormCloseClicked();
    }
  }

  disableCtxBlockCallback(disable) {
    this.setState({
      enableContextBlockToggle: !disable,
    });
  }

  render() {
    if (!this.props.isOpen) {
      return null;
    }

    const modifier = this.isPaRestartingModifier();
    const { childComponent: ChildComponent } = this.props;
    return (
      <ContextBlock
        isOpen={this.props.isOpen}
        target={this.props.targetRef}
        title={ChildComponent.title(
          this.props.companyName,
          this.props.isPreannouncing,
          this.props.isPaRestarting,
          this.props.isEditProjectClient
        )}
        toggle={this.handleToggle.bind(this)}
        placement={this.props.placement}
        modifiers={modifier}
        increasedWidth={this.props.isAddProjectClient || this.props.isEditProjectClient}
      >
        <ChildComponent
          companyName={this.props.companyName}
          supplierData={this.props.supplierData}
          supplierId={this.props.supplierId}
          supplierType={this.props.supplierType}
          projectId={this.props.projectId}
          isRoot={this.props.isRoot}
          permissions={this.props.permissions}
          disableCtxBlockCallback={this.disableCtxBlockCallback.bind(this)}
          closeCallback={subcontractorFormCloseClicked}
          isPaRestarting={this.props.isPaRestarting}
        />
      </ContextBlock>
    );
  }
}

export default AddSubcontractorContextBlock;
