import PropTypes from 'prop-types';
import React from 'react';

import FormattedMessage from '../../components/i18n/FormattedMessage'
class AddProjectButton extends React.Component {
  static get propTypes() {
    return {
      onClick: PropTypes.func,
      id: PropTypes.string,
      showAddCompact: PropTypes.bool,
      narrow: PropTypes.bool,
    };
  }

  static get defaultProps() {
    return {
      showAddCompact: false,
      narrow: false,
    };
  }

  render() {
    const { id, showAddCompact, narrow } = this.props;
    if (narrow && !showAddCompact) return null;
    return (
      <button
        id={id}
        className="btn btn-sm btn-primary th-button"
        role="button"
        onClick={this.props.onClick}
      >
        <i className="fa fa-plus --bol" />
        {!showAddCompact && (
          <FormattedMessage
            id="projects.addNewProject"
            description="Add new project button label"
            defaultMessage="Add new project"
          />
        )}
        {showAddCompact && (
          <FormattedMessage
            id="projects.addNewProjectCompact"
            description="Add new project button label short"
            defaultMessage="Add"
          />
        )}
      </button>
    );
  }
}

export default AddProjectButton;
