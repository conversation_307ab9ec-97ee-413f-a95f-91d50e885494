/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import BootstrapTable from 'react-bootstrap-table-next';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage';
import StatusIconComponent from '../shared/StatusIconComponent';
import VisitorIconComponent from '../shared/VisitorIconComponent';
import ReportIconComponent from '../shared/ReportIconComponent';
import {
  companyRoleComparator,
  statusComparator,
} from '../../helpers/ProjectTree';
import {
  MAIN_CONTRACTOR,
  SUPERVISOR,
  SupplierRolesMessages,
} from './SupplierRoles';
import companyDetailsOpen
  from '../../actions/actionCreators/CompanyDetailsOpen';
import CompanyDetailsViewComponent
  from '../companies/CompanyDetailsViewComponent';
import selectCompanyDetails
  from '../../actions/actionCreators/CompanyDetailsSelect';
import companyProjectCommentsSelect
  from '../../actions/actionCreators/CompanyProjectCommentsSelect';
import openPreannouncementView
  from '../../actions/actionCreators/OpenPreannouncementView';
import { featureActive } from '../../helpers/FeatureFlags';
import projectTreeOpenReport
  from '../../actions/actionCreators/ProjectTreeOpenReport';
import projectSuppliersRemoveSupplier
  from '../../actions/actionCreators/ProjectSuppliersRemoveSupplier';
import TableMessages from '../shared/TableMessages';
import UserMessages from './ProjectUsersMessages';
import ContextualMenuComponent
  from '../shared/dropdown/ContextualMenuComponent';
import ContextualMenuItem from '../shared/dropdown/ContextualMenuItem';
import PASupplierStatusIconComponent from './PASupplierStatusIconComponent';
import { authStore } from '../../stores/Stores';
import { SUPPLIER_LINKED } from '../../Constants';
import contextModalContainerOpen
  from '../../actions/actionCreators/ContextModalContainerOpen';
import contextModalContainerClose
  from '../../actions/actionCreators/ContextModalContainerClose';
import contextModalContainerSubcontractorFormClose
  from '../../actions/actionCreators/ContextModalContainerSubcontractorFormClose';
import SubcontractorFormEditComponent from './SubcontractorEditComponent';
import ModalContextForm from './ModalContextForm';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';
import CommentIconComponent from '../shared/CommentIconComponent';

/* eslint-enable max-len */

class RelatedSuppliersListComponent extends React.Component {
  static get propTypes() {
    return {
      projectId: PropTypes.string,
      selectedPA: PropTypes.string,
      permissions: PropTypes.arrayOf(PropTypes.string),
      suppliers: PropTypes.array,
      renderBulkSupplierMenu: PropTypes.func,
      renderAddSingleSupplierLink: PropTypes.func,
      isPAFormEnabledForProject: PropTypes.bool,
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired,
    };
  }

  constructor(props) {
    super(props);
    this.contextBlockTargetRef = {};
  }

  componentDidMount() {
    if (this.props.selectedPA) {
      setTimeout(() => this.openSelectedPA(), 0);
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.selectedPA !== this.props.selectedPA) {
      if (this.props.selectedPA) {
        setTimeout(() => this.openSelectedPA(), 0);
      } else if (prevProps.selectedPA) {
        // NB: sometimes selectedPA changes from `undefined` to `null`, which
        // are !==, and that doesn't mean we need to close any PA models that
        // aren't even visible.
        console.log('Navigated away from a PA that was', prevProps.selectedPA);
        setTimeout(() => this.closePA(), 0);
      }
    }
  }

  statusFormatter(cell) {
    if (!cell) {
      return <VisitorIconComponent />;
    }
    return <StatusIconComponent status={cell} />;
  }

  preannouncementStatusFormatter(cell, row) {
    const currUserProfile = authStore.getState().profile;
    const isAssignedOrgSameAsCurrentOrg =
      row.pa_assigned_to_company_id === currUserProfile.organisation_id;
    const preannouncementCallback = () => this.openPreannouncement(row);
    return (
      <PASupplierStatusIconComponent
        status={cell}
        isAssignedOrgSameAsCurrentOrg={isAssignedOrgSameAsCurrentOrg}
        onClick={preannouncementCallback}
      />
    );
  }

  statusClassNameFormat(fieldValue) {
    return `company-status status-cell status-${fieldValue}`;
  }

  supplierSortFunc(a, b, order, _dataField, rowA, rowB) {
    let result = companyRoleComparator(rowA.supplier_role, rowB.supplier_role);
    if (result === 0) {
      result = statusComparator(a, b);
    }
    if (result === 0) {
      result = rowA.organization.name.localeCompare(rowB.organization.name);
    }
    return order === 'asc' ? result : -result;
  }

  supplierFormatter(cell) {
    return (
      <div
        onClick={() => {
          this.openCompany(cell.company_id);
        }}
      >
        <span className="stv-table-cell-title">{cell.name}</span>
        <br />
        <small className="sub-title text-muted">
          <span>
            {cell.gov_org_id}
            {cell.vat_number && ` / ${cell.vat_number}`}
          </span>
          {this.renderSupplierRole(cell.role)}
        </small>
      </div>
    );
  }

  buyerFormatter(cell) {
    return (
      <div
        onClick={() => {
          this.openCompany(cell.id);
        }}
      >
        {cell.name}
      </div>
    );
  }

  contactFormatter(cell) {
    if (!cell || !cell.length) {
      return null;
    }

    const contact = cell[0]; // take first contact

    if (contact.supplier_contact_name) {
      const { formatMessage } = this.props.intl;
      const userInfo = contact.is_person_registered
        ? contact.supplier_contact_name
        : `(${formatMessage(UserMessages.pending_invitation)})`;
      return (
        <div>
          <span className="stv-table-cell-title">{userInfo}</span>
          <br />
          <small className="sub-title text-muted">{contact.supplier_contact_email}</small>
        </div>
      );
    }
    return contact.supplier_contact_email;
  }

  reportFormatter(_cell, row) {
    return (
      <ReportIconComponent
        company_resource_id={row.organization.company_id}
        report_available={row.organization.report_available}
      />
    );
  }

  commentFormatter(cell) {
    if (!cell.has_comment) {
      return null;
    } 
    return (
      <div
        onClick={() => {
          console.log('Clicked on comment icon'); // Placeholder for future functionality
        }}
      >
        <CommentIconComponent
          has_comment={cell.has_comment}
          has_unread_comment={cell.has_unread_comment}
        />
      </div>
    );
  }

  openCompany(id) {
    companyDetailsOpen(id, this.props.router.navigate);
  }

  openPreannouncement(row) {
    openPreannouncementView(
      this.props.router.navigate,
      this.props.projectId,
      row.pa_id,
      row.pa_status,
      row.organization,
      row.buyer.name,
      row.supplier_id
    );
  }

  openSelectedPA() {
    if (this.props.selectedPA) {
      const row = this.props.suppliers.find(supplier => supplier.pa_id === this.props.selectedPA);
      if (row) {
        this.openPreannouncement(row);
      }
    }
  }

  closePA() {
    $('.modal:visible').modal('hide');
  }

  openCompanyDetails(row) {
    contextModalContainerOpen(
      <ModalContextForm
        childComponent={
          <CompanyDetailsViewComponent
            formatting="context-block"
            companyId={row.organization.company_id}
          />
        }
        doubleColumnWidth
        handleClose={contextModalContainerClose}
        title={
          row.organization.name ? (
            <div>{row.organization.name}</div>
          ) : (
            <FormattedMessage
              id="companyDetails.contextual_title"
              description="Company details popup title in context"
              defaultMessage="Company details"
            />
          )
        }
        outerClasses={classNames('company-details-view', 'details-view')}
      />
    );
    selectCompanyDetails(row.organization.company_id, row.supplier_id);
    companyProjectCommentsSelect(row.supplier_id);
  }

  createNewPaFormModalComponent(row) {
    const data = {
      projectId: this.props.projectId,
      companyName: row.organization.name,
      companyId: row.organization.company_id,
      govOrgId: row.organization.gov_org_id,
      vatNumber: row.organization.vat_number,
      country: row.organization.country,
      supplierId: row.supplier_id,
      supplierRev: row.supplier_rev,
      supplierType: SUPPLIER_LINKED,
      role: row.supplier_role,
      contractType: row.contract_type,
      contractStartDate: row.contract_start_date,
      contractEndDate: row.contract_end_date,
      contractWorkAreas: row.contract_work_areas,
      isOneManCompany: row.is_one_man_company,
      hasCollectiveAgreement: row.has_collective_agreement,
      collectiveAgreementName: row.collective_agreement_name,
      contacts: row.contacts,
      isRoot: false,
      permissions: row.permissions,
      preannouncementId: row.pa_id,
      isPreannoucing: true,
      isPaRestarting: true,
    };
    return (
      <ModalContextForm
        childComponent={
          <SubcontractorFormEditComponent
            companyName={data.companyName}
            supplierData={data}
            supplierId={data.supplierId}
            supplierType={data.supplierType}
            projectId={data.projectId}
            isRoot={data.isRoot}
            permissions={data.permissions}
            closeCallback={contextModalContainerSubcontractorFormClose}
            isPaRestarting={data.isPaRestarting}
            shouldCloseModalContainerOnSaveSuccess
          />
        }
        handleClose={contextModalContainerSubcontractorFormClose}
        title={SubcontractorFormEditComponent.title(
          data.companyName,
          data.isPreannoucing,
          data.isPaRestarting
        )}
      />
    );
  }

  openCreateNewPreannouncement(row) {
    contextModalContainerOpen(this.createNewPaFormModalComponent(row));
  }

  menuFormatter(cell, row, index) {
    const hasDetailsAction = row.permissions.includes('view_supplier');
    const hasCreateNewPaAction = row.permissions.includes('create_new_preannouncement');
    const hasPreannouncement =
      featureActive('pre_announcements') && this.props.isPAFormEnabledForProject && !!row.pa_status;
    const hasRemoveAction = row.permissions.includes('remove_supplier');
    const hasReport = row.organization.report_available;
    const removeCallback = () =>
      projectSuppliersRemoveSupplier(
        this.contextBlockTargetRef[index],
        this.props.projectId,
        row.supplier_id,
        row.supplier_rev,
        row.organization.name,
        row.children_count,
        row.children
      );

    const viewReportCallback = () => projectTreeOpenReport(row.organization.company_id);

    const detailsCallback = () => this.openCompanyDetails(row);

    const preannouncementCallback = () => this.openPreannouncement(row);

    const createNewPaCallback = () => this.openCreateNewPreannouncement(row);

    return (
      <div
        className="context-menu-trigger"
        ref={targetRef => {
          this.contextBlockTargetRef[index] = targetRef;
        }}
      >
        <ContextualMenuComponent>
          <ContextualMenuItem
            visible={hasRemoveAction}
            className="dropdown-add-supplier"
            onClick={removeCallback}
          >
            <FormattedMessage
              id="projects.nodemenu.removeSupplier"
              description="Remove supplier in project tree"
              defaultMessage="Remove supplier"
            />
          </ContextualMenuItem>
          <ContextualMenuItem
            visible={hasPreannouncement}
            className="dropdown-open-preannouncement"
            onClick={preannouncementCallback}
          >
            <FormattedMessage
              id="projects.nodemenu.openPreannouncement"
              description="Open preannouncement popup"
              defaultMessage="Open preannouncement"
            />
          </ContextualMenuItem>
          <ContextualMenuItem
            visible={hasCreateNewPaAction}
            className="dropdown-create-new-preannouncement"
            onClick={createNewPaCallback}
          >
            <FormattedMessage
              id="projects.nodemenu.createNewPreannouncement"
              description="Open new preannouncement popup"
              defaultMessage="Create new preannouncement"
            />
          </ContextualMenuItem>
          <ContextualMenuItem
            visible={hasDetailsAction}
            className="dropdown-company-details"
            onClick={detailsCallback}
          >
            <FormattedMessage
              id="projects.nodemenu.companyDetails"
              description="Show company details popup"
              defaultMessage="View company"
            />
          </ContextualMenuItem>
          <ContextualMenuItem
            visible={hasDetailsAction && hasReport}
            className="dropdown-company-report"
            onClick={viewReportCallback}
          >
            <FormattedMessage
              id="projects.nodemenu.viewReport"
              description="Show company report"
              defaultMessage="View report"
            />
          </ContextualMenuItem>
        </ContextualMenuComponent>
      </div>
    );
  }

  renderSupplierRole(role) {
    const { formatMessage } = this.props.intl;
    if ([MAIN_CONTRACTOR, SUPERVISOR].includes(role)) {
      return <span> – {formatMessage(SupplierRolesMessages[role])}</span>;
    }
    return null;
  }

  render() {
    const { formatMessage } = this.props.intl;
    const defaultSorted = [
      {
        dataField: 'status',
        order: 'asc',
      },
    ];
    const headerClasses = classNames('pointer-events-none');
    const columns = [
      {
        dataField: 'status',
        text: formatMessage(TableMessages.status),
        formatter: this.statusFormatter,
        sort: true,
        sortFunc: this.supplierSortFunc,
        columnClassName: this.statusClassNameFormat,
        headerClasses,
        headerAlign: 'left',
        align: 'center',
        style: { width: '55px' },
        formatExtraData: this.props.intl.locale,
      },
      {
        dataField: 'organization',
        text: formatMessage(TableMessages.organization),
        formatter: this.supplierFormatter.bind(this),
        formatExtraData: this.props.intl.locale,
        headerClasses,
      },
      {
        dataField: 'buyer',
        text: formatMessage(TableMessages.buyer),
        formatter: this.buyerFormatter.bind(this),
        headerClasses,
      },
      {
        dataField: 'pa_status',
        text: formatMessage(TableMessages.preannouncement),
        formatter: this.preannouncementStatusFormatter.bind(this),
        classNames: 'pa-status-column',
        formatExtraData: this.props.intl.locale,
        hidden: !(featureActive('pre_announcements') && this.props.isPAFormEnabledForProject),
      },
      {
        dataField: 'contacts',
        text: formatMessage(TableMessages.contacts),
        formatter: this.contactFormatter.bind(this),
        headerClasses,
        hidden: featureActive('pre_announcements') && this.props.isPAFormEnabledForProject,
      },
      {
        dataField: 'report_available',
        text: formatMessage(TableMessages.report),
        formatter: this.reportFormatter.bind(this),
        headerClasses,
        headerAlign: 'center',
        align: 'center',
        style: { width: '60px' },
        formatExtraData: this.props.intl.locale,
      },
      {
        dataField: 'comments',
        text: formatMessage(TableMessages.comments),
        formatter: this.commentFormatter.bind(this),
        formatExtraData: this.props.intl.locale,
        headerClasses,
        headerAlign: 'center',
        align: 'center',
        style: { width: '60px' },
        hidden: !(
          featureActive('project_supplier_comments') &&
          this.props.permissions.includes('view_supplier_comments')
        ),
      },
      {
        dataField: 'permissions',
        text: '',
        formatter: this.menuFormatter.bind(this),
        headerClasses,
        headerAlign: 'center',
        align: 'center',
        style: { width: '50px' },
      },
    ];

    return (
      <div>
        <div className="text-right">
          {this.props.renderBulkSupplierMenu(
            <FormattedMessage
              id="projectDetails.bulkSupplierAdd"
              description="Project details bulk supplier add link"
              defaultMessage="Add multiple suppliers"
            />
          )}
          {this.props.renderAddSingleSupplierLink()}
        </div>
        <BootstrapTable
          keyField="supplier_id"
          columns={columns}
          data={this.props.suppliers}
          striped
          hover
          bordered={false}
          defaultSorted={defaultSorted}
          defaultSortDirection="asc"
          noDataIndication={() => formatMessage(TableMessages.no_data_to_display)}
          wrapperClasses="project-suppliers"
          classes={classNames('table-striped--bol', 'table--bol-compact')}
          rowClasses="table-clickable-rows--bol"
        />
      </div>
    );
  }
}

export default injectIntl(withRouter(RelatedSuppliersListComponent));
