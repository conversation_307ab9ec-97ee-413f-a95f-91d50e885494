/* eslint-disable max-len */
import DetailsModalComponent from '../shared/DetailsModalComponent';
import closeDialog from '../../actions/actionCreators/CloseDialog';
import companyDetailsCloseClicked from '../../actions/actionCreators/CompanyDetailsCloseClicked';
import subcontractorFormCloseClicked from '../../actions/actionCreators/SubcontractorFormCloseClicked';
import contextModalContainerClose from '../../actions/actionCreators/ContextModalContainerClose';
/* eslint-enable max-len */

class ProjectModalComponent extends DetailsModalComponent {
  onKeyUp(event) {
    if (event.keyCode === 27) {
      // XXX: Needs refactoring once a proper solution for handling Esc at
      // different popup layers is figured out. This may become a furhter
      // code development hazard.
      // https://jira.tilaajavastuu.fi/browse/BOL-513
      if (this.contextBlocksVisible()) {
        subcontractorFormCloseClicked();
        contextModalContainerClose();
        closeDialog();
        companyDetailsCloseClicked();
      } else if (this.modalsVisible()) {
        $('.modal:visible').modal('hide');
      } else {
        event.preventDefault();
        this.closeDetails();
      }
    }
  }
}

export default ProjectModalComponent;
