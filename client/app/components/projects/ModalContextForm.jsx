import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

class ModalContextForm extends React.Component {
  static get propTypes() {
    return {
      childComponent: PropTypes.object.isRequired,
      handleClose: PropTypes.func,
      title: PropTypes.object,
      doubleColumnWidth: PropTypes.bool,
      outerClasses: PropTypes.string,
    };
  }

  static get defaultProps() {
    return {
      doubleColumnWidth: false,
      outerClasses: '',
    };
  }

  render() {
    return (
      <div
        className={classNames(
          {
            'single-column-width': !this.props.doubleColumnWidth,
            'double-column-width': this.props.doubleColumnWidth,
          },
          'modal-dialog',
          'context-block border',
          this.props.outerClasses
        )}
      >
        <div className="heading d-flex justify-content-between">
          <div id="modal-container-label" className="mr-5 pointer-events-none">
            {this.props.title}
          </div>
          <div>
            <a onClick={this.props.handleClose.bind(this)} role="button">
              <i className="fa fa-close" />
            </a>
          </div>
        </div>
        <div className="content">{this.props.childComponent}</div>
      </div>
    );
  }
}

export default ModalContextForm;
