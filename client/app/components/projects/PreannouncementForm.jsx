import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import PropTypes from 'prop-types';
import classNames from 'classnames';
import moment from 'moment';
import {
  authStore,
  companyViewStore,
  projectViewStore,
  paFormStore,
  cardsStore,
} from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import DetailsListItem from '../shared/DetailsListItem';
import selectCompanyDetails from '../../actions/actionCreators/CompanyDetailsSelect';
import CompanyStatusComponent from '../companies/CompanyStatusComponent';
import registerPreannouncement from '../../actions/actionCreators/RegisterPreannouncement';
import updatePreannouncementFormErrors from '../../actions/actionCreators/UpdatePaFormErrors';
import getPreannouncement from '../../actions/actionCreators/GetPreannouncement';
import cardsCountFetchStart from '../../actions/actionCreators/CardsCountFetchStart';
import canConfirmPreannouncement from '../../actions/actionCreators/CanConfirmPreannouncement';
import StatusMessages from '../shared/PAStatusMessages';
import { validEmail } from '../../helpers/MailValidator';
import { featureActive } from '../../helpers/FeatureFlags';
import PAFormMessages from './PAFormMessages';
import {
  PA_STATUS_CONFIRMED,
  PA_STATUS_REJECTED,
  PA_STATUS_REGISTERED,
  PA_STATUS_CREATED,
  PA_CONFIRM_STATE_ALLOW,
  PA_CONFIRM_STATE_WAIT,
  PA_CONFIRM_STATE_OVERRIDE,
} from '../../Constants';
import { isContractLongerThanGivenMonths } from '../../helpers/Preannouncements';
import ContractTypeMessages from './SupplierContractTypes';
import ContractWorkAreasMsgs from './ContractWorkAreas';
import Spinner from '../shared/Spinner';
import { id06FAQUrl } from '../HelpPageComponent';
import { FieldErrorMsg } from './SubcontractorFormSharedComponents';
import { intlPropType } from '../i18n/IntlPropTypes';

const paFormMessages = PAFormMessages;

const companyStatusCannotBeDeterminedMessage = (
  <FormattedMessage
    id="preannouncementDetails.pa_company_status_cannot_be_determined"
    description="Info message for when company report is not available"
    defaultMessage="Status cannot be determined because the report is
                missing. Check that there is support for monitoring the country
                of registration according to the {faq_link}. If there is support and the
                report is still missing, contact {mail_link}"
    values={{
      faq_link: (
        <a target="_blank" rel="noopener noreferrer" href={id06FAQUrl}>
          <FormattedMessage
            id="preannouncementDetails.pa_company_status_faq_link_text"
            defaultMessage="FAQ"
          />
        </a>
      ),
      mail_link: <a href="mailto:<EMAIL>"><EMAIL></a>,
    }}
  />
);

class PreannouncementForm extends React.Component {
  static get propTypes() {
    return {
      startSubmit: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.projectViewSub = new StoreSubscription(projectViewStore, this.storeChanged.bind(this));
    this.companyViewSub = new StoreSubscription(companyViewStore, this.storeChanged.bind(this));
    this.paFormSub = new StoreSubscription(paFormStore, this.storeChanged.bind(this));
    this.cardsStoreSub = new StoreSubscription(cardsStore, this.storeChanged.bind(this));
    this.state = this.mapStoreToState(
      projectViewStore.getState(),
      companyViewStore.getState(),
      paFormStore.getState(),
      authStore.getState(),
      cardsStore.getState()
    );
    this.state = { ...this.state, foremanEmailError: false };
    this.PA_STATUSES = {
      register: PA_STATUS_REGISTERED,
    };
  }

  componentDidMount() {
    this.projectViewSub.activate();
    this.companyViewSub.activate();
    this.paFormSub.activate();
    this.cardsStoreSub.activate();
    this.loadPreannouncementDetails();
    this.loadCompanyDetails();

    if (featureActive('skip_pa_reg_step')) {
      this.checkCanConfirmPreannouncement();
      this.timer = setInterval(() => {
        if (this.state.pa_can_confirm_status !== PA_CONFIRM_STATE_ALLOW) {
          this.checkCanConfirmPreannouncement();
        } else {
          clearInterval(this.timer);
        }
      }, 5000);
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.state.currentPaId !== prevState.currentPaId) {
      this.loadPreannouncementDetails();
      this.loadCompanyDetails();
    }
    if (this.props.startSubmit) {
      this.onSubmit();
    }
  }

  componentWillUnmount() {
    if (featureActive('skip_pa_reg_step')) {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = null;
    }
    this.projectViewSub.deactivate();
    this.companyViewSub.deactivate();
    this.paFormSub.deactivate();
    this.cardsStoreSub.deactivate();
  }

  onSubmit() {
    const { locale, formatMessage } = this.props.intl;
    const requiredMsg = [{ [locale]: formatMessage(paFormMessages.required) }];
    const errors = {};
    if (this.state.is_one_man_company === undefined) {
      errors.one_man_business = requiredMsg;
    }
    if (this.state.foreman_is_on_site === undefined) {
      errors.pa_foreman_on_site = requiredMsg;
    }
    if (
      this.state.is_one_man_company === false &&
      this.state.has_collective_agreement === undefined
    ) {
      errors.has_collective_agreement = requiredMsg;
    }
    if (this.state.foreman_is_on_site === true) {
      if (this.state.foreman_first_name === undefined) {
        errors.pa_foreman_first_name = requiredMsg;
      }
      if (this.state.foreman_last_name === undefined) {
        errors.pa_foreman_last_name = requiredMsg;
      }
      if (this.state.foreman_phone_number === undefined) {
        errors.pa_foreman_phone_number = requiredMsg;
      }
      if (this.state.foreman_email === undefined) {
        errors.pa_foreman_email = requiredMsg;
      }
    }

    if (
      this.state.company.country !== 'SWE' &&
      this.state.has_permanent_establishment === undefined
    ) {
      errors.has_permanent_establishment = requiredMsg;
    }
    if (
      this.state.has_collective_agreement === true &&
      this.state.collective_agreement_name == null
    ) {
      errors.collective_agreement_name = requiredMsg;
    }

    if (Object.keys(errors).length > 0) {
      updatePreannouncementFormErrors(
        errors,
        this.state.has_permanent_establishment,
        this.state.has_collective_agreement,
        this.state.is_one_man_company,
        this.state.foreman_is_on_site,
        this.state.foreman_first_name,
        this.state.foreman_last_name,
        this.state.foreman_email,
        this.state.foreman_phone_number,
        this.state.collective_agreement_name
      );
    } else {
      registerPreannouncement(
        this.state.currentPaId,
        this.PA_STATUSES.register,

        this.state.for_supplier_org.main_company_id,
        this.state.for_supplier_org.main_company_id_type,
        this.state.for_supplier_org.main_company_country,
        this.state.for_supplier_org.name,

        this.state.created_by_supplier_org.main_company_id,
        this.state.created_by_supplier_org.main_company_id_type,
        this.state.created_by_supplier_org.main_company_country,
        this.state.created_by_supplier_org.name,

        this.state.has_permanent_establishment,
        this.state.is_one_man_company,
        this.state.has_collective_agreement,
        this.state.collective_agreement_name !== '' ? this.state.collective_agreement_name : null,
        this.state.foreman_is_on_site,
        this.state.foreman_first_name,
        this.state.foreman_last_name,
        this.state.foreman_phone_number,
        this.state.foreman_email,
        this.state.contract_start_date,
        this.state.contract_end_date,
        this.state.contract_type,
        this.state.contract_work_areas
      );
    }
  }

  // eslint-disable-next-line react/sort-comp
  loadPreannouncementDetails() {
    getPreannouncement(this.state.currentPaId);
  }

  isEditMode() {
    const PA_STATUSES_READ = [PA_STATUS_REGISTERED, PA_STATUS_REJECTED, PA_STATUS_CONFIRMED];
    if (this.state.authProfile.organisation_id !== this.state.companyDetails.company_id) {
      PA_STATUSES_READ.push(PA_STATUS_CREATED);
    }
    return !PA_STATUSES_READ.includes(this.state.currentPAstatus);
  }

  mapStoreToState(projectState, companyState, paFormState, authState, cardsState) {
    return {
      authProfile: authState.profile,
      projectName: projectState.name,
      projectId: projectState.selected_project_id,
      currentPAstatus: paFormState.current_pa_status,
      companyDetails: paFormState.current_pa_company_details,
      currentPaId: paFormState.current_pa_id,
      preannouncement_buyer: paFormState.preannouncement_buyer,
      company: {
        companyId: companyState.company_id,
        name: companyState.name,
        govOrgIds: companyState.gov_org_ids,
        country: companyState.country,
        status: companyState.status,
        terminated: companyState.terminated,
        permissions: companyState.permissions,
        isReliablePartner: companyState.is_reliable_partner,
        hasSubsidiaries: companyState.has_subsidiaries,
        hasCombinedReport: companyState.has_combined_report,
        loaded: companyState.company_loaded,
      },
      assigned_to_company_id: paFormState.assigned_to_company_id,
      assigned_to_company: paFormState.assigned_to_company,
      assigned_to_time: paFormState.assigned_to_time,
      pa_form_main_contractors: paFormState.pa_form_main_contractors,
      pa_form_client: paFormState.pa_form_client,
      pa_form_contractor: paFormState.pa_form_contractor,
      created_by_supplier_org: paFormState.created_by_supplier_org,
      for_supplier_id: paFormState.for_supplier_id,
      for_supplier_org: paFormState.for_supplier_org,
      company_name: paFormState.company_name,
      company_gov_org_id: paFormState.company_gov_org_id,
      company_id_type: paFormState.company_id_type,
      company_country: paFormState.company_country,
      has_permanent_establishment: paFormState.has_permanent_establishment,
      is_one_man_company: paFormState.is_one_man_company,
      has_collective_agreement: paFormState.has_collective_agreement,
      collective_agreement_name: paFormState.collective_agreement_name,
      foreman_is_on_site: paFormState.foreman_is_on_site,
      foreman_first_name: paFormState.foreman_first_name,
      foreman_last_name: paFormState.foreman_last_name,
      foreman_phone_number: paFormState.foreman_phone_number,
      foreman_email: paFormState.foreman_email,
      contract_start_date: paFormState.contract_start_date,
      contract_end_date: paFormState.contract_end_date,
      contract_type: paFormState.contract_type,
      contract_work_areas: paFormState.contract_work_areas,
      confirmed_name: paFormState.confirmed_name,
      confirmed_gov_org_id: paFormState.confirmed_gov_org_id,
      confirmed_id_type: paFormState.confirmed_id_type,
      confirmed_time: paFormState.confirmed_time,
      rejected_name: paFormState.rejected_name,
      rejected_gov_org_id: paFormState.rejected_gov_org_id,
      rejected_id_type: paFormState.rejected_id_type,
      rejected_time: paFormState.rejected_time,
      submitted_time: paFormState.submitted_time,
      paUpdated: paFormState.preannouncement_updated,
      projectTree: projectState.project_tree,
      submitted_by_first_name: paFormState.submitted_by_first_name,
      submitted_by_last_name: paFormState.submitted_by_last_name,
      submitted_by_phone: paFormState.submitted_by_phone,
      submitted_by_email: paFormState.submitted_by_email,
      isContractLongerThanGivenMonths: isContractLongerThanGivenMonths(
        projectState.project_tree,
        paFormState.for_supplier_id
      ),
      paLoading: paFormState.pa_loading || !companyState.company_loaded,
      informantFirstName: paFormState.informant_supplier_first_name,
      informantLastName: paFormState.informant_supplier_last_name,
      informantEmail: paFormState.informant_supplier_email,
      informantPhone: paFormState.informant_supplier_phone,
      cardsCount: cardsState.cardsCount,
      cardsLoading: cardsState.loading,
      update_pa_form_errors: paFormState.update_pa_form_errors,
      skipPARegStep: featureActive('skip_pa_reg_step'),
      pa_can_confirm_status: paFormState.pa_can_confirm_status,
    };
  }

  storeChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(
        projectViewStore.getState(),
        companyViewStore.getState(),
        paFormStore.getState(),
        authStore.getState(),
        cardsStore.getState()
      )
    );
  }

  loadCompanyDetails() {
    const companyId = this.state.companyDetails.company_id;
    setTimeout(() => selectCompanyDetails(companyId), 0);
    setTimeout(() => cardsCountFetchStart(companyId), 0);
  }

  isAssignedToOrgSameAsActiveOrg() {
    return this.state.authProfile.organisation_id === this.state.assigned_to_company_id;
  }

  _setPermanentPlace(event) {
    this.setState({
      has_permanent_establishment: this.str2bool(event.target.value),
    });
  }

  _setOneManBusiness(event) {
    this.setState({ is_one_man_company: this.str2bool(event.target.value) });
  }

  _setCollectiveAgreement(event) {
    this.setState({
      has_collective_agreement: this.str2bool(event.target.value),
    });
  }

  _setCollectiveAgreementName(event) {
    this.setState({ collective_agreement_name: event.target.value });
  }

  _setForemanOnsite(event) {
    this.setState({ foreman_is_on_site: this.str2bool(event.target.value) });
  }

  _setForemanFirstName(event) {
    this.setState({ foreman_first_name: event.target.value });
  }

  _setForemanLastName(event) {
    this.setState({ foreman_last_name: event.target.value });
  }

  _setForemanPhone(event) {
    this.setState({ foreman_phone_number: event.target.value });
  }

  _setForemanEmail(event) {
    this.checkEmail(event);
    this.setState({ foreman_email: event.target.value });
  }

  renderDate(timestamp) {
    return moment(timestamp).format('YYYY-MM-DD');
  }

  str2bool(value) {
    if (value && typeof value === 'string') {
      if (value.toLowerCase() === 'true') return true;
      if (value.toLowerCase() === 'false') return false;
    }
    return value;
  }

  checkEmail(event) {
    const mailValue = event.target.value;
    const trimmedEmail = mailValue.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
    if (!validEmail(trimmedEmail)) {
      this.setState({
        foremanEmailError: true,
      });
    } else {
      this.setState({
        foremanEmailError: false,
      });
    }
  }

  editableCollectiveAgreementType() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        id="collective_agreement_type"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('collective_agreement_name'),
        })}
      >
        <span className="text--red">*&nbsp;</span>
        <label htmlFor="pa_collective_agreement_name">
          {formatMessage(paFormMessages.CollectiveAgreementName)}
        </label>
        <input
          name="pa_collective_agreement_name"
          id="pa_collective_agreement_name"
          className="form-control"
          type="text"
          placeholder={formatMessage(paFormMessages.CollectiveAgreementPlaceholder)}
          value={this.state.collective_agreement_name || ''}
          onChange={this._setCollectiveAgreementName.bind(this)}
        />
        <FieldErrorMsg
          fieldName="collective_agreement_name"
          errors={this.state.update_pa_form_errors}
        />
      </div>
    );
  }

  editableCollectiveAgreement() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        id="collective_agreement"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('has_collective_agreement'),
        })}
      >
        <div>
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.CollectiveAgreement)}
        </div>
        <div>
          <input
            type="radio"
            value="true"
            name="pa_collective_agreement"
            id="pa_collective_agreement_yes"
            checked={this.state.has_collective_agreement === true}
            onChange={this._setCollectiveAgreement.bind(this)}
          />
          <label className="pa_form_radio_label" htmlFor="pa_collective_agreement_yes">
            {formatMessage(paFormMessages.LabelYes)}
          </label>

          <input
            type="radio"
            value="false"
            name="pa_collective_agreement"
            id="pa_collective_agreement_no"
            checked={this.state.has_collective_agreement === false}
            onChange={this._setCollectiveAgreement.bind(this)}
          />
          <label htmlFor="pa_collective_agreement_no">
            {formatMessage(paFormMessages.LabelNo)}
          </label>
        </div>
        <FieldErrorMsg
          fieldName="has_collective_agreement"
          errors={this.state.update_pa_form_errors}
        />
      </div>
    );
  }

  collectiveAgreement() {
    const { formatMessage } = this.props.intl;

    return (
      <DetailsListItem id="collective_agreement_view_mode">
        {formatMessage(paFormMessages.CollectiveAgreement)}

        {this.state.has_collective_agreement === true
          ? formatMessage(paFormMessages.LabelYes)
          : formatMessage(paFormMessages.LabelNo)}
      </DetailsListItem>
    );
  }

  collectiveAgreementType() {
    const { formatMessage } = this.props.intl;

    return (
      <DetailsListItem id="collective_agreement_type_view_mode">
        {formatMessage(paFormMessages.CollectiveAgreementName)}

        {this.state.collective_agreement_name}
      </DetailsListItem>
    );
  }

  editableForemanOnSite() {
    const { formatMessage } = this.props.intl;
    return (
      <div
        id="pa_foreman_on_site"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('pa_foreman_on_site'),
        })}
      >
        <div>
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.ForemanOnSite)}
        </div>
        <input
          type="radio"
          value="true"
          name="pa_foreman_on_site"
          id="pa_foreman_on_site_yes"
          checked={this.state.foreman_is_on_site === true}
          onChange={this._setForemanOnsite.bind(this)}
        />
        <label className="pa_form_radio_label" htmlFor="pa_foreman_on_site_yes">
          {formatMessage(paFormMessages.LabelYes)}
        </label>
        <input
          type="radio"
          value="false"
          name="pa_foreman_on_site"
          id="pa_foreman_on_site_no"
          checked={this.state.foreman_is_on_site === false}
          onChange={this._setForemanOnsite.bind(this)}
        />
        <label htmlFor="pa_foreman_on_site_no">{formatMessage(paFormMessages.LabelNo)}</label>
        <FieldErrorMsg fieldName="pa_foreman_on_site" errors={this.state.update_pa_form_errors} />
      </div>
    );
  }

  foremanOnSite() {
    const { formatMessage } = this.props.intl;
    let label = '';
    if (this.state.foreman_is_on_site === true) {
      label = formatMessage(paFormMessages.LabelYes);
    } else if (this.state.foreman_is_on_site === false) {
      label = formatMessage(paFormMessages.LabelNo);
    }
    return (
      <DetailsListItem id="pa_foreman_on_site_view_mode">
        {formatMessage(paFormMessages.ForemanOnSite)}
        {label}
      </DetailsListItem>
    );
  }

  editableForemanName() {
    const { formatMessage } = this.props.intl;
    return (
      <div
        className={classNames({
          'has-danger': this.checkFormFieldErrors('pa_foreman_first_name'),
        })}
      >
        <span className="text--red">*&nbsp;</span>
        <label htmlFor="pa_foreman_first_name">{formatMessage(paFormMessages.ForemanName)}</label>
        <input
          name="pa_foreman_first_name"
          id="pa_foreman_first_name"
          className="form-control"
          type="text"
          value={this.state.foreman_first_name || ''}
          onChange={this._setForemanFirstName.bind(this)}
        />
        <FieldErrorMsg
          fieldName="pa_foreman_first_name"
          errors={this.state.update_pa_form_errors}
        />
      </div>
    );
  }

  editableForemanSurname() {
    const { formatMessage } = this.props.intl;
    return (
      <div
        className={classNames({
          'has-danger': this.checkFormFieldErrors('pa_foreman_last_name'),
        })}
      >
        <span className="text--red">*&nbsp;</span>
        <label htmlFor="pa_foreman_last_name">{formatMessage(paFormMessages.ForemanSurname)}</label>
        <input
          name="pa_foreman_last_name"
          id="pa_foreman_last_name"
          className="form-control"
          type="text"
          value={this.state.foreman_last_name || ''}
          onChange={this._setForemanLastName.bind(this)}
        />
        <FieldErrorMsg fieldName="pa_foreman_last_name" errors={this.state.update_pa_form_errors} />
      </div>
    );
  }

  editableForemanPhone() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        className={classNames({
          'has-danger': this.checkFormFieldErrors('pa_foreman_phone_number'),
        })}
      >
        <span className="text--red">*&nbsp;</span>
        <label htmlFor="pa_foreman_phone_number">
          {formatMessage(paFormMessages.ForemanPhone)}
        </label>
        <input
          name="pa_foreman_phone_number"
          id="pa_foreman_phone_number"
          className="form-control"
          type="text"
          value={this.state.foreman_phone_number || ''}
          onChange={this._setForemanPhone.bind(this)}
        />
        <FieldErrorMsg
          fieldName="pa_foreman_phone_number"
          errors={this.state.update_pa_form_errors}
        />
      </div>
    );
  }

  editableForemanEmail() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        className={classNames({
          'has-danger': this.checkFormFieldErrors('pa_foreman_email'),
        })}
      >
        <span className="text--red">*&nbsp;</span>
        <label htmlFor="pa_foreman_email">{formatMessage(paFormMessages.ForemanEmail)}</label>
        <input
          name="pa_foreman_email"
          id="pa_foreman_email"
          className="form-control"
          type="text"
          value={this.state.foreman_email || ''}
          onChange={this._setForemanEmail.bind(this)}
        />
        {this.state.foremanEmailError && (
          <div className="form-control-feedback err-msg">
            {formatMessage(paFormMessages.ForemanEmailError)}
          </div>
        )}
        <FieldErrorMsg fieldName="pa_foreman_email" errors={this.state.update_pa_form_errors} />
      </div>
    );
  }

  editableContactInfoMail() {
    const { formatMessage } = this.props.intl;

    return (
      <div>
        <span className="text--red">*&nbsp;</span>
        <label htmlFor="pa_contact_info_email">{formatMessage(paFormMessages.ContactEmail)}</label>
        <p className="field-help mb-3">{formatMessage(paFormMessages.ContactEmailHelpText)}</p>
        <input
          name="pa_contact_info_email"
          id="pa_contact_info_email"
          className="form-control"
          defaultValue={this.state.authProfile.email}
          type="text"
        />
      </div>
    );
  }

  editablePermanentPlace() {
    if (featureActive('skip_pa_reg_step')) {
      return null;
    }

    const { formatMessage } = this.props.intl;

    return (
      <div
        id="permanent_place"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('has_permanent_establishment'),
        })}
      >
        <div>
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.HasPermPlace)}
        </div>
        <input
          type="radio"
          value="true"
          name="permanent_place"
          id="permanent_place_yes"
          checked={this.state.has_permanent_establishment === true}
          onChange={this._setPermanentPlace.bind(this)}
        />
        <label className="pa_form_radio_label" htmlFor="permanent_place_yes">
          {formatMessage(paFormMessages.LabelYes)}
        </label>

        <input
          type="radio"
          value="false"
          name="permanent_place"
          id="permanent_place_no"
          checked={this.state.has_permanent_establishment === false}
          onChange={this._setPermanentPlace.bind(this)}
        />
        <label htmlFor="permanent_place_no">{formatMessage(paFormMessages.LabelNo)}</label>
        <FieldErrorMsg
          fieldName="has_permanent_establishment"
          errors={this.state.update_pa_form_errors}
        />
      </div>
    );
  }

  editableOneManBusiness() {
    const { formatMessage } = this.props.intl;
    return (
      <div
        id="one_man_business"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('one_man_business'),
        })}
      >
        <div>
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.OneManBusiness)}
        </div>
        <div
          className={
            this.checkFormFieldErrors('one_man_business')
              ? 'field-help-has-danger mb-3'
              : 'field-help mb-3'
          }
        >
          {formatMessage(paFormMessages.OneManBusinessHelptext)}
        </div>
        <input
          type="radio"
          value="true"
          name="one_man_business"
          id="one_man_business_yes"
          checked={this.state.is_one_man_company === true}
          onChange={this._setOneManBusiness.bind(this)}
        />

        <label className="pa_form_radio_label" htmlFor="one_man_business_yes">
          {formatMessage(paFormMessages.LabelYes)}
        </label>
        <input
          type="radio"
          value="false"
          name="one_man_business"
          id="one_man_business_no"
          checked={this.state.is_one_man_company === false}
          onChange={this._setOneManBusiness.bind(this)}
        />
        <label htmlFor="one_man_business_no">{formatMessage(paFormMessages.LabelNo)}</label>
        <FieldErrorMsg fieldName="one_man_business" errors={this.state.update_pa_form_errors} />
      </div>
    );
  }

  permanentPlace() {
    if (featureActive('skip_pa_reg_step')) {
      return null;
    }

    const { formatMessage } = this.props.intl;
    let label = '';

    if (this.state.has_permanent_establishment === true) {
      label = formatMessage(paFormMessages.LabelYes);
    } else if (this.state.has_permanent_establishment === false) {
      label = formatMessage(paFormMessages.LabelNo);
    }

    return (
      <DetailsListItem id="permanent_place_view_mode">
        {formatMessage(paFormMessages.HasPermPlace)}
        {label}
      </DetailsListItem>
    );
  }

  foremanName() {
    const { formatMessage } = this.props.intl;
    return (
      <DetailsListItem id="pa_foreman_first_name_view_mode">
        {formatMessage(paFormMessages.ForemanName)}
        {this.state.foreman_first_name}
      </DetailsListItem>
    );
  }

  foremanSurname() {
    const { formatMessage } = this.props.intl;
    return (
      <DetailsListItem id="pa_foreman_last_name_view_mode">
        {formatMessage(paFormMessages.ForemanSurname)}
        {this.state.foreman_last_name}
      </DetailsListItem>
    );
  }

  foremanPhone() {
    const { formatMessage } = this.props.intl;
    return (
      <DetailsListItem id="pa_foreman_phone_number_view_mode">
        {formatMessage(paFormMessages.ForemanPhone)}
        {this.state.foreman_phone_number}
      </DetailsListItem>
    );
  }

  foremanEmail() {
    const { formatMessage } = this.props.intl;
    return (
      <DetailsListItem id="pa_foreman_email_view_mode">
        {formatMessage(paFormMessages.ForemanEmail)}
        {this.state.foreman_email}
      </DetailsListItem>
    );
  }

  contactMail() {
    const { formatMessage } = this.props.intl;
    return (
      <DetailsListItem>
        {formatMessage(paFormMessages.ContactEmail)}
        {this.state.pa_contact_info_mail ? this.state.pa_contact_info_mail : 'None'}
      </DetailsListItem>
    );
  }

  oneManBusiness() {
    const { formatMessage } = this.props.intl;
    let label = '';
    if (this.state.is_one_man_company === true) {
      label = formatMessage(paFormMessages.LabelYes);
    } else if (this.state.is_one_man_company === false) {
      label = formatMessage(paFormMessages.LabelNo);
    }
    return (
      <DetailsListItem id="one_man_business_view_mode">
        <div>
          {formatMessage(paFormMessages.OneManBusiness)}
          <div className="field-help mb-3">
            {formatMessage(paFormMessages.OneManBusinessHelptext)}
          </div>
        </div>
        {label}
      </DetailsListItem>
    );
  }

  getSwedishFtax() {
    const orgIdFTax = this.state.company.govOrgIds.find(item => item.org_id_type === 'f-tax');
    const swedishFTax = orgIdFTax ? orgIdFTax.gov_org_id : '';

    return swedishFTax;
  }

  checkFormFieldErrors(fieldname) {
    if (this.state.update_pa_form_errors && fieldname in this.state.update_pa_form_errors) {
      return true;
    }
    return false;
  }

  checkCanConfirmPreannouncement() {
    if (this.state.company.loaded && !this.state.company.status) {
      canConfirmPreannouncement(this.state.currentPaId, this.state.companyDetails.company_id);
    }
  }

  confirmContactMail() {
    const { formatMessage } = this.props.intl;
    return (
      <div>
        {formatMessage(paFormMessages.ContactEmailConfirm)}

        <input
          name="pa_contact_info_email_confirm"
          className="form-control"
          defaultValue={this.state.authProfile.email}
          type="text"
        />
      </div>
    );
  }

  renderForemanDetails() {
    if (featureActive('skip_pa_reg_step')) {
      return null;
    }

    const { formatMessage } = this.props.intl;

    return (
      <div id="foreman_details" className="ro-list-block">
        <h4>{formatMessage(paFormMessages.ForemanHeader)}</h4>
        {this.isEditMode() ? this.editableForemanOnSite() : this.foremanOnSite()}
        {this.state.foreman_is_on_site && (
          <div>
            {this.isEditMode() ? this.editableForemanName() : this.foremanName()}
            {this.isEditMode() ? this.editableForemanSurname() : this.foremanSurname()}
            {this.isEditMode() ? this.editableForemanPhone() : this.foremanPhone()}
            {this.isEditMode() ? this.editableForemanEmail() : this.foremanEmail()}
          </div>
        )}
      </div>
    );
  }

  renderPaStatus(currentStatus) {
    const { formatMessage } = this.props.intl;
    if (currentStatus === PA_STATUS_CREATED && !this.isAssignedToOrgSameAsActiveOrg()) {
      return formatMessage(StatusMessages.waitingForRegistration);
    }
    if (currentStatus === PA_STATUS_CREATED && this.isAssignedToOrgSameAsActiveOrg()) {
      return formatMessage(StatusMessages.created);
    }
    if (currentStatus === PA_STATUS_REGISTERED && !this.isAssignedToOrgSameAsActiveOrg()) {
      return formatMessage(StatusMessages.inReview);
    }
    return formatMessage(StatusMessages[currentStatus]);
  }

  renderAssignedTo() {
    const { formatMessage } = this.props.intl;

    if ([PA_STATUS_REJECTED, PA_STATUS_CONFIRMED].includes(this.state.currentPAstatus)) {
      return null;
    }

    return (
      <DetailsListItem>
        {formatMessage(paFormMessages.PAAssignedTo)}
        {this.state.assigned_to_company && this.state.assigned_to_company.company_name}
      </DetailsListItem>
    );
  }

  renderAssignedToDate() {
    const { formatMessage } = this.props.intl;

    if ([PA_STATUS_REJECTED, PA_STATUS_CONFIRMED].includes(this.state.currentPAstatus)) {
      return null;
    }

    return (
      <DetailsListItem>
        {formatMessage(paFormMessages.PAAssignedToDate)}
        {this.renderDate(this.state.assigned_to_time)}
      </DetailsListItem>
    );
  }

  renderSubmittedDate() {
    const { formatMessage } = this.props.intl;

    if (!this.state.submitted_time) return null;

    return (
      <DetailsListItem>
        {formatMessage(paFormMessages.PASubmittedDate)}
        {this.renderDate(this.state.submitted_time)}
      </DetailsListItem>
    );
  }

  renderCompanyName(name, govOrgId) {
    return govOrgId ? `${name} (${govOrgId})` : name;
  }

  renderConfirmedRejected() {
    const { formatMessage } = this.props.intl;

    if (PA_STATUS_REJECTED === this.state.currentPAstatus) {
      return (
        <div className="pa_form_rejected">
          <DetailsListItem>
            {formatMessage(paFormMessages.PARejectedName)}
            {this.renderCompanyName(this.state.rejected_name, this.state.rejected_gov_org_id)}
          </DetailsListItem>
          <DetailsListItem>
            {formatMessage(paFormMessages.PARejectedDate)}
            {this.renderDate(this.state.rejected_time)}
          </DetailsListItem>
        </div>
      );
    } else if (PA_STATUS_CONFIRMED === this.state.currentPAstatus) {
      return (
        <div className="pa_form_confirmed">
          <DetailsListItem>
            {formatMessage(paFormMessages.PAConfirmedName)}
            {this.renderCompanyName(this.state.confirmed_name, this.state.confirmed_gov_org_id)}
          </DetailsListItem>
          <DetailsListItem>
            {formatMessage(paFormMessages.PAConfirmedDate)}
            {this.renderDate(this.state.confirmed_time)}
          </DetailsListItem>
        </div>
      );
    }

    return null;
  }

  renderPreannouncementData() {
    const currentStatus = this.state.currentPAstatus;
    const { formatMessage } = this.props.intl;

    return (
      <div id="preannoucement_data" className="ro-list-block">
        <h4>{formatMessage(paFormMessages.PAHeader)}</h4>
        <DetailsListItem>
          {formatMessage(paFormMessages.PAStatus)}
          {this.renderPaStatus(currentStatus)}
        </DetailsListItem>
        {this.renderAssignedTo()}
        {this.renderAssignedToDate()}
        {currentStatus === PA_STATUS_REGISTERED && (
          <DetailsListItem>{formatMessage(paFormMessages.PAReviewer)}</DetailsListItem>
        )}
        {this.renderConfirmedRejected()}
        {this.renderSubmittedDate()}
      </div>
    );
  }

  renderContactInfo() {
    return null; // Disabled until https://vaultit.atlassian.net/browse/BOL-4037
  }

  renderInformantSupplierInfo() {
    if (featureActive('skip_pa_reg_step')) {
      return null;
    }

    const { formatMessage } = this.props.intl;

    if (this.state.currentPAstatus === PA_STATUS_CREATED) {
      return null;
    }

    return (
      <div id="informant_supplier_info" className="ro-list-block">
        <h4>{formatMessage(paFormMessages.InformantHeader)}</h4>
        <DetailsListItem>
          {formatMessage(paFormMessages.InformantName)}
          {`${this.state.informantFirstName} ${this.state.informantLastName}`}
        </DetailsListItem>
        <DetailsListItem>
          {formatMessage(paFormMessages.InformantEmail)}
          {this.state.informantEmail}
        </DetailsListItem>
        <DetailsListItem>
          {formatMessage(paFormMessages.InformantPhone)}
          {this.state.informantPhone}
        </DetailsListItem>
      </div>
    );
  }

  renderInformantCustomerInfo() {
    const { formatMessage } = this.props.intl;
    if (this.state.submitted_by_first_name || this.state.submitted_by_last_name) {
      return (
        <div id="informant_customer_info" className="ro-list-block">
          <h4>{formatMessage(paFormMessages.InformantCustomerHeader)}</h4>
          <DetailsListItem>
            {formatMessage(paFormMessages.InformantName)}
            {`${this.state.submitted_by_first_name} ${this.state.submitted_by_last_name}`}
          </DetailsListItem>
          <DetailsListItem>
            {formatMessage(paFormMessages.InformantEmail)}
            {this.state.submitted_by_email}
          </DetailsListItem>
          <DetailsListItem>
            {formatMessage(paFormMessages.InformantPhone)}
            {this.state.submitted_by_phone}
          </DetailsListItem>
        </div>
      );
    }

    return null;
  }

  renderProjectInformation() {
    const { formatMessage } = this.props.intl;

    return (
      <div id="project_info" className="ro-list-block">
        <h4>{formatMessage(paFormMessages.ProjectInfoHeader)}</h4>

        <DetailsListItem>
          {formatMessage(paFormMessages.ProjectName)}
          {this.state.projectName}
        </DetailsListItem>

        <DetailsListItem>
          {formatMessage(paFormMessages.Client)}
          {this.state.pa_form_client}
        </DetailsListItem>

        {this.state.pa_form_main_contractors.length > 0 && (
          <DetailsListItem>
            {formatMessage(paFormMessages.MainContractor)}
            {this.state.pa_form_main_contractors.join(', ')}
          </DetailsListItem>
        )}

        <DetailsListItem>
          {formatMessage(paFormMessages.Contractor)}
          {this.state.pa_form_main_contractors.includes(this.state.pa_form_contractor)
            ? null
            : this.state.pa_form_contractor}
        </DetailsListItem>

        <DetailsListItem>
          {formatMessage(paFormMessages.Customer)}
          {this.state.preannouncement_buyer}
        </DetailsListItem>
      </div>
    );
  }

  renderContractInfo() {
    const { formatMessage } = this.props.intl;
    let workAreas = [];
    if (this.state.contract_work_areas) {
      workAreas = this.state.contract_work_areas.map(wa =>
        formatMessage(ContractWorkAreasMsgs[wa])
      );
    }
    return (
      <div id="contract_info" className="ro-list-block">
        <h4>{formatMessage(paFormMessages.ContractInfoHeader)}</h4>
        <DetailsListItem>
          {formatMessage(paFormMessages.ContractType)}
          {this.state.contract_type &&
            formatMessage(ContractTypeMessages[this.state.contract_type])}
        </DetailsListItem>
        {!this.state.skipPARegStep && (
          <React.Fragment>
            <DetailsListItem>
              {formatMessage(paFormMessages.ContractStartDate)}
              {this.state.contract_start_date}
            </DetailsListItem>
            <DetailsListItem>
              {formatMessage(paFormMessages.ContractEndDate)}
              {this.state.contract_end_date}
            </DetailsListItem>
          </React.Fragment>
        )}

        <DetailsListItem>
          {formatMessage(paFormMessages.ProfessionalWork)}
          {workAreas.join(', ')}
        </DetailsListItem>
      </div>
    );
  }

  shouldShowContractWarning() {
    return (
      this.isEditMode() &&
      this.state.company.country !== 'SWE' &&
      this.state.isContractLongerThanGivenMonths &&
      !this.state.has_permanent_establishment
    );
  }

  renderActiveCards(formatMessage) {
    return (
      <div id="active-cards" className="mb-6">
        <DetailsListItem>
          {formatMessage(paFormMessages.ActiveCards)}
          {this.state.cardsLoading ? (
            <Spinner id="acitve-cards-loading" className="text-left" />
          ) : (
            this.state.cardsCount
          )}
        </DetailsListItem>
      </div>
    );
  }

  renderCompanyStatusReportDetails() {
    const { formatMessage } = this.props.intl;
    if (this.state.company.status) {
      return <CompanyStatusComponent company={this.state.company} showStatusLabel={false} />;
    }

    if (featureActive('skip_pa_reg_step')) {
      if (this.state.pa_can_confirm_status === null) {
        return <Spinner id="pa-can-confirm-loading" className="text-left" />;
      }

      if (this.state.pa_can_confirm_status === PA_CONFIRM_STATE_WAIT) {
        return (
          <div>
            <span className="text--red">{formatMessage(paFormMessages.paCanConfirmWait)}</span>
            <div className="field-help mb-3">
              {formatMessage(paFormMessages.paCanConfirmWaitHelpText)}
            </div>
          </div>
        );
      }
      if (this.state.pa_can_confirm_status === PA_CONFIRM_STATE_OVERRIDE) {
        return (
          <div>
            <span className="text--red">{formatMessage(paFormMessages.paCanConfirmOverride)}</span>
            <div className="field-help mb-3">{companyStatusCannotBeDeterminedMessage}</div>
          </div>
        );
      }
    } else {
      return companyStatusCannotBeDeterminedMessage;
    }
    return null;
  }

  renderCompanyInfo() {
    const { formatMessage } = this.props.intl;
    // BOL-4671: show the name and ID of the company the way it was when the
    // preannouncement was registered, even if it changes later.
    const companyInfo =
      this.state.currentPAstatus === PA_STATUS_CREATED
        ? this.state.for_supplier_org
        : {
            name: this.state.company_name,
            main_company_id: this.state.company_gov_org_id,
            main_company_id_type: this.state.company_id_type,
            main_company_country: this.state.company_country,
            // We have no copy of the old value of the VAT number
            vat_number: '',
          };
    const canConfirmIsNotAllowed =
      this.state.pa_can_confirm_status === PA_CONFIRM_STATE_OVERRIDE ||
      this.state.pa_can_confirm_status === PA_CONFIRM_STATE_WAIT;
    return (
      <div id="company_info" className="ro-list-block">
        <h4>{formatMessage(paFormMessages.CompanyInfoHeader)}</h4>
        <DetailsListItem>
          {formatMessage(paFormMessages.CompanyName)}
          {companyInfo.name || ''}
        </DetailsListItem>

        <DetailsListItem>
          {formatMessage(paFormMessages.BusinessId)}
          {companyInfo.main_company_id_type === 'registration_number'
            ? companyInfo.main_company_id || ''
            : ''}
        </DetailsListItem>

        <DetailsListItem>
          {formatMessage(paFormMessages.VAT)}
          {companyInfo.main_company_id_type === 'vat_number'
            ? companyInfo.main_company_id || ''
            : companyInfo.vat_number || ''}
        </DetailsListItem>

        <DetailsListItem>
          {formatMessage(paFormMessages.RegistrationCountry)}
          {companyInfo.main_company_country || ''}
        </DetailsListItem>

        <DetailsListItem classNames="pa_company_status" clickable>
          <div>
            {canConfirmIsNotAllowed ? (
              <span className="text--red">{formatMessage(paFormMessages.CompanyStatus)}</span>
            ) : (
              formatMessage(paFormMessages.CompanyStatus)
            )}
          </div>
          {this.renderCompanyStatusReportDetails()}
        </DetailsListItem>
        {this.state.company.country !== 'SWE' ? this.renderSwedishFtax() : null}

        {this.renderActiveCards(formatMessage)}

        <div>
          {this.state.company.country !== 'SWE' && (
            <div>{this.isEditMode() ? this.editablePermanentPlace() : this.permanentPlace()}</div>
          )}
          {/* Might be reenabled, see: https://vaultit.atlassian.net/browse/BOL-4372 */}
          {/* {this.shouldShowContractWarning() && (
            <div className="field-warning">
              {formatMessage(paFormMessages.ContractLongerThanSixMonths)}
            </div>
          )} */}
        </div>

        {this.isEditMode() ? this.editableOneManBusiness() : this.oneManBusiness()}

        {this.state.is_one_man_company === false &&
          (this.isEditMode() ? this.editableCollectiveAgreement() : this.collectiveAgreement())}

        {this.state.is_one_man_company === false &&
          this.state.has_collective_agreement &&
          (this.isEditMode()
            ? this.editableCollectiveAgreementType()
            : this.collectiveAgreementType())}
      </div>
    );
  }

  renderSwedishFtax() {
    const swedishFTax = this.getSwedishFtax();
    const { formatMessage } = this.props.intl;
    const helpText = formatMessage(paFormMessages.swTaxNumberHelptext)

    return (
      <DetailsListItem id="swedish_ftax">
        {formatMessage(paFormMessages.SwedishTaxNumber)}
        <div>
          <div>
            <span className="mr-4">{swedishFTax}</span>
          </div>
          {swedishFTax && <div className="field-help mb-3">{helpText}</div>}
        </div>
      </DetailsListItem>
    );
  }

  renderIntroText() {
    return (
      this.state.currentPAstatus === PA_STATUS_CREATED &&
      this.state.authProfile.organisation_id === this.state.companyDetails.company_id
    );
  }

  render() {
    if (this.state.paLoading) {
      return <Spinner />;
    }
    return (
      <div className="preannouncement-modal">
        <p id="pa-intro" className={classNames('text-muted', 'ro-list-block')}>
          {this.renderIntroText() && (
            <FormattedMessage
              id="preannouncementDetails.pa_intro"
              description="PA intro message"
              defaultMessage="Use the form below to register information about your company
            and your agreement in the project.
            Also check the contract information provided by your customer.
            The information stated below will be reviewed by your customer,
            who will then submit the preannouncement.
            All companies above you in the supply chain will then review the data.
            When the preannouncement is confirmed, you will receive a notification."
            />
          )}
        </p>
        {this.renderPreannouncementData()}
        {this.renderProjectInformation()}
        {this.renderContractInfo()}
        {this.renderCompanyInfo()}
        {this.renderForemanDetails()}
        {this.renderContactInfo()}
        {this.renderInformantSupplierInfo()}
        {this.renderInformantCustomerInfo()}
      </div>
    );
  }
}

export default injectIntl(PreannouncementForm);
