import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import ProjectModalComponent from './ProjectModalComponent';
import StoreSubscription from '../../helpers/StoreSubscription';
import { preventDefault } from '../../helpers/EventHandlers';
import confirmClientship from '../../actions/actionCreators/ConfirmClientship';
import { projectSaveStore } from '../../stores/Stores';
import LocalizedText from '../i18n/LocalizedText';
import { withRouter } from '../../helpers/RouterComponentWrappers';

class AddedProjectClientBlockedModal extends ProjectModalComponent {
  static get propTypes() {
    return {
      title: PropTypes.node.isRequired,
      projectId: PropTypes.string.isRequired,
      onDetailsClose: PropTypes.func.isRequired,
      extraClassNames: PropTypes.object,
      projectCreatorName: PropTypes.string,
      addedClientName: PropTypes.string,
    };
  }

  constructor(props) {
    super(props);
    this.saveStoreSub = new StoreSubscription(projectSaveStore, this.storesChanged.bind(this));
    this.state = this.mapStoreToState(projectSaveStore.getState());
  }

  componentDidMount() {
    this.saveStoreSub.activate();
  }

  componentWillUnmount() {
    this.saveStoreSub.deactivate();
  }

  storesChanged() {
    this.setState(this.mapStoreToState(projectSaveStore.getState()));
  }

  mapStoreToState(saveStoreState) {
    return {
      saveInProgress: saveStoreState.saveInProgress,
      errors: saveStoreState.errors,
    };
  }

  onConfirm(event) {
    preventDefault(event);
    confirmClientship(this.props.projectId, this.props.router.navigate);
  }

  hasError(fieldName) {
    return !!this.state.errors[fieldName];
  }

  renderGeneralError() {
    // Things like network problems are reported as errors: { general: "error message" }
    if (!this.state.errors.general) {
      return null;
    }

    return (
      <div className="card-block">
        <div className="text--red err-msg pointer-events-none">{this.state.errors.general}</div>
      </div>
    );
  }

  renderError(fieldName) {
    // Things pertaining to form fields are reported as
    // errors: { fieldName: [ { langCode: "message", ... }, ... ] }
    if (!this.hasError(fieldName)) {
      return null;
    }

    return (
      <div className="card-block">
        <div className="text--red err-msg pointer-events-none">
          {`${fieldName}: `}
          {this.state.errors[fieldName].map((error, idx) => (
            <LocalizedText key={idx} translations={error} />
          ))}
        </div>
      </div>
    );
  }

  render() {
    return (
      <ProjectModalComponent
        title={this.props.title}
        onDetailsClose={this.props.onDetailsClose.bind(this)}
        extraClassNames={this.props.extraClassNames}
      >
        <div id="project_details_blocked" className="row u-border details-overview">
          <div className="col-xs-12">
            <div className="card noborder">
              <div className="card-block">
                <div className="card-title">
                  <FormattedMessage
                    id="confirmClientshipModal.headerText"
                    description="Confirm clientship modal header text"
                    defaultMessage="Confirm full access according to contract"
                  />
                </div>
                <div className="mt-5 mb-5">
                  <FormattedMessage
                    id="confirmClientshipModal.bodyText1"
                    description="Confirm clientship modal body text 1"
                    defaultMessage="{project_creator} has stated that your organization
                                    is the client of this project and must have full
                                    transparency in the project."
                    values={{
                      project_creator: this.props.projectCreatorName,
                    }}
                  />
                </div>
                <div className="mt-5 mb-5">
                  <FormattedMessage
                    id="confirmClientshipModal.bodyText2"
                    description="Confirm clientship modal body text 2"
                    defaultMessage="If {project_client} confirms below,
                                    all credit reports will be displayed
                                    and {project_client} will appear as
                                    the requester on  letters of disclosures."
                    values={{
                      project_client: this.props.addedClientName,
                    }}
                  />
                </div>
                <div className="mt-5 mb-5">
                  <FormattedMessage
                    id="confirmClientshipModal.bodyText3"
                    description="Confirm clientship modal body text 3"
                    defaultMessage="As a client,
                                    you will have permission to add side contractors."
                  />
                </div>
              </div>
              <div className="d-flex justify-content-center pt-4">
                <div className="justify-self-one-third-width">
                  <button
                    id="added_client_confirm_button"
                    className="btn btn-sm btn-primary mr-3"
                    type="submit"
                    role="button"
                    disabled={this.state.saveInProgress}
                    onClick={this.onConfirm.bind(this)}
                  >
                    <FormattedMessage
                      id="confirmClientshipModal.confirmAction"
                      description="On click client confirms their customership and
                                   user is redirected to project overview"
                      defaultMessage="Confirm"
                    />
                  </button>
                </div>
                <div className="justify-self-one-third-width justify-self-right">
                  <button
                    id="added_client_cancel_button"
                    className="btn btn-sm btn-link"
                    role="button"
                    type="reset"
                    onClick={this.props.onDetailsClose.bind(this)}
                  >
                    <FormattedMessage
                      id="confirmClientshipModal.cancelAction"
                      description="On click customersship is not confirmed and
                                   user is redirected to projects main tab"
                      defaultMessage="Cancel"
                    />
                  </button>
                </div>
              </div>
              {this.renderGeneralError()}
              {this.renderError('added_client_confirmed')}
            </div>
          </div>
        </div>
      </ProjectModalComponent>
    );
  }
}

export default injectIntl(withRouter(AddedProjectClientBlockedModal));
