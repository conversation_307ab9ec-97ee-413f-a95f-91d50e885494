import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { injectIntl, defineMessages } from 'react-intl';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import filterProjectList from '../../actions/actionCreators/ProjectListFilter';
import detailsViewClose from '../../actions/actionCreators/DetailsViewClose';
import StatusMessages from '../shared/StatusMessages';
import StateMessages from '../shared/StateMessages';
import ListFilterOptionsComponent from '../shared/ListFilterOptionsComponent';
import AddProjectButton from '../projects/AddProjectButton';
import { preventDefault } from '../../helpers/EventHandlers';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';

const messages = defineMessages({
  searchPlaceholder: {
    id: 'projects.filter.searchPlaceholder',
    description: 'Project place holder for search filter field',
    defaultMessage: 'Project name or ID',
  },
});

class ProjectListFilterComponent extends React.Component {
  static get propTypes() {
    return {
      narrow: PropTypes.bool,
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired
    };
  }

  static get defaultProps() {
    return {
      narrow: false,
    };
  }

  _getStatusValue() {
    return this.statusRef ? this.statusRef.options[this.statusRef.selectedIndex].value : '';
  }

  _getStateValue() {
    return this.stateRef ? this.stateRef.options[this.stateRef.selectedIndex].value : '';
  }

  _onFilter(event) {
    event.preventDefault();
    filterProjectList({
      search: this.searchRef ? this.searchRef.value : '',
      status: this._getStatusValue(),
      state: this._getStateValue(),
    });
    detailsViewClose();
    this.props.router.navigate('/projects');
    return false;
  }

  _onStatusChange() {
    filterProjectList({
      status: this._getStatusValue(),
    });
  }

  _onStateChange() {
    filterProjectList({
      state: this._getStateValue(),
    });
  }

  _onClear(event) {
    event.preventDefault();
    document.getElementById('projects_filter_form').reset();
    this._onFilter(event);
  }

  _addProjectClicked(event) {
    preventDefault(event);
    detailsViewClose();
    this.props.router.navigate('/projects/new');
  }

  renderOptions(options) {
    return options.map(option => (
      <option key={option.value} value={option.value}>
        {option.title}
      </option>
    ));
  }

  render() {
    const { formatMessage } = this.props.intl;
    const { narrow } = this.props;

    const statusChoices = [
      { value: '', title: formatMessage(StatusMessages.any) },
      { value: 'ok', title: formatMessage(StatusMessages.ok) },
      { value: 'not:ok', title: formatMessage(StatusMessages.notOk) },
      { value: 'stop', title: formatMessage(StatusMessages.stop) },
      {
        value: 'investigate',
        title: formatMessage(StatusMessages.investigate),
      },
      { value: 'incomplete', title: formatMessage(StatusMessages.incomplete) },
      { value: 'attention', title: formatMessage(StatusMessages.attention) },
    ];

    const stateChoices = [
      { value: 'not:closed', title: formatMessage(StateMessages.notClosed) },
      { value: 'active', title: formatMessage(StateMessages.active) },
      { value: 'draft', title: formatMessage(StateMessages.draft) },
      { value: 'closed', title: formatMessage(StateMessages.closed) },
    ];

    return (
      <div className="card noborder vertical-compact list-filter-with-options">
        <div className="card card-default mb-0 list-filter">
          <div className="card-block filter-block">
            <form id="projects_filter_form" onSubmit={this._onFilter.bind(this)}>
              <div className="row">
                <div className="col-md-6">
                  <div>
                    <label className="form-control-label form-control-label-sm">
                      <FormattedMessage
                        id="projects.filter.searchLabel"
                        description="Label for search input"
                        defaultMessage="Search"
                      />
                    </label>
                    <div className="input-group">
                      <input
                        name="search"
                        className="form-control"
                        placeholder={formatMessage(messages.searchPlaceholder)}
                        type="search"
                        ref={search => {
                          this.searchRef = search;
                        }}
                      />
                      <button
                        id="projects_filter_button"
                        type="submit"
                        role="button"
                        className={classNames(
                          'input-group-addon',
                          'text-primary',
                          'bg-white',
                          'projects_filter'
                        )}
                      >
                        <span className="fa fa-search" aria-hidden="true" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="col-md-0" />
                <div className="col-md-3">
                  <div>
                    <label className="form-control-label form-control-label-sm">
                      <FormattedMessage
                        id="projects.filter.stateLabel"
                        description="Label for state filter"
                        defaultMessage="State"
                      />
                    </label>
                    <select
                      name="project_state"
                      className="form-control c-select pull-right"
                      onChange={this._onStateChange.bind(this)}
                      ref={state => {
                        this.stateRef = state;
                      }}
                      role="button"
                    >
                      {this.renderOptions(stateChoices, '')}
                    </select>
                  </div>
                </div>
                <div className="col-md-3">
                  <div>
                    <label className="form-control-label form-control-label-sm">
                      <span>
                        <FormattedMessage
                          id="projects.filter.statusLabel"
                          description="Label for status filter"
                          defaultMessage="Status"
                        />
                      </span>
                    </label>
                    <select
                      name="project_status"
                      className="form-control"
                      onChange={this._onStatusChange.bind(this)}
                      ref={status => {
                        this.statusRef = status;
                      }}
                      role="button"
                    >
                      {this.renderOptions(statusChoices, '')}
                    </select>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div className="d-flex justify-content-between">
          <div className="add-project-button left">
            <AddProjectButton
              id="add_project_button_main_left"
              onClick={this._addProjectClicked.bind(this)}
              narrow={narrow}
            />
          </div>
          <div />
          <div className="add-project-button filter-options">
            <ListFilterOptionsComponent callback={this._onClear.bind(this)} />
          </div>
        </div>
      </div>
    );
  }
}

export default injectIntl(withRouter(ProjectListFilterComponent));
