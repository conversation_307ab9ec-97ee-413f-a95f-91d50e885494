import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import BulkSupplierImportCompanyTable from './BulkSupplierImportCompanyTable';
import LocalizedText from '../i18n/LocalizedText';

class BulkSupplierImportReviewStep extends React.Component {
  static get propTypes() {
    return {
      handleNext: PropTypes.func.isRequired,
      handleCancel: PropTypes.func.isRequired,
      companiesToSubmit: PropTypes.array,
      notSubmittedCompanies: PropTypes.array,
      companiesProcessed: PropTypes.array,
      supplierCheckInProgress: PropTypes.bool,
      supplierCheckCancelInProgress: PropTypes.bool,
      supplierSubmitInProgress: PropTypes.bool,
      checkErrors: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
      submitErrors: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
      jobErrors: PropTypes.array,
    };
  }

  static get defaultProps() {
    return {
      companiesToSubmit: [],
      notSubmittedCompanies: [],
      companiesProcessed: [],
    };
  }

  handleReviewNext() {
    this.props.handleNext(this.props.companiesToSubmit);
  }

  actionsDisabled() {
    return (
      this.props.supplierCheckInProgress ||
      this.props.supplierCheckCancelInProgress ||
      this.props.supplierSubmitInProgress ||
      this.props.jobErrors
    );
  }

  listContainerMaxHeight() {
    // This may need to grow dynamically for multi-line errors
    const errHeight = this.hasGeneralErrors() ? '86px' : '0px';
    return { maxHeight: `calc(100vh - 300px - ${errHeight})` };
  }

  hasGeneralErrors() {
    return this.props.checkErrors || this.props.jobErrors || this.props.submitErrors;
  }

  renderNonFieldErrors() {
    let errMsg;
    let submitErrorMsg;
    if (this.props.submitErrors) {
      const [submitError] = this.props.submitErrors;
      submitErrorMsg = submitError;
    } else if (this.props.checkErrors) {
      errMsg = (
        <FormattedMessage
          id="supplierBulkImport.genericSubmitError"
          description="Bulk supplier generic error text when submit has failed"
          defaultMessage="An error has occured while submitting data"
        />
      );
    } else if (this.props.jobErrors) {
      errMsg = (
        <FormattedMessage
          id="supplierBulkImport.genericJobError"
          description="Bulk supplier generic error text when job processing has failed"
          defaultMessage="An error has occured while processing the import"
        />
      );
    }

    if (!errMsg && !submitErrorMsg) {
      return null;
    }

    return (
      <div className="row">
        <div id="bulk-import-errors" className="col-md-12 errors">
          <div className="alert alert-danger" role="alert">
            {submitErrorMsg && <LocalizedText translations={submitErrorMsg} />}
            {!submitErrorMsg && errMsg}
          </div>
        </div>
      </div>
    );
  }

  render() {
    const numberOfCompaniesToBeSubmitted = this.props.companiesToSubmit.length;
    const numberOfNotSubmittedCompanies = this.props.notSubmittedCompanies.length;
    const numberOfTotalCompanies = numberOfCompaniesToBeSubmitted + numberOfNotSubmittedCompanies;
    const numberOfCompaniesProcessed = this.props.companiesProcessed.length;
    const { supplierCheckInProgress } = this.props;

    return (
      <div>
        <div className="modal-payload--no-scroll container-fluid bulkimport-wizard-review-step">
          {this.renderNonFieldErrors()}
          {supplierCheckInProgress && (
            <div className="text-info pointer-events-none">
              <FormattedMessage
                id="supplierBulkImport.companiesSubmitInProgress"
                description=""
                defaultMessage={
                  'Processing {numberOfCompanies, number} of' +
                  '{totalCompanies, number} total companies'
                }
                values={{
                  numberOfCompanies: numberOfCompaniesProcessed,
                  totalCompanies: numberOfTotalCompanies,
                }}
              />
            </div>
          )}
          <div className="row">
            <div className="col-md-6 companies-to-be-submitted">
              {!supplierCheckInProgress && (
                <div className="text-success pointer-events-none">
                  <FormattedMessage
                    id="supplierBulkImport.companiesReadyToBeSubmitted"
                    description=""
                    defaultMessage="{numberOfCompanies, number} companies ready to be submitted"
                    values={{ numberOfCompanies: numberOfCompaniesToBeSubmitted }}
                  />
                </div>
              )}
              <div
                className="bulk-import-wizard__company-list-container"
                style={this.listContainerMaxHeight()}
              >
                <BulkSupplierImportCompanyTable companies={this.props.companiesToSubmit} />
              </div>
            </div>
            <div className="col-md-6 companies-not-to-be-submitted">
              {!supplierCheckInProgress && (
                <div className="text-danger pointer-events-none">
                  <FormattedMessage
                    id="supplierBulkImport.companiesWillNotBeSubmitted"
                    description=""
                    defaultMessage="{numberOfCompanies, number} companies will not be submitted"
                    values={{ numberOfCompanies: numberOfNotSubmittedCompanies }}
                  />
                </div>
              )}
              <div
                className="bulk-import-wizard__company-list-container"
                style={this.listContainerMaxHeight()}
              >
                <BulkSupplierImportCompanyTable
                  companies={this.props.notSubmittedCompanies}
                  showPendingProgress={false}
                />
              </div>
            </div>
          </div>
          <div className="row">
            <div
              className={classNames(
                'col-md-12',
                'field-help',
                'form-group',
                'd-flex',
                'justify-content-start',
                'pointer-events-none'
              )}
            >
              <i className="fa fa-info-circle pr-2" />
              <FormattedMessage
                id="supplierBulkImport.reviewDataFormHelpText"
                description="Bulk supplier import check form help text"
                defaultMessage="Only left column will be submitted to the system."
              />
            </div>
          </div>
        </div>
        <div className="modal-actions col-md-12 d-flex justify-content-center">
          <button
            id="bulkImportReviewNext"
            role="button"
            className="btn btn-sm btn-primary mr-3"
            onClick={this.handleReviewNext.bind(this)}
            disabled={this.actionsDisabled()}
          >
            <FormattedMessage
              id="supplierBulkImport.wizardReviewNextButton"
              description={"Bulk supplier import wizard review step 'next' button"}
              defaultMessage="Next"
            />
          </button>
          <a
            id="BulkImportReviewCancel"
            className="btn btn-sm btn-link"
            onClick={(e)=>{
              e.preventDefault();
              if(!this.props.supplierCheckCancelInProgress) {
                this.props.handleCancel();
              }
            }}
            href="#"
          >
            <FormattedMessage
              id="supplierBulkImport.wizardReviewCancelAndBackButton"
              description={"Bulk supplier import wizard 'Back to previous' button"}
              defaultMessage="Cancel and go back to previous step"
            />
          </a>
        </div>
      </div>
    );
  }
}

export default BulkSupplierImportReviewStep;
