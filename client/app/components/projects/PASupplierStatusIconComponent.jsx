import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { injectIntl } from 'react-intl';
import StatusMessages from '../shared/PAStatusMessages';
import {
  PA_STATUS_CREATED,
  PA_STATUS_REGISTERED,
  PA_STATUS_CONFIRMED,
  PA_STATUS_REJECTED,
} from '../../Constants';
import { intlPropType } from '../i18n/IntlPropTypes';

class PASupplierStatusIconComponent extends React.Component {
  static get propTypes() {
    return {
      status: PropTypes.oneOf([
        PA_STATUS_CREATED,
        PA_STATUS_REGISTERED,
        PA_STATUS_CONFIRMED,
        PA_STATUS_REJECTED,
      ]),
      isAssignedOrgSameAsCurrentOrg: PropTypes.bool,
      onClick: PropTypes.func,
      intl: intlPropType.isRequired
    };
  }

  getStatusIcon(status) {
    const StatusIcons = {
      created: 'mark_chat_unread',
      registered: 'mark_chat_unread',
    };

    return StatusIcons[status];
  }

  getMessage(statusForUser) {
    const { formatMessage } = this.props.intl;

    return formatMessage(StatusMessages[statusForUser]);
  }

  render() {
    const { status, onClick } = this.props;
    if (!status) return null;

    let statusForUser = status;
    let iconStyle = status;
    if (!this.props.isAssignedOrgSameAsCurrentOrg) {
      if (status === PA_STATUS_REGISTERED) {
        statusForUser = 'inReview';
        iconStyle = 'in-review';
      }
      if (status === PA_STATUS_CREATED) {
        statusForUser = 'waitingForRegistration';
        iconStyle = 'waiting-for-register';
      }
    }

    const icon = this.getStatusIcon(statusForUser);

    return (
      <span title={this.getMessage(statusForUser)}>
        {icon && (
          <i
            className={classNames(
              'material-icons',
              'text-large',
              'pa-status-align',
              'pa-status-cell',
              `pa-status-${iconStyle}`
            )}
          >
            {icon}
          </i>
        )}
        <a href="#" onClick={(e)=>{e.preventDefault(); onClick()}}>
          {this.getMessage(statusForUser)}
        </a>
      </span>
    );
  }
}

export default injectIntl(PASupplierStatusIconComponent);
