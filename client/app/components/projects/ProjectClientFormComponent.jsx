/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { defineMessages, injectIntl } from 'react-intl';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { featureActive } from '../../helpers/FeatureFlags';
import { StoreSubscription, cloneState } from '../../helpers/StoreSubscription';
import {
  subcontractorFormStore,
  companySearchFieldStore,
  contactPersonSearchFieldStore,
  projectViewStore,
} from '../../stores/Stores';
import SearchableField from '../shared/SearchableField';
import addSubcontractorsClearFindClicked from '../../actions/actionCreators/AddSubcontractorsClearFindClicked';
import addSubcontractorsFindChanged from '../../actions/actionCreators/AddSubcontractorsFindChanged';
import addSubcontractorsCompanyFound from '../../actions/actionCreators/AddSubcontractorsCompanyFound';
import subcontractorContactPersonFind from '../../actions/actionCreators/SubcontractorContactPersonFind';
import addSubcontractorsCompanyFind from '../../actions/actionCreators/AddSubcontractorsCompanyFind';
import searchFieldChanged from '../../actions/actionCreators/SearchFieldChanged';
import searchFieldClearClicked from '../../actions/actionCreators/SearchFieldClearClicked';
import addSubcontractorsSaveFailure from '../../actions/actionCreators/AddSubcontractorsSaveFailure';
import {
  ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON,
  ERROR_TYPE_SUBCONTRACTOR_ADD_SAME_AS_MAIN_CONTRACTOR,
} from '../../Constants';
import {
  EmailVerifyInput,
  FieldErrorMsg,
  SearchableFieldAction,
  SubcontractorFormMessages,
  SubcontractorFormFieldRO,
  SubcontractorFormGeneralError,
  SubcontractorFormCountryDropdown,
} from './SubcontractorFormSharedComponents';
import Config from '../../Config';
import projectFormAddClient from '../../actions/actionCreators/ProjectFormAddClient';
import { intlPropType } from '../i18n/IntlPropTypes';

/* eslint-enable max-len */

const messages = defineMessages({
  supplierSearchErrorCheckIdOrVAT: {
    id: 'addSupplier.search_error_check_id_or_vat',
    description: 'Add supplier Business ID or VAT not found error',
    defaultMessage:
      'This company does not exist in available registers. Please check Business ID or VAT',
  },
  searchByVATNotSupportedCountries: {
    id: 'addSupplier.search_by_vat_info_not_supported_countries',
    description: 'Inform user about not supported countries searching by VAT',
    defaultMessage:
      'Search by VAT number is not supported for Sweden, Lithuania, Netherlands and Norway',
  },
  addClientCountryHelpText: {
    id: 'addSupplier.addClientCountryHelpText',
    description: 'Add client modal helptext for country dropdown',
    defaultMessage: 'ID06 Bolagsdeklaration may only be used in projects carried out in Sweden',
  },
  addClientBusinessIDorVatPlaceholder: {
    id: 'addSupplier.enter_company_business_id_placeholder_for_add_client',
    description: 'Add client placeholder for Business ID or VAT',
    defaultMessage: 'Company Business ID or VAT',
  },
});

FieldErrorMsg.propTypes = {
  errors: PropTypes.object,
  fieldName: PropTypes.string,
};

class ProjectClientFormComponent extends React.Component {
  static get defaultProps() {
    return {
      projectId: null,
    };
  }

  static get propTypes() {
    return {
      projectId: PropTypes.string,
      componentUpdateCallback: PropTypes.func,
      closeCallback: PropTypes.func.isRequired,
      intl: intlPropType.isRequired
    };
  }

  static title(_companyName, _isPreannouncing, _isPaRestarting, isEditProjectClient) {
    if (isEditProjectClient) {
      return (
        <div>
          <FormattedMessage
            id="supplier.submenu.edit_project_client"
            description="Edit project client form title"
            defaultMessage="Edit project client"
          />
        </div>
      );
    }
    return (
      <div>
        <div>
          <FormattedMessage
            id="supplier.submenu.add_project_client"
            description="Add project client form title"
            defaultMessage="Add project client"
          />
        </div>
      </div>
    );
  }

  constructor(props) {
    super(props);

    this.formSubscription = new StoreSubscription(
      subcontractorFormStore,
      this.formChanged.bind(this)
    );

    this.companySearchCompanyFieldSub = new StoreSubscription(
      companySearchFieldStore,
      this.formChanged.bind(this)
    );

    this.contactPersonSearchCompanyFieldSub = new StoreSubscription(
      contactPersonSearchFieldStore,
      this.formChanged.bind(this)
    );

    this.projectViewSub = new StoreSubscription(projectViewStore, this.formChanged.bind(this));

    this.state = this.mapStoreToState(
      subcontractorFormStore.getState(),
      companySearchFieldStore.getState(),
      contactPersonSearchFieldStore.getState(),
      projectViewStore.getState()
    );
  }

  componentDidMount() {
    this.formSubscription.activate();
    this.companySearchCompanyFieldSub.activate();
    this.contactPersonSearchCompanyFieldSub.activate();
    this.projectViewSub.activate();

    if (this.isEditingFound()) {
      // Simulate company search
      setTimeout(
        () => searchFieldChanged(this.state.addedClientGovId, 'add_subcontractor_company_search'),
        0
      );
      // Simulate successful result of addSubcontractorsCompanyFind():
      setTimeout(
        () =>
          addSubcontractorsCompanyFound(
            this.getEditedFoundCompany(),
            'add_subcontractor_company_search'
          ),
        0
      );
      // Simulate person search
      let email = null;
      if (this.getContact()) {
        // eslint-disable-next-line prefer-destructuring
        email = this.getContact().email;
      } else {
        email = this.state.addedClientContactPersonEmail;
      }
      if (email) {
        setTimeout(
          () =>
            subcontractorContactPersonFind(
              email,
              this.state.addedClientCompanyId,
              'contact_person_search_field'
            ),
          0
        );
      }
    }
  }

  componentDidUpdate() {
    if (this.props.componentUpdateCallback) {
      this.props.componentUpdateCallback();
    }
  }

  componentWillUnmount() {
    this.formSubscription.deactivate();
    this.companySearchCompanyFieldSub.deactivate();
    this.contactPersonSearchCompanyFieldSub.deactivate();
    this.projectViewSub.deactivate();
  }

  onSubmit(event) {
    if (event) {
      event.preventDefault();
    }
    switch (this.formActionBtn.id) {
      case 'company_find_button':
        const query = this.searchId ? this.searchId.value : '';
        const country = this.selectedCountry ? this.selectedCountry : 'se';
        if (query && country) {
          addSubcontractorsCompanyFind(
            this.props.projectId,
            query,
            country,
            'add_subcontractor_company_search',
            true
          );
        }
        break;
      case 'contact_person_find_button':
        const email = this.state.contact_person_search_field.query;
        this.clearContactPersonError();
        subcontractorContactPersonFind(
          email,
          this.getFoundCompany().id,
          'contact_person_search_field'
        );
        break;
      case 'company_save_button':
        this.handleSaveProjectClient();
        break;
      default:
    }
  }

  onFindInput(event) {
    searchFieldChanged(event.target.value, 'add_subcontractor_company_search');
    addSubcontractorsFindChanged(event.target.value);
    searchFieldClearClicked('contact_person_search_field');
  }

  onCountryDropdownChanged() {
    searchFieldChanged(this.state.query, 'add_subcontractor_company_search');
    addSubcontractorsFindChanged(this.state.query);
  }

  onCompanySearchClear(event) {
    event.preventDefault();
    searchFieldClearClicked('add_subcontractor_company_search');
    addSubcontractorsClearFindClicked();
  }

  getContactEmail() {
    return this.state.contact_person_search_field.query;
  }

  getContact() {
    let contact = null;
    const { results } = this.state.contact_person_search_field;
    if (results && results[0]) {
      // eslint-disable-next-line prefer-destructuring
      contact = results[0];
    }
    return contact;
  }

  getEditedFoundCompany() {
    const company = {
      id: this.state.addedClientCompanyId,
      name: this.state.addedClientName,
      country: this.state.addedClientCountry,
    };
    return company;
  }

  getFoundCompany() {
    let company = null;
    const { results } = this.state.company_search_field;
    if (results && results[0]) {
      // eslint-disable-next-line prefer-destructuring
      company = results[0];
    } else if (this.isEditingFound()) {
      company = this.getEditedFoundCompany();
    }
    return company;
  }

  foundMultiple() {
    return (
      this.state.company_search_field.results && this.state.company_search_field.results.length > 1
    );
  }

  foundSingle() {
    return (
      this.state.company_search_field.results &&
      this.state.company_search_field.results.length === 1
    );
  }

  foundNone() {
    return (
      (this.state.company_search_field.found && this.state.company_search_field.results < 1) ||
      this.state.company_search_field.not_found
    );
  }

  isEditingFound() {
    return this.state.addedClientName && this.state.addedClientGovId;
  }

  isDisabledControl() {
    return this.isEditingFound() && this.props.projectId != null;
  }

  formChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(
        subcontractorFormStore.getState(),
        companySearchFieldStore.getState(),
        contactPersonSearchFieldStore.getState(),
        projectViewStore.getState()
      )
    );
  }

  mapStoreToState(
    storeState,
    companySearchFieldState,
    contactPersonSearchFieldState,
    projectViewState
  ) {
    return {
      ...cloneState(storeState),
      company_search_field: companySearchFieldState,
      contact_person_search_field: contactPersonSearchFieldState,
      isPAEnabled: featureActive('pre_announcements') && projectViewState.pa_form_enabled,
      addedClientName: projectViewState.added_client_name,
      addedClientGovId: projectViewState.added_client_gov_id,
      addedClientCompanyId: projectViewState.added_client_company_id,
      addedClientCompanyExternalId: projectViewState.added_client_company_external_id,
      addedClientCountry: projectViewState.added_client_country,
      addedClientContactPersonEmail: projectViewState.added_client_contact_person_email,
      addedClientContactPersonId: projectViewState.added_client_contact_person_id,
      addedClientContactPersonFullName: projectViewState.added_client_contact_person_full_name,
    };
  }

  checkFormFieldErrors(fieldname) {
    if (this.state.errors && fieldname in this.state.errors) {
      return true;
    }
    return false;
  }

  handleSaveProjectClient() {
    const {
      addedClientName,
      addedClientGovId,
      addedClientCompanyId,
      addedClientCountry,
      addedClientCompanyExternalId,
    } = this.state;
    let addedProjectClientName = null;
    let addedProjectClientCompanyId = null;
    let addedProjectClientCountry = null;
    let addedProjectClientCompanyExternalId = null;
    if (this.getFoundCompany()) {
      addedProjectClientName = this.getFoundCompany().name || addedClientName;
      addedProjectClientCompanyId = this.getFoundCompany().id || addedClientCompanyId;
      addedProjectClientCompanyExternalId = 
        this.getFoundCompany().external_id || addedClientCompanyExternalId;
      addedProjectClientCountry = this.getFoundCompany().country || addedClientCountry;
    }
    const addedProjectClientGovId = this.state.company_search_field.query || addedClientGovId;
    const addedClientContactEmail = this.getContactEmail();
    let addedClientContactPersonId = null;
    let addedClientContactPersonFullName = null;
    if (this.getContact()) {
      addedClientContactPersonId = this.getContact().person_id;
      addedClientContactPersonFullName = this.getContact().full_name;
    }
    projectFormAddClient(
      addedProjectClientName,
      addedProjectClientGovId,
      addedProjectClientCompanyId,
      addedProjectClientCompanyExternalId,
      addedProjectClientCountry,
      addedClientContactEmail,
      addedClientContactPersonId,
      addedClientContactPersonFullName
    );
  }

  clearContactPersonError() {
    if ((this.state.errors || {}).supplier_contacts) {
      delete this.state.errors.supplier_contacts;
      addSubcontractorsSaveFailure(this.state.errors);
    }
  }

  hasSearchError() {
    return (
      !this.state.company_search_field.is_find_step && (this.foundNone() || this.foundMultiple())
    );
  }

  isPrivatePersonError() {
    return this.state.error_type === ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON;
  }

  isProjectClientSameAsMainContractorError() {
    return this.state.error_type === ERROR_TYPE_SUBCONTRACTOR_ADD_SAME_AS_MAIN_CONTRACTOR;
  }

  canAddExisting() {
    return (
      !this.state.company_search_field.is_find_step &&
      (this.foundSingle() || (this.isEditingFound() && !this.hasSearchError()))
    );
  }

  cannotAddThisCompanyHere() {
    return this.state.errors.hasOwnProperty('parent_id');
  }

  isNextDisabled() {
    return !this.state.company_search_field.query || this.state.company_search_field.not_found;
  }

  isSaveButtonDisabled() {
    return this.state.save_in_progress;
  }

  renderSearchError() {
    const { formatMessage } = this.props.intl;
    const supplierSearchError = messages.supplierSearchErrorCheckIdOrVAT;

    if (this.hasSearchError()) {
      if (this.isPrivatePersonError()) {
        return (
          <div className="form-control-feedback err-msg">
            <FormattedMessage
              id="addSupplier.search_cannot_add_private person"
              description="Indicate that user cannot add a sole trader that is not a private person"
              defaultMessage="Cannot add private person that is not a sole trader"
            />
          </div>
        );
      }
      if (this.isProjectClientSameAsMainContractorError()) {
        return (
          <div className="form-control-feedback err-msg">
            <FormattedMessage
              id="addProjectClient.searchMainContractorCannotAddAsProjectClient"
              description="Indicate that user cannot add a project client same main contractor"
              defaultMessage={
                'A main contractor can not add themselves as the project client. ' +
                'If you are also the project’s client, select that option and then ' +
                'add yourself as the main contractor once the project has been created.'
              }
            />
          </div>
        );
      }
      return (
        <div className="form-control-feedback err-msg">{formatMessage(supplierSearchError)}</div>
      );
    } else if (this.cannotAddThisCompanyHere()) {
      return (
        <div className="form-control-feedback err-msg">
          <FormattedMessage
            id="addSupplier.cannot_add_error"
            description="Same supplier already exists at this level error"
            defaultMessage="This company is already a supplier at this level"
          />
        </div>
      );
    }

    return null;
  }

  renderSearch() {
    const { formatMessage } = this.props.intl;
    let formGroupClasses = 'form-group mb-1';
    if (this.hasSearchError() || this.cannotAddThisCompanyHere()) {
      formGroupClasses += ' has-danger';
    }
    const searchFieldLabel = SubcontractorFormMessages.addClientBusinessIDorVatLabel;
    const searchFieldPlaceholder = messages.addClientBusinessIDorVatPlaceholder;

    return (
      <div>
        <SearchableField
          className={formGroupClasses}
          label={formatMessage(searchFieldLabel)}
          placeholder={formatMessage(searchFieldPlaceholder)}
          input_value={this.state.company_search_field.query}
          onChange={this.onFindInput.bind(this)}
          reference={ref => {
            this.searchId = ref;
          }}
          renderErrors={this.renderSearchError()}
          required
          disabled={this.isDisabledControl()}
        >
          <a
            href="#"
            className={classNames('input-group-addon', 'bg-white', 'no-decoration', {
              'pointer-events-none': this.isDisabledControl(),
            })}
            id="clear_search_id"
            onClick={this.onCompanySearchClear.bind(this)}
            disabled={this.isDisabledControl()}
          >
            <SearchableFieldAction
              search_in_progress={this.state.company_search_field.search_in_progress}
            />
          </a>
        </SearchableField>
        <div className="form-group">
          <label className="field-help form-control-feedback">
            <span>
              <i className="fa fa-info-circle pr-3" />
            </span>
            {formatMessage(messages.searchByVATNotSupportedCountries)}
          </label>
        </div>
        <SubcontractorFormCountryDropdown
          className="form-group"
          togglerFieldId="select_company_country"
          reference={ref => {
            this.selectedCountry = ref;
          }}
          countries={Config.countriesList[this.props.intl.locale]}
          onChange={this.onCountryDropdownChanged.bind(this)}
          disabled={this.isDisabledControl()}
          selectedCountry={this.state.addedClientCountry}
        />
        <div className="form-group">
          <label className="field-help form-control-feedback">
            {formatMessage(messages.addClientCountryHelpText)}
          </label>
        </div>
      </div>
    );
  }

  renderFoundCompany() {
    const { formatMessage } = this.props.intl;

    let companyName = '';
    if (this.getFoundCompany()) {
      companyName = this.getFoundCompany().name;
    }

    return (
      <div id="search_results">
        <SubcontractorFormFieldRO
          label={formatMessage(SubcontractorFormMessages.companyNameLabel)}
          value={companyName}
          fieldId="subcontractor_form_name"
          disabled={this.isEditingFound()}
        />
      </div>
    );
  }

  renderCompany() {
    if (this.canAddExisting()) {
      return this.renderFoundCompany();
    }
    return null;
  }

  renderContactEmail() {
    if (!this.canAddExisting()) {
      return null;
    }

    return (
      <EmailVerifyInput
        query={this.state.contact_person_search_field.query}
        search_in_progress={this.state.contact_person_search_field.search_in_progress}
        found={this.state.contact_person_search_field.found}
        not_found={this.state.contact_person_search_field.not_found}
        results={this.state.contact_person_search_field.results}
        errors={
          (this.state.contact_person_search_field.search_errors || {}).email ||
          (this.state.errors || {}).supplier_contacts
        }
        required={this.state.isPAEnabled}
        isPASubsupplier={false}
        isProjectClient
      />
    );
  }

  renderCancelButton() {
    return (
      <button
        id="company_cancel_button"
        className="btn btn-sm btn-link"
        role="button"
        type="reset"
        onClick={this.props.closeCallback}
        disabled={this.state.save_in_progress}
      >
        <FormattedMessage
          id="supplierForm.cancel_action"
          description="subcontractor cancel button label"
          defaultMessage="Cancel"
        />
      </button>
    );
  }

  renderActions() {
    const findCompanyActions = (
      <div className="d-flex align-items-center add-supplier-actions">
        <div className="justify-self-one-third-width">
          <button
            id="company_find_button"
            className="btn btn-sm btn-primary mr-3"
            type="submit"
            role="button"
            ref={ref => {
              this.formActionBtn = ref;
            }}
            disabled={this.isNextDisabled()}
          >
            <FormattedMessage
              id="supplierForm.button_next"
              description="Add subcontractor Next button label"
              defaultMessage="Next"
            />
          </button>
        </div>
        <div className="justify-self-one-third-width justify-self-right">
          {this.renderCancelButton()}
        </div>
      </div>
    );
    const findContactPersonActions = (
      <div className="d-flex align-items-center add-supplier-actions">
        <div className="justify-self-one-third-width">
          <button
            id="contact_person_find_button"
            className="btn btn-sm btn-primary mr-3"
            type="submit"
            role="button"
            ref={ref => {
              this.formActionBtn = ref;
            }}
            disabled={
              !this.state.contact_person_search_field.query ||
              this.state.contact_person_search_field.search_errors
            }
          >
            <FormattedMessage
              id="supplierForm.button_next"
              description="Add subcontractor Next button label"
              defaultMessage="Next"
            />
          </button>
        </div>
        <div className="justify-self-one-third-width justify-self-right">
          {this.renderCancelButton()}
        </div>
      </div>
    );

    const saveActions = (
      <div className="d-flex align-items-center add-supplier-actions">
        <div className="justify-self-one-third-width">
          <button
            id="company_save_button"
            className="btn btn-sm btn-primary mr-3"
            type="submit"
            role="button"
            ref={ref => {
              this.formActionBtn = ref;
            }}
            disabled={this.isSaveButtonDisabled()}
          >
            <FormattedMessage
              id="addSupplier.save_button"
              description="Add supplier Save button label"
              defaultMessage="Save"
            />
          </button>
        </div>
        <div className="justify-self-one-third-width justify-self-right">
          {this.renderCancelButton()}
        </div>
      </div>
    );

    let actions = null;

    if (this.state.company_search_field.is_find_step) {
      actions = findCompanyActions;
    } else if (!this.canAddExisting()) {
      actions = findCompanyActions;
    } else if (
      this.canAddExisting() &&
      this.state.contact_person_search_field.is_find_step &&
      this.state.contact_person_search_field.query
    ) {
      actions = findContactPersonActions;
    } else {
      actions = saveActions;
    }

    return <div>{actions}</div>;
  }

  render() {
    return (
      <form id="project_client_form" onSubmit={this.onSubmit.bind(this)}>
        {this.renderSearch()}
        {this.renderCompany()}
        {this.renderContactEmail()}
        <SubcontractorFormGeneralError
          errors={{ ...this.state.errors, ...this.state.contact_person_search_field.search_errors }}
        />
        {this.renderActions()}
      </form>
    );
  }
}

export default injectIntl(ProjectClientFormComponent);
