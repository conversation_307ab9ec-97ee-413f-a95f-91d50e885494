import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../../components/i18n/FormattedMessage'

import BulkSupplierImportCompanyTable from './BulkSupplierImportCompanyTable';

class BulkSupplierImportDoneStep extends React.Component {
  static get propTypes() {
    return {
      handleDone: PropTypes.func.isRequired,
      submittedCompanies: PropTypes.array,
      notSubmittedCompanies: PropTypes.array,
    };
  }

  static get defaultProps() {
    return {
      submittedCompanies: [],
      notSubmittedCompanies: [],
    };
  }

  listContainerMaxHeight() {
    return { maxHeight: 'calc(100vh - 300px)' };
  }

  render() {
    const numberOfSubmittedCompanies = this.props.submittedCompanies.length;
    const numberOfNotSubmittedCompanies = this.props.notSubmittedCompanies.length;
    return (
      <div>
        <div className="modal-payload--no-scroll container-fluid bulkimport-wizard-done-step">
          <div className="row">
            <div className="col-md-6 companies-to-be-submitted">
              <div className="text-success pointer-events-none">
                <FormattedMessage
                  id="supplierBulkImport.companiesSubmitted"
                  description=""
                  defaultMessage="{numberOfCompanies, number} companies submitted"
                  values={{ numberOfCompanies: numberOfSubmittedCompanies }}
                />
              </div>
              <div
                className="bulk-import-wizard__company-list-container"
                style={this.listContainerMaxHeight()}
              >
                <BulkSupplierImportCompanyTable companies={this.props.submittedCompanies} />
              </div>
            </div>
            <div className="col-md-6 companies-not-to-be-submitted">
              <div className="text-danger pointer-events-none">
                <FormattedMessage
                  id="supplierBulkImport.companiesNotSubmitted"
                  description=""
                  defaultMessage={'{numberOfCompanies, number} companies not submitted'}
                  values={{ numberOfCompanies: numberOfNotSubmittedCompanies }}
                />
              </div>
              <div
                className="bulk-import-wizard__company-list-container"
                style={this.listContainerMaxHeight()}
              >
                <BulkSupplierImportCompanyTable
                  companies={this.props.notSubmittedCompanies}
                  showPendingProgress={false}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="modal-actions">
          <div className="col-md-12 d-flex justify-content-center" style={{ paddingTop: '16px' }}>
            <button
              id="bulkImportDoneClose"
              className="btn btn-sm btn-primary"
              role="button"
              onClick={this.props.handleDone}
            >
              <FormattedMessage
                id="supplierBulkImport.wizardDoneStepDoneButton"
                description={"Bulk supplier import wizard done step 'Done' button"}
                defaultMessage="Done"
              />
            </button>
          </div>
        </div>
      </div>
    );
  }
}

export default BulkSupplierImportDoneStep;
