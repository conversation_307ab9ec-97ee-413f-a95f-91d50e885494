/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import addSubcontractorsMenuClicked from '../../actions/actionCreators/AddSubcontractorsMenuClicked';
import placeSupplierMenuClicked from '../../actions/actionCreators/PlaceSupplierMenuClicked';
import { SUPPLIER_LINKED, TREE_ROOT_DEPTH } from '../../Constants';
import NodeMenuComponent from './NodeMenuComponent';
import NodeMenuItem from './NodeMenuItem';
/* eslint-enable max-len */

class ProjectTreeRootComponent extends React.Component {
  static get defaultProps() {
    return {
      depth: TREE_ROOT_DEPTH,
      disableContextMenu: false,
    };
  }

  static get propTypes() {
    return {
      depth: PropTypes.number,
      name: PropTypes.node.isRequired,
      role: PropTypes.node,
      worstSubtreeStatus: PropTypes.oneOf(['stop', 'investigate', 'incomplete', 'attention', 'ok']),
      hasAddAction: PropTypes.bool.isRequired,
      hasPlaceAction: PropTypes.bool.isRequired,
      children: PropTypes.node,
      disableContextMenu: PropTypes.bool,
    };
  }

  renderMenu() {
    const addCallback = () =>
      addSubcontractorsMenuClicked(this.contextBlockTargetRef, {
        companyName: this.props.name,
        supplierId: null,
        supplierType: SUPPLIER_LINKED,
        isRoot: true,
      });
    const placeCallback = () => placeSupplierMenuClicked(null, SUPPLIER_LINKED);
    return (
      <NodeMenuComponent disableContextMenu={this.props.disableContextMenu}>
        <NodeMenuItem
          visible={this.props.hasAddAction}
          className="dropdown-add-supplier"
          onClick={addCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.rootAddSupplier"
            description="Add supplier project tree root item menu entry"
            defaultMessage="Add supplier"
          />
        </NodeMenuItem>
        <NodeMenuItem
          visible={this.props.hasPlaceAction}
          className="dropdown-place-supplier"
          onClick={placeCallback}
        >
          <FormattedMessage
            id="projects.nodemenu.placeSupplier"
            description="Place supplier project tree item menu entry"
            defaultMessage="Place supplier"
          />
        </NodeMenuItem>
      </NodeMenuComponent>
    );
  }

  render() {
    return (
      <li>
        <div
          ref={targetRef => {
            this.contextBlockTargetRef = targetRef;
          }}
          className="tree-node root-node"
          data-depth={this.props.depth}
          data-worst-subtree-status={this.props.worstSubtreeStatus}
        >
          <div
            className={classNames({
              left: true,
              'node-content': true,
              thick: this.props.role,
              grow: true,
              'pointer-events-none': true,
            })}
          >
            <div className="node-title">{this.props.name}</div>
            <div className="node-role">{this.props.role}</div>
          </div>
          <div className="node-content">{this.renderMenu()}</div>
        </div>
        <ul>{this.props.children}</ul>
      </li>
    );
  }
}

export default ProjectTreeRootComponent;
