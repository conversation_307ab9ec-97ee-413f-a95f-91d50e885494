import PropTypes from 'prop-types';
import { defineMessages, injectIntl } from 'react-intl';
import React from 'react';
import classNames from 'classnames';

import LocalizedText from '../i18n/LocalizedText';
import { intlPropType } from '../i18n/IntlPropTypes';

export const BulkImportFormMessages = defineMessages({
  supplierBulkImportEnterDataHelpText: {
    id: 'supplierBulkImport.enterDataFormHelpText',
    description: 'Bulk supplier import check form help text',
    defaultMessage:
      'Enter up to 100 business IDs separate by commas or ' +
      'newlines. We will get the company information from official ' +
      'registries and you will review the company list in the next ' +
      'step before submitting',
  },
  supplierBulkImportEnterDataOrHelpTextVAT: {
    id: 'supplierBulkImport.enterDataFormHelpTextVAT',
    description: 'Bulk supplier import check form help text with VAT support',
    defaultMessage:
      'Enter up to 100 business IDs or VAT numbers separate by commas or ' +
      'newlines. We will get the company information from official ' +
      'registries and you will review the company list in the next ' +
      'step before submitting',
  },
  supplierBulkImportGovOrgIdTextarea: {
    id: 'supplierBulkImport.govOrgIdTextareaLabel',
    description: 'Bulk supplier import business ID textarea label',
    defaultMessage: 'Business IDs',
  },
  supplierBulkImportGovOrgIdTextareaVAT: {
    id: 'supplierBulkImport.govOrgIdTextareaLabelVAT',
    description: 'Bulk supplier import business ID textarea label with VAT support',
    defaultMessage: 'Business IDs or VAT numbers',
  },
  supplierBulkImportWizardNextBtn: {
    id: 'supplierBulkImport.wizardEnterDataNextBtn',
    description: "Bulk supplier import wizard enter step 'next' button",
    defaultMessage: 'Next',
  },
  supplierBulkImportClearAllBtn: {
    id: 'supplierBulkImport.clearAllBtn',
    description: "Bulk supplier import wizard 'Clear all' button",
    defaultMessage: 'Clear all',
  },
  supplierBulkImportByVATNotSupportedCountries: {
    id: 'supplierBulkImport.search_by_vat_info_not_supported_countries',
    description: 'Inform user about not supported countries searching by VAT',
    defaultMessage:
      'Search by VAT number is not supported for Sweden, Lithuania, Netherlands and Norway',
  },
});

const messages = BulkImportFormMessages;

class BulkSupplierEnterIDsStep extends React.Component {
  static get propTypes() {
    return {
      handleNext: PropTypes.func.isRequired,
      defaultValue: PropTypes.string,
      errors: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
      intl: intlPropType.isRequired,
    };
  }

  getFieldError(field) {
    if (this.props.errors && !this.props.errors.length) {
      return this.props.errors[field];
    }

    return null;
  }

  listContainerHeight() {
    const singleErrorHeight = 24;
    let errorHeight = this.props.errors ? singleErrorHeight : 0;
    if (this.props.errors && this.props.errors.length) {
      errorHeight = this.props.errors.length * singleErrorHeight;
    }

    const maxHeightCalc = `calc(100vh - 330px - ${errorHeight}px)`;
    return { maxHeight: maxHeightCalc, minHeight: '50px' };
  }

  handleClearAll(e) {
    e.preventDefault();
    this.companyGovOrgIds.value = '';
  }

  handleEnterNext() {
    const govOrgIds = this.companyGovOrgIds.value;
    this.props.handleNext(govOrgIds);
  }

  renderNonFieldErrors() {
    // Check for non-field errors
    if (!this.props.errors || !this.props.errors.length) {
      return null;
    }

    return (
      <div className="row">
        <div className="col-md-12 errors">
          {this.props.errors.map((error, idx) => (
            <div key={idx} className="alert alert-danger" role="alert">
              <LocalizedText translations={error} />
            </div>
          ))}
        </div>
      </div>
    );
  }

  renderFieldErrors(field) {
    const errors = this.getFieldError(field);
    if (!errors) {
      return null;
    }

    return (
      <div className="pointer-events-none">
        {errors.map((error, idx) => (
          <LocalizedText translations={error} key={idx} />
        ))}
      </div>
    );
  }

  render() {
    const { formatMessage } = this.props.intl;
    const govOrgIdInputStyle = classNames({
      'form-control': true,
      'form-field--text-area-field': true,
      'has-danger': this.getFieldError('companies'),
    });
    const govOrgIdFormGroupStyle = classNames({
      'col-12 form-group': true,
      'has-danger': this.getFieldError('companies'),
    });
    const supplierBulkImportEnterDataHelpText = messages.supplierBulkImportEnterDataOrHelpTextVAT;

    const supplierBulkImportGovOrgIdTextarea = messages.supplierBulkImportGovOrgIdTextareaVAT;
    return (
      <div>
        <div className="modal-payload container-fluid bulkimport-wizard-enter-data-step">
          {this.renderNonFieldErrors()}
          <div className="row">
            <div
              className={classNames(
                'col-md-12',
                'field-help',
                'form-group',
                'd-flex',
                'justify-content-start',
                'pointer-events-none'
              )}
            >
              <i className="fa fa-info-circle pr-3" />
              {formatMessage(supplierBulkImportEnterDataHelpText)}
            </div>
          </div>
          <div className="row">
            <div className={govOrgIdFormGroupStyle}>
              <label htmlFor="companyGovOrgIds" className="form-control-label">
                <span>{formatMessage(supplierBulkImportGovOrgIdTextarea)}</span>
                <span className="text-danger">&nbsp;*</span>
              </label>
              <textarea
                id="companyGovOrgIds"
                ref={elem => {
                  this.companyGovOrgIds = elem;
                }}
                name="companyGovOrgIds"
                className={govOrgIdInputStyle}
                defaultValue={this.props.defaultValue}
                style={this.listContainerHeight()}
              />
              {this.renderFieldErrors('companies')}
            </div>
          </div>
          <div className="row">
            <div className="col-md-12 field-help form-group">
              <label className="form-control-label form-control-label-sm">
                <span className="text-danger">* </span>
                {formatMessage(messages.supplierBulkImportByVATNotSupportedCountries)}
              </label>
            </div>
          </div>
        </div>
        <div className="modal-actions col-md-12 d-flex justify-content-center">
          <button
            id="bulkImportEnterDataNext"
            className="btn btn-sm btn-primary mr-3"
            role="button"
            onClick={this.handleEnterNext.bind(this)}
          >
            {formatMessage(messages.supplierBulkImportWizardNextBtn)}
          </button>
          <a
            href="#"
            className="btn btn-sm btn-link"
            onClick={this.handleClearAll.bind(this)}
          >
            {formatMessage(messages.supplierBulkImportClearAllBtn)}
          </a>
        </div>
      </div>
    );
  }
}

export default injectIntl(BulkSupplierEnterIDsStep);
