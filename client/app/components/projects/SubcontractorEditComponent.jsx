/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { defineMessages, injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { TREE_ROOT_DEPTH } from '../../Constants';
import { featureActive } from '../../helpers/FeatureFlags';
import supplierRoleChanged from '../../actions/actionCreators/SupplierRoleChanged';
import subcontractorContactPersonFind from '../../actions/actionCreators/SubcontractorContactPersonFind';
import subcontractorContactEmailChanged from '../../actions/actionCreators/SubcontractorContactEmailChanged';
import searchFieldChanged from '../../actions/actionCreators/SearchFieldChanged';
import subcontractorEditSave from '../../actions/actionCreators/SubcontractorEditSave';
import subcontractorEditSaveFailed from '../../actions/actionCreators/SubcontractorEditSaveFailed';
import StoreSubscription from '../../helpers/StoreSubscription';
import {
  contactPersonSearchFieldStore,
  projectViewStore,
  subcontractorFormStore,
  subcontractorSaveStore,
} from '../../stores/Stores';
import PAFormMessages from './PAFormMessages';
import { SUPPLIER_ROLES, SUPPLIER } from './SupplierRoles';
import {
  EmailVerifyInput,
  FieldErrorMsg,
  SubcontractorFormFieldRO,
  SubcontractorFormMessages,
  SubcontractorFormRoleDropdown,
  SubcontractorFormGeneralError,
  SubcontractorFormContractTypeDropdown,
  WorkAreasCheckboxes,
  ContractDateRange,
} from './SubcontractorFormSharedComponents';
import supplierContractTypeChanged from '../../actions/actionCreators/SupplierContractTypeChanged';
import supplierContractStartDateChanged from '../../actions/actionCreators/SupplierContractStartDateChanged';
import supplierContractEndDateChanged from '../../actions/actionCreators/SupplierContractEndDateChanged';
import supplierWorkAreasChanged from '../../actions/actionCreators/SupplierContractWorkAreasChanged';
import supplierIsOneManCompanyChanged from '../../actions/actionCreators/SupplierIsOneManCompanyChanged';
import supplierHasCollectiveAgreementChanged from '../../actions/actionCreators/SupplierHasCollectiveAgreementChanged';
import supplierCollectiveAgreementNameChanged from '../../actions/actionCreators/SupplierCollectiveAgreementNameChanged';
import { ELIGIBLE_CONTRACT_TYPES } from './SupplierContractTypes';
import { intlPropType } from '../i18n/IntlPropTypes';
/* eslint-enable max-len */

const paFormMessages = PAFormMessages;

const messages = defineMessages({
  required: {
    id: 'supplierForm.field_is_required',
    description: 'Error message for required field left blank',
    defaultMessage: 'Required field.',
  },
});

class SubcontractorFormEditComponent extends React.Component {
  static get propTypes() {
    return {
      projectId: PropTypes.string.isRequired,
      supplierId: PropTypes.string.isRequired,
      companyName: PropTypes.node.isRequired,
      supplierData: PropTypes.object.isRequired,
      componentUpdateCallback: PropTypes.func,
      disableCtxBlockCallback: PropTypes.func,
      closeCallback: PropTypes.func.isRequired,
      permissions: PropTypes.arrayOf(PropTypes.string).isRequired,
      isRoot: PropTypes.bool,
      isPaRestarting: PropTypes.bool,
      shouldCloseModalContainerOnSaveSuccess: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  static get defaultProps() {
    return {
      shouldCloseModalContainerOnSaveSuccess: false,
    };
  }

  // eslint-disable-next-line no-unused-vars
  static title(companyName, isPreannouncing, isPaRestarting, _isEditProjectClient) {
    if (isPaRestarting && isPreannouncing) {
      return (
        <div>
          <FormattedMessage
            id="supplier.submenu.create_new_preannouncement_form_edit"
            description="Create new preannouncement form title"
            defaultMessage="Create new preannouncement for {companyName}"
            values={{ companyName }}
          />
        </div>
      );
    }
    return (
      <div>
        <FormattedMessage
          id="supplier.submenu.subcontractor_form_edit"
          description="Edit subcontractor form title"
          defaultMessage="Edit supplier {companyName}"
          values={{ companyName }}
        />
      </div>
    );
  }

  constructor(props) {
    super(props);

    this.formSubscription = new StoreSubscription(
      subcontractorFormStore,
      this.formChanged.bind(this)
    );
    this.saveStoreSub = new StoreSubscription(subcontractorSaveStore, this.formChanged.bind(this));
    this.contactPersonSearchCompanyFieldSub = new StoreSubscription(
      contactPersonSearchFieldStore,
      this.formChanged.bind(this)
    );
    this.projectViewSub = new StoreSubscription(projectViewStore, this.formChanged.bind(this));

    this.state = this.mapStoreToState(
      subcontractorFormStore.getState(),
      subcontractorSaveStore.getState(),
      contactPersonSearchFieldStore.getState(),
      projectViewStore.getState()
    );
  }

  componentDidMount() {
    this.formSubscription.activate();
    this.saveStoreSub.activate();
    this.contactPersonSearchCompanyFieldSub.activate();
    this.projectViewSub.activate();

    const rolesAvailable = this.rolesAvailable();
    if (rolesAvailable.length === 1) {
      supplierRoleChanged(rolesAvailable[0]);
    } else if (rolesAvailable.length > 1) {
      supplierRoleChanged(this.props.supplierData.role);
    }
    supplierContractTypeChanged(this.props.supplierData.contractType);
    supplierContractStartDateChanged(this.props.supplierData.contractStartDate);
    supplierContractEndDateChanged(this.props.supplierData.contractEndDate);
    supplierWorkAreasChanged(this.props.supplierData.contractWorkAreas);
    supplierIsOneManCompanyChanged(this.props.supplierData.isOneManCompany);
    supplierHasCollectiveAgreementChanged(this.props.supplierData.hasCollectiveAgreement);
    supplierCollectiveAgreementNameChanged(this.props.supplierData.collectiveAgreementName);

    const email = this.getContactEmail();
    // Resets email to blank
    searchFieldChanged('', 'contact_person_search_field', null);
    if (email) {
      // Show stored email (resets to blank if no email)
      // Need to separate from searchFieldChanged due to
      // 'Warning: Input changing from uncontrolled input to controlled'
      searchFieldChanged(email, 'contact_person_search_field', null);
      // Extract and show contact data
      subcontractorContactPersonFind(
        email,
        this.props.supplierData.companyId,
        'contact_person_search_field'
      );
    }
  }

  componentDidUpdate() {
    if (this.props.componentUpdateCallback) {
      this.props.componentUpdateCallback();
    }
  }

  componentWillUnmount() {
    this.formSubscription.deactivate();
    this.saveStoreSub.deactivate();
    this.contactPersonSearchCompanyFieldSub.deactivate();
    this.projectViewSub.deactivate();
  }

  onSubmit(event) {
    if (event) event.preventDefault();

    const btnId = this.formActionBtn.id;
    if (btnId === 'contact_person_find_button') {
      this.clearContactPersonError();
      subcontractorContactPersonFind(
        this.state.contact_person_search_field.query,
        this.props.supplierData.companyId,
        'contact_person_search_field'
      );
    } else if (btnId === 'company_save_button') {
      this.handleSave();
    }

    return false;
  }

  onClose(event) {
    event.preventDefault();
    this.props.closeCallback();
    return false;
  }

  onContactEmailChanged(event) {
    subcontractorContactEmailChanged(event && event.target.value);
  }

  getContactPersonId() {
    const contactSelected = this.props.supplierData.contacts[0];
    return contactSelected && contactSelected.supplier_contact_person_id;
  }

  getContactEmail() {
    const contactSelected = this.props.supplierData.contacts[0];
    return contactSelected && contactSelected.supplier_contact_email;
  }

  getSupplierRole() {
    return this.state.supplierRole;
  }

  getContractType() {
    return this.state.contractType;
  }

  canEditWorkAreas() {
    return this.state.isPAEnabled && ELIGIBLE_CONTRACT_TYPES.includes(this.state.contractType);
  }

  formChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(
        subcontractorFormStore.getState(),
        subcontractorSaveStore.getState(),
        contactPersonSearchFieldStore.getState(),
        projectViewStore.getState()
      )
    );
  }

  isTopLevel() {
    return !this.props.isRoot && this.props.supplierData.depth === TREE_ROOT_DEPTH + 1;
  }

  rolesAvailable() {
    let roles = [];
    const currentRole = this.props.supplierData.role;
    /* Not root and not unlinked */
    if (this.isTopLevel() && this.props.permissions.includes('update_supplier_role')) {
      roles = SUPPLIER_ROLES;
    } else if (currentRole && SUPPLIER_ROLES.includes(currentRole)) {
      roles = [currentRole];
    } else {
      roles = [SUPPLIER];
    }
    return roles;
  }

  checkFormFieldErrors(fieldname) {
    if (this.state.errors && fieldname in this.state.errors) {
      return true;
    }
    return false;
  }

  mapStoreToState(formStoreState, saveStoreState, contactPersonSearchFieldState, projectViewState) {
    return {
      supplierRole: formStoreState.supplierRole,
      contractType: formStoreState.contractType,
      contractStartDate: formStoreState.contractStartDate,
      contractEndDate: formStoreState.contractEndDate,
      contractWorkAreas: formStoreState.contractWorkAreas,
      contact_person: formStoreState.contact_person,
      is_one_man_company: formStoreState.is_one_man_company,
      has_collective_agreement: formStoreState.has_collective_agreement,
      collective_agreement_name: formStoreState.collective_agreement_name,
      save_in_progress: saveStoreState.saveInProgress,
      errors: saveStoreState.errors,
      contact_person_search_field: contactPersonSearchFieldState,
      isPAEnabled: featureActive('pre_announcements') && projectViewState.pa_form_enabled,
      skipPARegStep: featureActive('skip_pa_reg_step'),
    };
  }

  handleSave() {
    let contractId = null;
    let personId = null;
    let email = null;
    if (this.state.contact_person_search_field.results) {
      const contactPerson = this.state.contact_person_search_field.results[0];
      contractId = contactPerson.contract_id;
      personId = contactPerson.person_id;
      // BOL-4516: we want to remember the original email and continue using it
      // it even if the user deletes their account or changes their email
      email = contactPerson.email; // eslint-disable-line prefer-destructuring
    } else {
      email = this.state.contact_person_search_field.query;
    }

    let contractWorkAreas = null;
    if (this.state.isPAEnabled) {
      contractWorkAreas = this.canEditWorkAreas() ? this.state.contractWorkAreas : [];
    }

    const supplierData = {
      supplier_id: this.props.supplierId,
      supplier_revision: this.props.supplierData.supplierRev,
      supplier_role: this.getSupplierRole(),
      contract_type: this.getContractType(),
      contract_start_date: this.state.contractStartDate,
      contract_end_date: this.state.contractEndDate,
      contract_work_areas: contractWorkAreas,
      supplier_contacts: [
        {
          supplier_contact_contract_id: contractId,
          supplier_contact_email: email,
          supplier_contact_person_id: null,
        },
      ],
      is_restart_pa: this.props.isPaRestarting,
    };
    if (featureActive('person_id_for_project_users')) {
      supplierData.supplier_contacts[0].supplier_contact_person_id = personId;
      supplierData.supplier_contacts[0].supplier_contact_contract_id = undefined;
    }
    if (this.canEditCollectiveAgreementFields()) {
      supplierData.is_one_man_company = this.state.is_one_man_company;
      supplierData.has_collective_agreement = this.state.has_collective_agreement;
      supplierData.collective_agreement_name = this.state.collective_agreement_name;
    }

    const { locale, formatMessage } = this.props.intl;
    const requiredMsg = [{ [locale]: formatMessage(messages.required) }];
    const errors = {};
    if (!this.state.supplierRole) {
      errors.supplier_role = requiredMsg;
    }
    if (this.canEditContractType() && !this.state.contractType) {
      errors.contract_type = requiredMsg;
    }
    if (this.state.isPAEnabled) {
      if (!this.state.skipPARegStep) {
        // Old PA flow
        if (!this.state.contractStartDate) {
          errors.contract_start_date = requiredMsg;
        }
        if (!this.state.contractEndDate) {
          errors.contract_end_date = requiredMsg;
        }
      } else {
        // Skip PA registration flow
        // eslint-disable-next-line no-lonely-if
        if (this.canEditCollectiveAgreementFields()) {
          if (this.state.is_one_man_company == null) {
            errors.is_one_man_company = requiredMsg;
          }
          if (
            this.state.is_one_man_company === false &&
            this.state.has_collective_agreement === null
          ) {
            errors.has_collective_agreement = requiredMsg;
          }
          if (
            this.state.is_one_man_company === false &&
            this.state.has_collective_agreement &&
            (!this.state.collective_agreement_name ||
              this.state.collective_agreement_name.trim().length === 0)
          ) {
            errors.collective_agreement_name = requiredMsg;
          }
        }
      }
      if (this.canEditWorkAreas()) {
        if (!Array.isArray(this.state.contractWorkAreas) || !this.state.contractWorkAreas.length) {
          errors.contract_work_areas = requiredMsg;
        }
      }
      if (!this.state.contact_person_search_field.query) {
        errors.supplier_contacts = requiredMsg;
      }
    }

    if (Object.keys(errors).length > 0) {
      subcontractorEditSaveFailed(errors);
    } else {
      subcontractorEditSave(
        this.props.projectId,
        supplierData,
        this.props.shouldCloseModalContainerOnSaveSuccess
      );
    }
  }

  clearContactPersonError() {
    if ((this.state.errors || {}).supplier_contacts) {
      delete this.state.errors.supplier_contacts;
      subcontractorEditSaveFailed(this.state.errors);
    }
  }

  canEditContractType() {
    return (
      (this.state.isPAEnabled &&
        this.props.supplierData.permissions.includes('update_contract_type')) ||
      this.props.isPaRestarting
    );
  }

  isSaveButtonDisabled() {
    return this.state.save_in_progress;
  }

  canEditCollectiveAgreementFields() {
    return (
      (this.state.isPAEnabled &&
        this.state.skipPARegStep &&
        this.props.supplierData.permissions.includes('update_collective_agreement')) ||
      this.props.isPaRestarting
    );
  }

  editableOneManBusiness() {
    const { formatMessage } = this.props.intl;
    return (
      <div
        id="one_man_business"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('is_one_man_company'),
        })}
      >
        <div className="form-control-label form-control-label-sm">
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.OneManBusiness)}
        </div>
        <input
          type="radio"
          value="true"
          name="one_man_business"
          id="one_man_business_yes"
          disabled={!this.canEditCollectiveAgreementFields()}
          checked={this.state.is_one_man_company === true}
          onChange={() => {
            supplierIsOneManCompanyChanged(true);
            supplierHasCollectiveAgreementChanged(null);
            supplierCollectiveAgreementNameChanged(null);
          }}
        />

        <label className="form-control-label text-fzsm mr-3" htmlFor="one_man_business_yes">
          {formatMessage(paFormMessages.LabelYes)}
        </label>
        <input
          type="radio"
          value="false"
          name="one_man_business"
          id="one_man_business_no"
          disabled={!this.canEditCollectiveAgreementFields()}
          checked={this.state.is_one_man_company === false}
          onChange={() => supplierIsOneManCompanyChanged(false)}
        />
        <label htmlFor="one_man_business_no" className="form-control-label text-fzsm ">
          {formatMessage(paFormMessages.LabelNo)}
        </label>
        <FieldErrorMsg fieldName="is_one_man_company" errors={this.state.errors} />
        <div
          className={
            this.checkFormFieldErrors('is_one_man_company')
              ? 'field-help-has-danger mb-1'
              : 'field-help mb-1'
          }
        >
          <label className="field-help form-control-feedback">
            <span>
              <i className="fa fa-info-circle pr-3" />
            </span>
            {formatMessage(paFormMessages.OneManBusinessHelptext)}
          </label>
        </div>
      </div>
    );
  }

  editableCollectiveAgreement() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        id="collective_agreement"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('has_collective_agreement'),
        })}
      >
        <label className="form-control-label form-control-label-sm">
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.CollectiveAgreement)}
        </label>
        <div>
          <input
            type="radio"
            value="true"
            name="pa_collective_agreement"
            id="pa_collective_agreement_yes"
            disabled={!this.canEditCollectiveAgreementFields()}
            checked={this.state.has_collective_agreement === true}
            onChange={() => supplierHasCollectiveAgreementChanged(true)}
          />
          <label
            className="form-control-label text-fzsm mr-3"
            htmlFor="pa_collective_agreement_yes"
          >
            {formatMessage(paFormMessages.LabelYes)}
          </label>

          <input
            type="radio"
            value="false"
            name="pa_collective_agreement"
            id="pa_collective_agreement_no"
            disabled={!this.canEditCollectiveAgreementFields()}
            checked={this.state.has_collective_agreement === false}
            onChange={() => {
              supplierHasCollectiveAgreementChanged(false);
              supplierCollectiveAgreementNameChanged(null);
            }}
          />
          <label htmlFor="pa_collective_agreement_no" className="form-control-label text-fzsm">
            {formatMessage(paFormMessages.LabelNo)}
          </label>
        </div>
        <FieldErrorMsg fieldName="has_collective_agreement" errors={this.state.errors} />
      </div>
    );
  }

  editableCollectiveAgreementType() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        id="collective_agreement_name"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('collective_agreement_name'),
        })}
      >
        <div className="mb-3">
          <label
            className="form-control-label form-control-label-sm"
            htmlFor="pa_collective_agreement_name"
          >
            <span className="text--red">*&nbsp;</span>
            {formatMessage(paFormMessages.CollectiveAgreementName)}
          </label>
          <input
            name="pa_collective_agreement_name"
            id="pa_collective_agreement_name"
            className="form-control form-control-sm"
            type="text"
            placeholder={formatMessage(paFormMessages.CollectiveAgreementPlaceholder)}
            disabled={!this.canEditCollectiveAgreementFields()}
            value={this.state.collective_agreement_name || ''}
            onChange={e => supplierCollectiveAgreementNameChanged(e.target.value)}
          />
          <FieldErrorMsg fieldName="collective_agreement_name" errors={this.state.errors} />
        </div>
      </div>
    );
  }

  renderCompanyRoles() {
    return (
      <SubcontractorFormRoleDropdown
        togglerFieldId="edit_user_role"
        roles={this.rolesAvailable()}
        roleSelected={this.state.supplierRole}
        toggleCallback={this.props.disableCtxBlockCallback}
      />
    );
  }

  renderContractType() {
    if (this.state.isPAEnabled) {
      return (
        <SubcontractorFormContractTypeDropdown
          togglerFieldId="add_contract_type"
          typeSelected={this.state.contractType}
          errors={this.state.errors}
          toggleCallback={this.props.disableCtxBlockCallback}
          disabled={!this.canEditContractType()}
        />
      );
    }
    return null;
  }

  renderDates() {
    if (this.state.isPAEnabled && !this.state.skipPARegStep) {
      return (
        <ContractDateRange
          selectedContractStartDate={this.state.contractStartDate}
          selectedContractEndDate={this.state.contractEndDate}
          disabled={
            !this.props.supplierData.permissions.includes('update_contract_dates') &&
            !this.props.isPaRestarting
          }
        />
      );
    }
    return null;
  }

  renderWorkAreas() {
    if (this.canEditWorkAreas()) {
      return (
        <WorkAreasCheckboxes
          disabled={
            !this.props.supplierData.permissions.includes('update_contract_type') &&
            !this.props.isPaRestarting
          }
        />
      );
    }
    return null;
  }

  renderCollectiveAgreement() {
    if (this.state.isPAEnabled && this.state.skipPARegStep) {
      return (
        <div id="collective_agreement_wrapper" className="form-group mb-1">
          {this.editableOneManBusiness()}
          {this.state.is_one_man_company === false && this.editableCollectiveAgreement()}
          {this.state.is_one_man_company === false &&
            this.state.has_collective_agreement &&
            this.editableCollectiveAgreementType()}
        </div>
      );
    }
    return null;
  }

  renderContractInfoHelpText() {
    if (this.state.isPAEnabled) {
      return (
        <div className="field-help contractor-info-helptext">
          <hr className="contract-info-helptext-line" />
          {!this.canEditCollectiveAgreementFields() && (
            <React.Fragment>
              <i className="fa fa-info-circle pr-3" />
              <FormattedMessage
                id="supplier.submenu.contract_information_helptext"
                description="Edit subcontractor form contract information helptext"
                defaultMessage="To change information included in the preannouncement,
                a new preannouncement needs to be created."
              />
            </React.Fragment>
          )}
        </div>
      );
    }
    return null;
  }

  renderContactEmail() {
    return (
      <EmailVerifyInput
        query={this.state.contact_person_search_field.query}
        search_in_progress={this.state.contact_person_search_field.search_in_progress}
        found={this.state.contact_person_search_field.found}
        not_found={this.state.contact_person_search_field.not_found}
        results={this.state.contact_person_search_field.results}
        errors={
          (this.state.contact_person_search_field.search_errors || {}).email ||
          (this.state.errors || {}).supplier_contacts
        }
        required={this.state.isPAEnabled}
        isPASubsupplier={this.state.isPAEnabled && !this.isTopLevel()}
      />
    );
  }

  renderCancelButton() {
    return (
      <button
        id="company_cancel_button"
        className="btn btn-sm btn-link"
        onClick={this.onClose.bind(this)}
      >
        <FormattedMessage
          id="supplierForm.cancel_action"
          description="subcontractor cancel button label"
          defaultMessage="Cancel"
        />
      </button>
    );
  }

  renderActions() {
    const findContactPersonActions = (
      <div>
        <button
          id="contact_person_find_button"
          className="btn btn-sm btn-primary mr-3"
          type="submit"
          role="button"
          ref={ref => {
            this.formActionBtn = ref;
          }}
          disabled={
            !this.state.contact_person_search_field.query ||
            this.state.contact_person_search_field.search_errors
          }
        >
          <FormattedMessage
            id="supplierForm.button_next"
            description="Add subcontractor Next button label"
            defaultMessage="Next"
          />
        </button>
      </div>
    );
    const saveActions = (
      <div className="d-flex align-items-center add-supplier-actions">
        <div className="justify-self-one-third-width">
          <button
            id="company_save_button"
            className="btn btn-sm btn-primary mr-3"
            type="submit"
            role="button"
            ref={ref => {
              this.formActionBtn = ref;
            }}
            disabled={this.isSaveButtonDisabled()}
          >
            <FormattedMessage
              id="addSupplier.save_button"
              description="Add supplier Save button label"
              defaultMessage="Save"
            />
          </button>
        </div>
        <div className="justify-self-one-third-width justify-self-right">
          {this.renderCancelButton()}
        </div>
      </div>
    );

    let actions = null;
    if (
      this.state.contact_person_search_field.is_find_step &&
      this.state.contact_person_search_field.query
    ) {
      actions = findContactPersonActions;
    } else {
      actions = saveActions;
    }

    return <div>{actions}</div>;
  }

  render() {
    const { formatMessage } = this.props.intl;
    return (
      <form id="subcontractor_edit_form" onSubmit={this.onSubmit.bind(this)}>
        {this.props.supplierData.govOrgId && (
          <SubcontractorFormFieldRO
            label={formatMessage(SubcontractorFormMessages.companyBusinessIdLabel)}
            value={this.props.supplierData.govOrgId}
            fieldId="subcontractor_form_bussiness_id"
          />
        )}
        {this.props.supplierData.vatNumber && (
          <SubcontractorFormFieldRO
            label={formatMessage(SubcontractorFormMessages.companyVATLabel)}
            value={this.props.supplierData.vatNumber}
            fieldId="subcontractor_form_vat"
          />
        )}
        <SubcontractorFormFieldRO
          label={formatMessage(SubcontractorFormMessages.companyNameLabel)}
          value={this.props.companyName}
          fieldId="subcontractor_form_name"
        />
        <SubcontractorFormFieldRO
          label={formatMessage(SubcontractorFormMessages.companyCountryLabel)}
          value={this.props.supplierData.country}
          fieldId="subcontractor_form_country"
        />
        {this.renderCompanyRoles()}
        {this.renderContractInfoHelpText()}
        {this.renderContractType()}
        {this.renderDates()}
        {this.renderWorkAreas()}
        {this.renderCollectiveAgreement()}
        {this.renderContactEmail()}
        <SubcontractorFormGeneralError errors={this.state.errors} />
        {this.renderActions()}
      </form>
    );
  }
}

export default injectIntl(SubcontractorFormEditComponent);
