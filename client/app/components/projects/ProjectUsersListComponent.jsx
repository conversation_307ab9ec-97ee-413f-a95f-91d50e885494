/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import BootstrapTable from 'react-bootstrap-table-next';
import { defineMessages, injectIntl } from 'react-intl';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { StoreSubscription, cloneState } from '../../helpers/StoreSubscription';
import LocalizedText from '../i18n/LocalizedText';
import { projectUsersStore, projectUsersAddFormStore } from '../../stores/Stores';
import { ProjectRolesMessages, ROLE_OPTIONS } from './ProjectUsersRoles';
import getProjectUsersList from '../../actions/actionCreators/GetProjectUsersList';
import projectUsersAddFormOpen from '../../actions/actionCreators/ProjectUsersAddFormOpen';
import projectUsersAddFormClose from '../../actions/actionCreators/ProjectUsersAddFormClose';
import ProjectUsersAddComponent from './ProjectUsersAddComponent';
import projectUsersAddClearStatus from '../../actions/actionCreators/ProjectUsersAddClearStatus';
import projectUsersUpdate from '../../actions/actionCreators/ProjectUsersUpdate';
import projectUsersNotifyUpdate from '../../actions/actionCreators/ProjectUsersNotifyUpdate';
import projectUsersDelete from '../../actions/actionCreators/ProjectUsersDelete';
import TableMessages from '../shared/TableMessages';
import UserMessages from './ProjectUsersMessages';
import Spinner from '../../components/shared/Spinner';
import DropdownMenuComponent from '../shared/dropdown/DropDownMenuComponent';
import supplierRoleChanged from '../../actions/actionCreators/SupplierRoleChanged';
import { intlPropType } from '../i18n/IntlPropTypes';
/* eslint-enable max-len */

const messages = defineMessages({
  usersAddSuccess: {
    id: 'projectUsers.changeSuccess',
    description: 'A message that project memeber has been created/updated/deleted successfully',
    defaultMessage: 'Project members have been updated successfully.',
  },
});

class ProjectUsersListComponent extends React.Component {
  static get propTypes() {
    return {
      projectId: PropTypes.string.isRequired,
      permissions: PropTypes.arrayOf(PropTypes.string).isRequired,
      intl: intlPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.projectUsersSub = new StoreSubscription(projectUsersStore, this.storeChanged.bind(this));
    this.projectUsersAddFormSub = new StoreSubscription(
      projectUsersAddFormStore,
      this.storeChanged.bind(this)
    );
    this.state = this.mapStoreToState(
      projectUsersStore.getState(),
      projectUsersAddFormStore.getState()
    );
    this.contextBlockTargetRef = {};
  }

  componentDidMount() {
    this.projectUsersSub.activate();
    this.projectUsersAddFormSub.activate();
    this.loadProjectUsers(this.props.projectId);
  }

  componentWillUnmount() {
    this.projectUsersSub.deactivate();
    this.projectUsersAddFormSub.deactivate();
  }

  mapStoreToState(projectUsersStoreState, projectUsersAddFormStoreState) {
    return {
      ...cloneState(projectUsersStoreState),
      ...cloneState(projectUsersAddFormStoreState),
    };
  }

  loadProjectUsers() {
    setTimeout(() => getProjectUsersList(this.props.projectId), 0);
  }

  storeChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(projectUsersStore.getState(), projectUsersAddFormStore.getState())
    );
  }

  changeRoleCallBack(role, contractResourceId) {
    projectUsersUpdate(this.props.projectId, contractResourceId, role);
    supplierRoleChanged(role);
  }

  deleteCallBack(contractResourceId, targetElement, userEmail, userRole, company, e) {
    e.preventDefault();
    projectUsersDelete(
      targetElement,
      this.props.projectId,
      contractResourceId,
      userEmail,
      userRole,
      company
    );
  }

  hasError(fieldName) {
    return !!this.state.errors[fieldName];
  }

  errorsMetaDataGet(key) {
    return this.state.errorsMetaData && this.state.errorsMetaData[key];
  }

  roleOptionsAvailable(rolesAvailable) {
    const options = {};
    rolesAvailable.forEach(role => {
      options[role] = ROLE_OPTIONS[role];
    });

    return Object.values(options);
  }

  roleFormatter(cell, row) {
    // Passing locale as extraData, "changes" value of the cell
    // and forces to update this cell altogether with translation
    const { formatMessage } = this.props.intl;

    const role = cell;
    const user = row;
    const roleLabel = formatMessage(ProjectRolesMessages[role.toLowerCase()]);

    let roleElement = roleLabel;
    if (user.permissions.includes('update_user')) {
      const toggler = (
        <span className="role_toggler">
          {roleLabel}
          <i className="fa fa-caret-down fa-pl--bol" aria-hidden="true" />
        </span>
      );
      roleElement = (
        <DropdownMenuComponent
          options={this.roleOptionsAvailable(user.available_roles)}
          callback={this.changeRoleCallBack.bind(this)}
          callbackParams={[user.project_user_id]}
          toggler={toggler}
          className="dropdown-role dropdown-role-"
        />
      );
    }

    let errorClass = '';
    if (
      this.hasError('role') &&
      this.errorsMetaDataGet('project_user_contract_id') === user.project_user_id
    ) {
      errorClass = 'has-danger';
    }

    return <div className={classNames(['text-left pl-3', errorClass])}>{roleElement}</div>;
  }

  handleNotifyUserChange(event, row) {
    const { checked } = event.target;
    projectUsersNotifyUpdate(this.props.projectId, row.project_user_id, checked);
  }

  notifyFormatter(cell, row) {
    return (
      <div>
        <label className="custom-control custom-checkbox">
          <input
            type="checkbox"
            name="notify"
            className="custom-control-input"
            onChange={e => this.handleNotifyUserChange(e, row)}
            disabled={!row.permissions.includes('update_user_notify')}
            checked={cell}
            value={cell}
          />
          <span className="custom-control-indicator" />
        </label>
      </div>
    );
  }

  userInfoFormatter(cell, row) {
    const { formatMessage } = this.props.intl;
    const userInfo = row.registered
      ? cell.name
      : `(${formatMessage(UserMessages.pending_invitation)})`;
    let permMessage;
    if (row.has_bol_permission) {
      permMessage = null;
    } else {
      permMessage = (
        <span>
          <br />
          <small className="sub-title text-muted">
            {formatMessage(UserMessages.no_bol_permissions)}
          </small>
        </span>
      );
    }
    return (
      <div>
        <span>{userInfo}</span>
        <br />
        <small className="sub-title text-muted">
          <span>{cell.email}</span>
        </small>
        {permMessage}
      </div>
    );
  }

  deleteFormatter(cell, row, index) {
    const user = row;

    if (!row.permissions.includes('remove_user')) {
      return null;
    }

    let errorClass = '';
    if (
      this.hasError('project_user_contract_id') &&
      this.errorsMetaDataGet('project_user_contract_id') === user.project_user_id
    ) {
      errorClass = 'has-danger';
    }

    return (
      <div
        ref={targetRef => {
          this.contextBlockTargetRef[index] = targetRef;
        }}
      >
        <a
          href="#"
          className={`pull-right ${errorClass}`}
          id="clear-filter-link"
          onClick={(e) =>
            this.deleteCallBack(
              row.project_user_id,
              this.contextBlockTargetRef[index],
              row.user_info.email,
              row.role,
              row.company,
              e
            )
          }
        >
          <i className="fa fa-trash-o fa-pr--bol" />
          <FormattedMessage
            id="projectUsers.deleteUserLink"
            description="Project user delete link"
            defaultMessage="Delete"
          />
        </a>
      </div>
    );
  }

  closeStatusMessage() {
    projectUsersAddClearStatus();
    if (this.state.saveSuccess) {
      projectUsersAddFormClose();
    }
  }
  renderMultipleErrors(fieldName) {
    if (!this.hasError(fieldName)) {
      return null;
    }
    return (
      <span>
        {this.state.errors[fieldName].map((error, idx) => (
          <span key={idx}>
            <LocalizedText translations={error} />
            <span>&nbsp;</span>
          </span>
        ))}
      </span>
    );
  }

  renderAddProjectUser() {
    if (!this.props.permissions.includes('manage_users')) {
      return null;
    } else if (this.state.formOpened && !this.state.saveSuccess) {
      return <ProjectUsersAddComponent projectId={this.props.projectId} />;
    }
    return (
      <div className="text-right">
        <a href="#" onClick={(e)=>{
          e.preventDefault()
          projectUsersAddFormOpen()
        }}>
          <i className="fa fa-plus fa-pr--bol" />
          <FormattedMessage
            id="add_user.addProjectUserLink"
            description="Project user add link"
            defaultMessage="Add project member"
          />
        </a>
      </div>
    );
  }

  renderStatusMessage(payload = '', cssClasses = 'msg-grey') {
    return (
      <div className={classNames(['alert--bol-compact', 'alert', 'd-flex', cssClasses])}>
        <div className="mr-auto">
          <i className="fa fa-exclamation-circle fa-pr--bol align-middle" />
          {payload}
        </div>
        <div className="ml-auto text-right">
          <a onClick={this.closeStatusMessage.bind(this)}>
            <i className="fa fa-close align-middle" />
          </a>
        </div>
      </div>
    );
  }

  renderFormStatus() {
    const { formatMessage } = this.props.intl;

    const generalError = this.state.errors.general;
    if (generalError) {
      return this.renderStatusMessage(generalError, 'alert-danger');
    }
    if (this.hasError('role')) {
      const errMsgs = this.renderMultipleErrors('role');
      return this.renderStatusMessage(errMsgs, 'alert-danger');
    }
    if (this.hasError('project_user_contract_id')) {
      const errMsgs = this.renderMultipleErrors('project_user_contract_id');
      return this.renderStatusMessage(errMsgs, 'alert-danger');
    }
    if (this.state.saveSuccess) {
      return this.renderStatusMessage(formatMessage(messages.usersAddSuccess), 'alert-success');
    }
    return null;
  }

  renderOptions(options) {
    return options.map(option => (
      <option key={option.value} value={option.value}>
        {option.title}
      </option>
    ));
  }

  render() {
    const { formatMessage } = this.props.intl;
    const defaultSorted = [
      {
        dataField: 'role',
        order: 'asc',
      },
    ];
    const headerClasses = classNames('pointer-events-none');
    const columns = [
      {
        dataField: 'role',
        text: formatMessage(TableMessages.role),
        formatter: this.roleFormatter.bind(this),
        formatExtraData: this.props.intl.locale,
        headerClasses,
        headerAlign: 'left',
        align: 'center',
        style: { width: '100px' },
      },
      {
        dataField: 'user_info',
        text: formatMessage(TableMessages.userInfo),
        formatter: this.userInfoFormatter.bind(this),
        formatExtraData: this.props.intl.locale,
        headerClasses,
      },
      {
        dataField: 'company',
        text: formatMessage(TableMessages.company),
        headerClasses,
      },
      {
        dataField: 'notify',
        text: formatMessage(TableMessages.notify),
        formatter: this.notifyFormatter.bind(this),
        headerClasses,
      },
      {
        dataField: 'actions',
        text: formatMessage(TableMessages.actions),
        formatter: this.deleteFormatter.bind(this),
        formatExtraData: this.props.intl.locale,
        headerClasses,
        align: 'left',
        style: { width: '80px' },
      },
    ];
    if (this.state.projectUsersLoading) {
      return (
        <div>
          <Spinner />
        </div>
      );
    }
    return (
      <div>
        {this.renderAddProjectUser()}
        {this.renderFormStatus()}
        <BootstrapTable
          keyField="project_user_id"
          columns={columns}
          data={this.state.users}
          striped
          hover
          bordered={false}
          defaultSorted={defaultSorted}
          defaultSortDirection="asc"
          noDataIndication={() => formatMessage(TableMessages.no_data_to_display)}
          wrapperClasses="project-users"
          classes={classNames('table-striped--bol', 'table--bol-compact')}
          rowClasses="table-clickable-rows--bol"
        />
      </div>
    );
  }
}

export default injectIntl(ProjectUsersListComponent);
