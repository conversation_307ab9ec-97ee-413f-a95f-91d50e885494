import { defineMessages } from 'react-intl';

export const CLEANING = 'cleaning';
export const DEMOLITION = 'demolition';
export const SANITATION = 'sanitation';
export const DRYWALL = 'drywall';
export const ASSEMBLY = 'assembly';
export const SCAFFOLDING = 'scaffolding';
export const LAND_WORKS = 'land_works';
export const CASTING_ALL_MATERIALS = 'casting_all_materials';
export const FRAMEWORK = 'framework';
export const MASONRY_AND_PLASTERING = 'masonry_and_plastering';
export const OTHER = 'other';

export const ContractWorkAreasMsgs = defineMessages({
  cleaning: {
    id: 'supplier.contract_work_area.cleaning',
    description: 'Contract work area label: cleaning',
    defaultMessage: 'Cleaning',
  },
  demolition: {
    id: 'supplier.contract_work_area.demolition',
    description: 'Contract work area label: demolition',
    defaultMessage: 'Demolition',
  },
  sanitation: {
    id: 'supplier.contract_work_area.sanitation',
    description: 'Contract work area label: sanitation',
    defaultMessage: 'Sanitation',
  },
  drywall: {
    id: 'supplier.contract_work_area.drywall',
    description: 'Contract work area label: drywall',
    defaultMessage: 'Drywall',
  },
  assembly: {
    id: 'supplier.contract_work_area.assembly',
    description: 'Contract work area label: assembly',
    defaultMessage: 'Assembly',
  },
  scaffolding: {
    id: 'supplier.contract_work_area.scaffolding',
    description: 'Contract work area label: scaffolding',
    defaultMessage: 'Scaffolding',
  },
  land_works: {
    id: 'supplier.contract_work_area.land_works',
    description: 'Contract work area label: land works',
    defaultMessage: 'Land works',
  },
  casting_all_materials: {
    id: 'supplier.contract_work_area.casting_all_materials',
    description: 'Contract work area label: casting all materials',
    defaultMessage: 'Casting all materials',
  },
  framework: {
    id: 'supplier.contract_work_area.framework',
    description: 'Contract work area label: framework',
    defaultMessage: 'Framework',
  },
  masonry_and_plastering: {
    id: 'supplier.contract_work_area.masonry_and_plastering',
    description: 'Contract work area label: masonry and plastering',
    defaultMessage: 'Masonry and plastering',
  },
  other: {
    id: 'supplier.contract_work_area.other',
    description: 'Contract work area label: other',
    defaultMessage: 'Other',
  },
});

export const CONTRACT_WORK_AREAS = Object.keys(ContractWorkAreasMsgs);

export default ContractWorkAreasMsgs;
