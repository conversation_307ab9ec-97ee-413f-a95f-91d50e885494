import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { useIntl } from 'react-intl';

import StatusIconComponent from '../shared/StatusIconComponent';
import StatusMessages from '../shared/StatusMessages';
import StateMessages from '../shared/StateMessages';

import { featureActive } from '../../helpers/FeatureFlags';


const ProjectListRowComponent = React.forwardRef(({
  project,
  onClick,
  selected = false,
  narrow = false,
  noColspan = false,
  hideSuppliers = false,
  hideStatus = false,
  fixedWidth = false,
}, ref) => {
  const intl = useIntl();
  return (
    <tr
      ref={ref}
      onClick={onClick}
      className={classNames({
        'table-active': selected,
      })}
    >
      <td>
        <div>
          <div className="left title-block">
            <span>{project.name}</span>
            <br />
            <span
              className="sub-title text-muted">{project.project_id}</span>
          </div>
          <div className={`right ${narrow ? '' : 'hidden'}`}>
            {project.project_status && (
              <StatusIconComponent status={project.project_status} />
            )}
          </div>
        </div>
      </td>
      {!hideStatus && (
        <td
          className={classNames(
            'status-cell',
            `status-${project.project_status}`,
            'project-status',
            { 'fixed-width': fixedWidth },
            { hidden: narrow },
          )}
        >
          {project.project_status && (
            <span className="text-nowrap p-0">
              <StatusIconComponent status={project.project_status} />
              <span>{intl.formatMessage(StatusMessages[project.project_status])}</span>
            </span>
          )}
        </td>
      )}
      {/* eslint-disable indent */}
      {featureActive('suppliers') && !hideSuppliers && (
        <td
          className={classNames({ 'fixed-width': fixedWidth }, { hidden: narrow })}>
          {project.contractor_count}
        </td>
      )}
      {/* eslint-enable */}
      <td
        className={classNames({ 'fixed-width': fixedWidth }, { hidden: narrow })}
        colSpan={noColspan ? '1' : '2'}
      >
        {StateMessages.hasOwnProperty(project.project_state)
          ? intl.formatMessage(StateMessages[project.project_state])
          : ''}
      </td>
    </tr>);
});

ProjectListRowComponent.propTypes = {
  narrow: PropTypes.bool,
  project: PropTypes.object.isRequired,
  selected: PropTypes.bool,
  onClick: PropTypes.func,
  noColspan: PropTypes.bool,
  hideSuppliers: PropTypes.bool,
  hideStatus: PropTypes.bool,
  fixedWidth: PropTypes.bool,
};

export default ProjectListRowComponent;
