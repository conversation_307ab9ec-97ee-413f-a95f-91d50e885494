/* eslint-disable max-len */
import React from 'react';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import StoreSubscription from '../../helpers/StoreSubscription';
import { BulkImportWizardSteps } from '../../stores/BulkSupplierImportStore';
import { bulkSupplierImportStore, projectViewStore } from '../../stores/Stores';
import WizardComponent from '../shared/WizardComponent';
import WizardStep from '../shared/WizardStep';
import bulkSupplierAddCheck from '../../actions/actionCreators/BulkSupplierAddCheck';
import bulkSupplierAddReviewSubmit from '../../actions/actionCreators/BulkSupplierAddReviewSubmit';
import bulkSupplierAddCancel from '../../actions/actionCreators/BulkSupplierAddCancel';
import closeBulkSupplierImport from '../../actions/actionCreators/CloseBulkSupplierImport';
import BulkSupplierEnterIDsStep from './BulkSupplierEnterIDsStep';
import BulkSupplierImportReviewStep from './BulkSupplierImportReviewStep';
import BulkSupplierImportDoneStep from './BulkSupplierImportDoneStep';
/* eslint-enable max-len */

class BulkSupplierImportWizard extends React.Component {
  constructor(props) {
    super(props);

    this.storeSub = new StoreSubscription(bulkSupplierImportStore, this.onStoreChange.bind(this));
    this.projectViewSub = new StoreSubscription(projectViewStore, this.onStoreChange.bind(this));
    this.state = this.mapStoreToState(
      bulkSupplierImportStore.getState(),
      projectViewStore.getState()
    );
  }

  componentDidMount() {
    this.storeSub.activate();
    this.projectViewSub.activate();
  }

  componentWillUnmount() {
    this.storeSub.deactivate();
    this.projectViewSub.deactivate();
  }

  onStoreChange(/* storeState */) {
    this.setState(
      this.mapStoreToState(bulkSupplierImportStore.getState(), projectViewStore.getState())
    );
  }

  mapStoreToState(storeState, projectViewStoreState) {
    return {
      activeStep: storeState.activeStep,
      supplierGovOrgIdsToCheck: storeState.supplierGovOrgIdsToCheck,
      companiesToSubmit: storeState.companiesToSubmit,
      notSubmittedCompanies: storeState.notSubmittedCompanies,
      companiesProcessed: storeState.companiesProcessed,
      supplierCheckInProgress: storeState.supplierCheckInProgress,
      supplierSubmitInProgress: storeState.supplierSubmitInProgress,
      supplierCheckCancelInProgress: storeState.supplierCheckCancelInProgress,
      checkErrors: storeState.checkErrors,
      jobErrors: storeState.jobErrors,
      submitErrors: storeState.submitErrors,
      projectId: projectViewStoreState.selected_project_id,
      bulkSupplierAddOpen: projectViewStoreState.bulk_supplier_add_is_open,
    };
  }

  handleEnterDataNext(govOrgIds) {
    bulkSupplierAddCheck(this.state.projectId, govOrgIds);
  }

  handleReviewCancel() {
    bulkSupplierAddCancel(this.state.projectId);
  }

  handleReviewNext(companiesToSubmit) {
    bulkSupplierAddReviewSubmit(this.state.projectId, companiesToSubmit);
  }

  render() {
    if (!this.state.bulkSupplierAddOpen || !this.state.projectId) {
      return null;
    }

    return (
      <WizardComponent
        title={
          <FormattedMessage
            id="supplierBulkImport.wizardTitle"
            description="Bulk supplier import wizard title"
            defaultMessage="Add suppliers by batch"
          />
        }
        activeStep={this.state.activeStep}
        handleClose={closeBulkSupplierImport}
      >
        <WizardStep
          step={BulkImportWizardSteps.ENTER_STEP}
          title={
            <FormattedMessage
              id="supplierBulkImport.enterWizardStep"
              description="Bulk supplier import 'Enter' step"
              defaultMessage="Enter data"
            />
          }
        >
          <BulkSupplierEnterIDsStep
            handleNext={this.handleEnterDataNext.bind(this)}
            defaultValue={this.state.supplierGovOrgIdsToCheck}
            errors={this.state.checkErrors}
          />
        </WizardStep>

        <WizardStep
          step={BulkImportWizardSteps.REVIEW_STEP}
          title={
            <FormattedMessage
              id="supplierBulkImport.reviewWizardStep"
              description="Bulk supplier import 'Review' step"
              defaultMessage="Review data"
            />
          }
        >
          <BulkSupplierImportReviewStep
            handleNext={this.handleReviewNext.bind(this)}
            handleCancel={this.handleReviewCancel.bind(this)}
            companiesToSubmit={this.state.companiesToSubmit}
            notSubmittedCompanies={this.state.notSubmittedCompanies}
            companiesProcessed={this.state.companiesProcessed}
            supplierCheckInProgress={this.state.supplierCheckInProgress}
            supplierSubmitInProgress={this.state.supplierSubmitInProgress}
            supplierCheckCancelInProgress={this.state.supplierCheckCancelInProgress}
            checkErrors={this.state.checkErrors}
            jobErrors={this.state.jobErrors}
            submitErrors={this.state.submitErrors}
          />
        </WizardStep>

        <WizardStep
          step={BulkImportWizardSteps.SUBMIT_STEP}
          title={
            <FormattedMessage
              id="supplierBulkImport.submitWizardStep"
              description="Bulk supplier import 'Submit' step"
              defaultMessage="Submit data"
            />
          }
        >
          <BulkSupplierImportDoneStep
            handleDone={closeBulkSupplierImport}
            submittedCompanies={this.state.companiesToSubmit}
            notSubmittedCompanies={this.state.notSubmittedCompanies}
          />
        </WizardStep>
      </WizardComponent>
    );
  }
}

export default BulkSupplierImportWizard;
