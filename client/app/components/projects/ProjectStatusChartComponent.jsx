import c3 from 'c3';
import React from 'react';
import { defineMessages, injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { projectViewStore } from '../../stores/Stores';
import ProjectStatusChartLegendComponent from './ProjectStatusChartLegendComponent';
import StatusMessages from '../shared/StatusMessages';
import filterProjectTree from '../../actions/actionCreators/ProjectTreeFilter';
import projectTreeOpen from '../../actions/actionCreators/ProjectTreeOpen';
import StoreSubscription from '../../helpers/StoreSubscription';
import Spinner from '../shared/Spinner';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  viewEditView: {
    id: 'statusChart.viewEditView',
    description: 'A link to open project tree 1st line',
    defaultMessage: 'View/',
  },
  viewEditEdit: {
    id: 'statusChart.viewEditEdit',
    description: 'A link to open project tree 2nd line',
    defaultMessage: 'edit',
  },
});

const visitorMessages = defineMessages({
  visitor: {
    id: 'statusChart.visitorSliceTooltip',
    description: 'A tooltip for Visitor slice in the pie-chart',
    defaultMessage: 'Visiting company',
  },
});

const pieChartMessages = Object.assign({}, StatusMessages, visitorMessages);

class ProjectStatusChartComponent extends React.Component {
  static get propTypes() {
    return {
      intl: intlPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.treeSubscription = new StoreSubscription(projectViewStore, this.onTreeChange.bind(this));
    this.state = this.mapStoreToState(projectViewStore.getState());
  }

  componentDidMount() {
    this.treeSubscription.activate();
    this.drawGraph(this.state.buckets);
  }

  componentDidUpdate() {
    this.drawGraph(this.state.buckets);
  }

  componentWillUnmount() {
    this.treeSubscription.deactivate();
  }

  onTreeChange(projectViewState) {
    this.setState(this.mapStoreToState(projectViewState));
  }

  getStatuses(statusCounts) {
    return ['stop', 'investigate', 'incomplete', 'attention', 'ok'].map(value => ({
      value,
      contractors: statusCounts[value] || 0,
    }));
  }

  mapStoreToState(storeState) {
    let buckets = [];
    let statuses;
    let visitorTotal;
    let nonPaedTotal;

    if (storeState.project_tree) {
      statuses = this.getStatuses(storeState.project_tree.root.children_status_counts);
      visitorTotal = storeState.project_tree.root.visitor_count;
      nonPaedTotal = storeState.project_tree.root.nonpaed_count;
    } else {
      statuses = this.getStatuses({});
      visitorTotal = 0;
      nonPaedTotal = 0;
    }
    buckets = buckets.concat(statuses);

    return {
      loaded: storeState.project_tree_loaded,
      loading: storeState.project_tree_loading,
      failed: storeState.project_tree_failed,
      statuses,
      visitorTotal,
      nonPaedTotal,
      buckets,
    };
  }

  showTreePopup(status) {
    projectTreeOpen();
    filterProjectTree(status);
    $('#project-tree-popup').modal({
      show: true,
      keyboard: false,
    });
  }

  bucketsExist(buckets) {
    return buckets.some(s => s.contractors);
  }

  isFirefox() {
    return navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
  }

  drawGraph(buckets) {
    const { formatMessage } = this.props.intl;

    const self = this;

    let data;
    let labelFormat;
    let tooltip;

    if (this.bucketsExist(buckets)) {
      data = {
        columns: buckets.map(x => [x.value, x.contractors]),
        colors: {
          stop: '#DA2710',
          investigate: '#EBAE10',
          incomplete: '#777777',
          attention: '#1B7FCB',
          ok: '#269E20',
          visitor: '#4A438D',
        },
        type: 'donut',
        onclick(d) {
          self.showTreePopup(d.id);
        },
        order: null,
      };
      labelFormat = value => value;
      tooltip = {
        format: {
          value: (value /* , ratio, id, index */) => value,
          name: (name /* , ratio, id, index */) => formatMessage(pieChartMessages[name]),
        },
        // Workaround to fix incorrect tooltip position on FireFox
        // https://jira.tilaajavastuu.fi/browse/BOL-1415
        /* eslint-disable indent */
        position: this.isFirefox()
          ? (tooltipData, tooltipWidth, tooltipHeight /* , thisElement */) => {
              const chartElement = document.getElementById('project-status-chart');
              const containerWidth = chartElement.clientWidth;
              const containerHeight = chartElement.clientHeight;
              return {
                top: containerHeight / 2 - tooltipHeight / 2,
                left: containerWidth / 2 - tooltipWidth / 2,
              };
            }
          : null,
        /* eslint-enable indent */
      };
    } else {
      data = {
        columns: [['no_suppliers', 1]],
        colors: {
          no_suppliers: '#EEEEEE',
        },
        type: 'donut',
        order: null,
        labels: false,
      };
      labelFormat = () => '';
      tooltip = {
        show: false,
      };
    }
    if (this.canvas) {
      c3.generate({
        bindto: this.canvas,
        oninit() {
          // Place tree icon into the middle of donut chart
          const icon = this.arcs
            .append('text')
            .attr('x', -40)
            .attr('y', 5)
            .attr('id', 'project-tree-popup-source-icon')
            .attr('class', 'fa fa-sitemap supplier-tree-link')
            .style('text-anchor', 'middle')
            .text('\uf0e8') // http://fontawesome.io/icon/sitemap/
            .on('click', () => {
              self.showTreePopup();
            });

          // Place a label next to the icon

          // Since most translations do not fit into a single line, always
          // split them into two lines and apply text-decoration on mouse over.
          // This solves both https://jira.tilaajavastuu.fi/browse/BOL-978
          // and https://jira.tilaajavastuu.fi/browse/BOL-939
          const msgView = formatMessage(messages.viewEditView);
          const msgEdit = formatMessage(messages.viewEditEdit);
          this.arcs
            .append('text')
            .attr('y', -5)
            .attr('x', 10)
            .attr('id', 'project-tree-popup-source') // for robot tests
            .attr('class', 'underline-link supplier-tree-link')
            .style('text-anchor', 'middle')
            .on('click', () => {
              self.showTreePopup();
            })
            .append('tspan')
            .text(msgView)
            .append('tspan')
            .attr('x', 10)
            .attr('dy', 15)
            .text(msgEdit);

          // Re-position icon on different text length
          const textWidth = document
            .getElementById('project-tree-popup-source')
            .getComputedTextLength();
          const iconWidth = document
            .getElementById('project-tree-popup-source-icon')
            .getComputedTextLength();
          const isIE = false || !!document.documentMode;
          const isEdge = !isIE && !!window.StyleMedia;
          // Needed to add extra space when computing text length on IE/Edge
          // https://jira.tilaajavastuu.fi/browse/BOL-1621
          const extraSpace = isIE || isEdge ? 15 : 0;
          const totalWidth = iconWidth + textWidth + extraSpace;
          const iconXposition = iconWidth - totalWidth / 2;
          if (iconXposition > -40 && iconXposition < 0) {
            icon.attr('x', iconXposition);
          }
        },
        data,
        legend: {
          show: false,
        },
        tooltip,
        donut: {
          title: '',
          width: 40,
          label: {
            format: labelFormat,
          },
        },
      });
    }
  }

  render() {
    if (this.state.loaded) {
      return (
        <div>
          <div className="row">
            <div className="col-md-5 col-xs-12">
              <ProjectStatusChartLegendComponent
                statuses={this.state.statuses}
                visitorTotal={this.state.visitorTotal}
                nonPaedTotal={this.state.nonPaedTotal}
                handleOnClick={this.showTreePopup}
              />
            </div>
            <div className="col-md-7  col-xs-12">
              <div>
                <div
                  id="project-status-chart"
                  className={this.state.loading ? 'loading' : ''}
                  ref={elem => {
                    this.canvas = elem;
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      );
    } else if (this.state.failed) {
      return (
        <p className="text-warning">
          <FormattedMessage
            id="project.statuses.failed"
            description="Text indicating that statuses failed to load."
            defaultMessage="Can't load project statuses."
          />
        </p>
      );
    }
    return (
      <div>
        <div className="row">
          <div
            id="project-status-chart"
            className="loading"
            ref={elem => {
              this.canvas = elem;
            }}
          />
          <Spinner className="details-loading" />
        </div>
      </div>
    );
  }
}

export default injectIntl(ProjectStatusChartComponent);
