import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl, useIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import Dialog from '../shared/Dialog';
import SupplierRolesMessages from './SupplierRoles';

const ConfirmDowngradeTitle = () => (
  // eslint-disable-line react/no-multi-comp
  <FormattedMessage
    id="projectTree.supplierRoleDowngrade.title"
    description="Title for supplier role downgrade dialog"
    defaultMessage="Changing supplier role"
  />
);

const ConfirmDowngradeContent = (props) => {
  const intl = useIntl();
  return (
    <div>
      <div>
        <p>
          <FormattedMessage
            id="projectTree.SupplierRoleDowngradeDialogComponent.heading"
            description="Heading for role downgrade confirmation dialog"
            defaultMessage={
              'The supplier cannot have role {previous_role} at this level ' +
              'of supply chain. By performing this move the role will be ' +
              'changed to supplier and privileges removed.'
            }
            values={{
              previous_role:
                intl.formatMessage(SupplierRolesMessages[props.movedSupplierRole])
                  .toLowerCase(),
            }}
          />
        </p>
        <button
          id="confirm_downgrade"
          className="btn btn-sm btn-primary"
          onClick={props.handleConfirm}
        >
          <FormattedMessage
            id="projectTree.supplierRoleDowngradeDialog.confirmAction"
            description="OK button for confirm supplier role downgrade dialog"
            defaultMessage="OK"
          />
        </button>
        <button
          id="cancel_remove"
          className="btn btn-sm btn-primary right"
          onClick={props.handleCancel}
        >
          <FormattedMessage
            id="projectForm.cancelAction"
            description="Cancel removal of project supplier"
            defaultMessage="Cancel"
          />
        </button>
      </div>
    </div>
  );
};

const SupplierRoleDowngradeDialogComponent = () => (
  // eslint-disable-line react/no-multi-comp
  <Dialog dialogType="CONFIRM_SUPPLIER_ROLE_DOWNGRADE">
    <ConfirmDowngradeTitle />
    <ConfirmDowngradeContent />
  </Dialog>
);

ConfirmDowngradeContent.propTypes = {
  movedSupplierRole: PropTypes.string,
  handleConfirm: PropTypes.func,
  handleCancel: PropTypes.func,
};

export default injectIntl(SupplierRoleDowngradeDialogComponent);
