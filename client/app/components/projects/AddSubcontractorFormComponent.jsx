/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { defineMessages, injectIntl } from 'react-intl';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import PAFormMessages from './PAFormMessages';
import { featureActive } from '../../helpers/FeatureFlags';
import { StoreSubscription, cloneState } from '../../helpers/StoreSubscription';
import {
  subcontractorFormStore,
  companySearchFieldStore,
  contactPersonSearchFieldStore,
  projectViewStore,
} from '../../stores/Stores';
import SearchableField from '../shared/SearchableField';
import addSubcontractorsClearFindClicked from '../../actions/actionCreators/AddSubcontractorsClearFindClicked';
import addSubcontractorsFindChanged from '../../actions/actionCreators/AddSubcontractorsFindChanged';
import subcontractorContactPersonFind from '../../actions/actionCreators/SubcontractorContactPersonFind';
import addSubcontractorsCompanyFind from '../../actions/actionCreators/AddSubcontractorsCompanyFind';
import searchFieldChanged from '../../actions/actionCreators/SearchFieldChanged';
import searchFieldClearClicked from '../../actions/actionCreators/SearchFieldClearClicked';
import addSubcontractorsSave from '../../actions/actionCreators/AddSubcontractorsSave';
import addSubcontractorsSaveFailure from '../../actions/actionCreators/AddSubcontractorsSaveFailure';
import supplierRoleChanged from '../../actions/actionCreators/SupplierRoleChanged';
import supplierIsOneManCompanyChanged from '../../actions/actionCreators/SupplierIsOneManCompanyChanged';
import supplierHasCollectiveAgreementChanged from '../../actions/actionCreators/SupplierHasCollectiveAgreementChanged';
import supplierCollectiveAgreementNameChanged from '../../actions/actionCreators/SupplierCollectiveAgreementNameChanged';
import {
  ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON,
  SUPPLIER_UNLINKED,
  SUPPLIER_LINKED,
} from '../../Constants';
import { SUPPLIER_ROLES, SUPPLIER } from './SupplierRoles';
import {
  EmailVerifyInput,
  FieldErrorMsg,
  SearchableFieldAction,
  SubcontractorFormMessages,
  SubcontractorFormRoleDropdown,
  SubcontractorFormFieldRO,
  SubcontractorFormGeneralError,
  SubcontractorFormCountryDropdown,
  SubcontractorFormContractTypeDropdown,
  WorkAreasCheckboxes,
  ContractDateRange,
} from './SubcontractorFormSharedComponents';
import Config from '../../Config';
import { ELIGIBLE_CONTRACT_TYPES } from './SupplierContractTypes';
import { intlPropType } from '../i18n/IntlPropTypes';

const paFormMessages = PAFormMessages;

/* eslint-enable max-len */

const messages = defineMessages({
  companyBusinessId: {
    id: 'addSupplier.enter_company_business_id_placeholder',
    description: 'Placeholder for company business ID',
    defaultMessage: 'Company Business ID',
  },
  companyBusinessIdOrVAT: {
    id: 'addSupplier.enter_company_business_id_or_vat_placeholder',
    description: 'Placeholder for company business ID or VAT',
    defaultMessage: 'Company Business ID or VAT',
  },
  supplierSearchErrorCheckId: {
    id: 'addSupplier.search_error',
    description: 'Add supplier Business ID not found error',
    defaultMessage: 'This company does not exist in available registers. Please check Business ID',
  },
  supplierSearchErrorCheckIdOrVAT: {
    id: 'addSupplier.search_error_check_id_or_vat',
    description: 'Add supplier Business ID or VAT not found error',
    defaultMessage:
      'This company does not exist in available registers. Please check Business ID or VAT',
  },
  searchByVATNotSupportedCountries: {
    id: 'addSupplier.search_by_vat_info_not_supported_countries',
    description: 'Inform user about not supported countries searching by VAT',
    defaultMessage:
      'Search by VAT number is not supported for Sweden, Lithuania, Netherlands and Norway',
  },
  required: {
    id: 'supplierForm.field_is_required',
    description: 'Error message for required field left blank',
    defaultMessage: 'Required field.',
  },
});

FieldErrorMsg.propTypes = {
  errors: PropTypes.object,
  fieldName: PropTypes.string,
  intl: intlPropType.isRequired
};

class AddSubcontractorFormComponent extends React.Component {
  static get defaultProps() {
    return {
      supplierId: null,
      projectId: null,
      permissions: [],
    };
  }

  static get propTypes() {
    return {
      supplierId: PropTypes.string,
      supplierType: PropTypes.string,
      projectId: PropTypes.string,
      componentUpdateCallback: PropTypes.func,
      isRoot: PropTypes.bool,
      disableCtxBlockCallback: PropTypes.func,
      closeCallback: PropTypes.func.isRequired,
      permissions: PropTypes.arrayOf(PropTypes.string),
      intl: intlPropType.isRequired,
    };
  }

  // eslint-disable-next-line no-unused-vars
  static title(companyName, isPreannouncing, _isPaRestarting, _isEditProjectClient) {
    if (isPreannouncing) {
      return (
        <div>
          <FormattedMessage
            id="supplier.submenu.preannounce_subcontractor_form"
            description="Preannounce subsupplier form title"
            defaultMessage="Preannounce subsupplier to {companyName}"
            values={{ companyName }}
          />
        </div>
      );
    }
    return (
      <div>
        <FormattedMessage
          id="supplier.submenu.add_subcontractor_form"
          description="Add subcontractor form title"
          defaultMessage="Add supplier under {companyName}"
          values={{ companyName }}
        />
      </div>
    );
  }

  constructor(props) {
    super(props);

    this.formSubscription = new StoreSubscription(
      subcontractorFormStore,
      this.formChanged.bind(this)
    );

    this.companySearchCompanyFieldSub = new StoreSubscription(
      companySearchFieldStore,
      this.formChanged.bind(this)
    );

    this.contactPersonSearchCompanyFieldSub = new StoreSubscription(
      contactPersonSearchFieldStore,
      this.formChanged.bind(this)
    );

    this.projectViewSub = new StoreSubscription(projectViewStore, this.formChanged.bind(this));

    this.state = this.mapStoreToState(
      subcontractorFormStore.getState(),
      companySearchFieldStore.getState(),
      contactPersonSearchFieldStore.getState(),
      projectViewStore.getState(),
      {
        supplierId: props.supplierId,
        supplierType: props.supplierType,
      }
    );
  }

  componentDidMount() {
    this.formSubscription.activate();
    this.companySearchCompanyFieldSub.activate();
    this.contactPersonSearchCompanyFieldSub.activate();
    this.projectViewSub.activate();

    const rolesAvailable = this.rolesAvailable();
    if (rolesAvailable.length === 1) {
      setTimeout(() => supplierRoleChanged(rolesAvailable[0]), 0);
    }
  }

  componentDidUpdate() {
    if (this.props.componentUpdateCallback) {
      this.props.componentUpdateCallback();
    }
  }

  componentWillUnmount() {
    this.formSubscription.deactivate();
    this.companySearchCompanyFieldSub.deactivate();
    this.contactPersonSearchCompanyFieldSub.deactivate();
    this.projectViewSub.deactivate();
  }

  onSubmit(event) {
    if (event) {
      event.preventDefault();
    }
    switch (this.formActionBtn.id) {
      case 'company_find_button':
        const query = this.searchId ? this.searchId.value : '';
        const country = this.selectedCountry ? this.selectedCountry : 'se';
        if (query && country) {
          addSubcontractorsCompanyFind(
            this.props.projectId,
            query,
            country,
            'add_subcontractor_company_search'
          );
        }
        break;
      case 'contact_person_find_button':
        const email = this.state.contact_person_search_field.query;
        this.clearContactPersonError();
        subcontractorContactPersonFind(
          email,
          this.getFoundCompany().id,
          'contact_person_search_field'
        );
        break;
      case 'company_save_button':
        this.handleSave(false);
        break;
      default:
    }
  }

  onFindInput(event) {
    searchFieldChanged(event.target.value, 'add_subcontractor_company_search');
    // TODO: maybe find a better way to clear save errors
    addSubcontractorsFindChanged(event.target.value);
    searchFieldClearClicked('contact_person_search_field');
  }

  onCountryDropdownChanged() {
    searchFieldChanged(this.state.query, 'add_subcontractor_company_search');
    addSubcontractorsFindChanged(this.state.query);
  }

  onCompanySearchClear(event) {
    event.preventDefault();
    searchFieldClearClicked('add_subcontractor_company_search');
    // TODO: maybe find a better way to clear save errors
    addSubcontractorsClearFindClicked();
  }

  onSaveAndAdd(event) {
    // Process Save and add action
    event.preventDefault();
    this.handleSave(true);
    return false;
  }

  getContactEmail() {
    return this.state.contact_person_search_field.query;
  }

  getSupplierRole() {
    return this.state.supplierRole;
  }

  getSupplierType() {
    let { supplierType } = this.state;
    const supplierRole = this.getSupplierRole();
    if (!supplierType) {
      supplierType = supplierRole === SUPPLIER ? SUPPLIER_UNLINKED : SUPPLIER_LINKED;
    }
    return supplierType;
  }

  getContractType() {
    return this.state.contractType;
  }

  getContact() {
    let contact = null;
    const { results } = this.state.contact_person_search_field;
    if (results && results[0]) {
      // eslint-disable-next-line prefer-destructuring
      contact = results[0];
    }
    return contact;
  }

  getFoundCompany() {
    let company = null;
    const { results } = this.state.company_search_field;
    if (results && results[0]) {
      // eslint-disable-next-line prefer-destructuring
      company = results[0];
    }
    return company;
  }

  foundMultiple() {
    return (
      this.state.company_search_field.results && this.state.company_search_field.results.length > 1
    );
  }

  foundSingle() {
    return (
      this.state.company_search_field.results &&
      this.state.company_search_field.results.length === 1
    );
  }

  foundNone() {
    return (
      (this.state.company_search_field.found && this.state.company_search_field.results < 1) ||
      this.state.company_search_field.not_found
    );
  }

  formChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(
        subcontractorFormStore.getState(),
        companySearchFieldStore.getState(),
        contactPersonSearchFieldStore.getState(),
        projectViewStore.getState()
      )
    );
  }

  mapStoreToState(
    storeState,
    companySearchFieldState,
    contactPersonSearchFieldState,
    projectViewState,
    extraState = {}
  ) {
    return {
      ...cloneState(storeState),
      company_search_field: companySearchFieldState,
      contact_person_search_field: contactPersonSearchFieldState,
      isPAEnabled: featureActive('pre_announcements') && projectViewState.pa_form_enabled,
      skipPARegStep: featureActive('skip_pa_reg_step'),
      ...extraState,
    };
  }

  editableOneManBusiness() {
    const { formatMessage } = this.props.intl;
    return (
      <div
        id="one_man_business"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('one_man_business'),
        })}
      >
        <div className="form-control-label form-control-label-sm">
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.OneManBusiness)}
        </div>
        <input
          type="radio"
          value="true"
          name="one_man_business"
          id="one_man_business_yes"
          checked={this.state.is_one_man_company === true}
          onChange={() => {
            supplierIsOneManCompanyChanged(true);
            supplierHasCollectiveAgreementChanged(null);
            supplierCollectiveAgreementNameChanged(null);
          }}
        />

        <label className="form-control-label text-fzsm mr-3" htmlFor="one_man_business_yes">
          {formatMessage(paFormMessages.LabelYes)}
        </label>
        <input
          type="radio"
          value="false"
          name="one_man_business"
          id="one_man_business_no"
          checked={this.state.is_one_man_company === false}
          onChange={() => supplierIsOneManCompanyChanged(false)}
        />
        <label htmlFor="one_man_business_no" className="form-control-label text-fzsm">
          {formatMessage(paFormMessages.LabelNo)}
        </label>
        <FieldErrorMsg fieldName="one_man_business" errors={this.state.errors} />
        <div
          className={
            this.checkFormFieldErrors('one_man_business')
              ? 'field-help-has-danger mb-1'
              : 'field-help mb-1'
          }
        >
          <label className="field-help form-control-feedback">
            <span>
              <i className="fa fa-info-circle pr-3" />
            </span>
            {formatMessage(paFormMessages.OneManBusinessHelptext)}
          </label>
        </div>
      </div>
    );
  }

  editableCollectiveAgreement() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        id="collective_agreement"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('has_collective_agreement'),
        })}
      >
        <label className="form-control-label form-control-label-sm">
          <span className="text--red">*&nbsp;</span>
          {formatMessage(paFormMessages.CollectiveAgreement)}
        </label>
        <div>
          <input
            type="radio"
            value="true"
            name="pa_collective_agreement"
            id="pa_collective_agreement_yes"
            checked={this.state.has_collective_agreement === true}
            onChange={() => supplierHasCollectiveAgreementChanged(true)}
          />
          <label
            className="form-control-label text-fzsm mr-3"
            htmlFor="pa_collective_agreement_yes"
          >
            {formatMessage(paFormMessages.LabelYes)}
          </label>

          <input
            type="radio"
            value="false"
            name="pa_collective_agreement"
            id="pa_collective_agreement_no"
            checked={this.state.has_collective_agreement === false}
            onChange={() => {
              supplierHasCollectiveAgreementChanged(false);
              supplierCollectiveAgreementNameChanged(null);
            }}
          />
          <label htmlFor="pa_collective_agreement_no" className="form-control-label text-fzsm">
            {formatMessage(paFormMessages.LabelNo)}
          </label>
        </div>
        <FieldErrorMsg fieldName="has_collective_agreement" errors={this.state.errors} />
      </div>
    );
  }

  editableCollectiveAgreementType() {
    const { formatMessage } = this.props.intl;

    return (
      <div
        id="collective_agreement_name"
        className={classNames({
          'has-danger': this.checkFormFieldErrors('collective_agreement_name'),
        })}
      >
        <div className="mb-3">
          <label
            className="form-control-label form-control-label-sm"
            htmlFor="pa_collective_agreement_name"
          >
            <span className="text--red">*&nbsp;</span>
            {formatMessage(paFormMessages.CollectiveAgreementName)}
          </label>
          <input
            name="pa_collective_agreement_name"
            id="pa_collective_agreement_name"
            className="form-control form-control-sm"
            type="text"
            placeholder={formatMessage(paFormMessages.CollectiveAgreementPlaceholder)}
            value={this.state.collective_agreement_name || ''}
            onChange={e => supplierCollectiveAgreementNameChanged(e.target.value)}
          />
          <FieldErrorMsg fieldName="collective_agreement_name" errors={this.state.errors} />
        </div>
      </div>
    );
  }

  checkFormFieldErrors(fieldname) {
    if (this.state.errors && fieldname in this.state.errors) {
      return true;
    }
    return false;
  }

  handleSave(addAnother) {
    const { projectId } = this.props;
    const { supplierId: parentId } = this.state;

    const supplierType = this.getSupplierType();
    const supplierRole = this.getSupplierRole();
    const contractType = this.getContractType();
    const contactEmail = this.getContactEmail();

    let contactPerson = null;
    if (this.state.contact_person_search_field.results) {
      [contactPerson] = this.state.contact_person_search_field.results;
    }
    const newSubcontractorId = this.getFoundCompany().id;
    const newSubcontractorExternalId = this.getFoundCompany().external_id;

    const { locale, formatMessage } = this.props.intl;
    const requiredMsg = [{ [locale]: formatMessage(messages.required) }];
    const errors = {};

    if (!this.state.supplierRole) {
      errors.supplier_role = requiredMsg;
    }
    if (this.state.isPAEnabled) {
      if (!this.state.contractType) {
        errors.contract_type = requiredMsg;
      }

      if (ELIGIBLE_CONTRACT_TYPES.includes(this.state.contractType)) {
        if (!Array.isArray(this.state.contractWorkAreas) || !this.state.contractWorkAreas.length) {
          errors.contract_work_areas = requiredMsg;
        }
      }
      if (!this.state.contact_person_search_field.query) {
        errors.supplier_contacts = requiredMsg;
      }

      if (this.state.skipPARegStep) {
        if (this.state.is_one_man_company === null) {
          errors.one_man_business = requiredMsg;
        }
        if (
          this.state.is_one_man_company === false &&
          this.state.has_collective_agreement === null
        ) {
          errors.has_collective_agreement = requiredMsg;
        }
        if (
          this.state.has_collective_agreement === true &&
          (this.state.collective_agreement_name == null ||
            this.state.collective_agreement_name.trim().length === 0)
        ) {
          errors.collective_agreement_name = requiredMsg;
        }
      } else {
        // Contract dates are hidden when skipPARegStep is enabled.
        if (!this.state.contractStartDate) {
          errors.contract_start_date = requiredMsg;
        }
        if (!this.state.contractEndDate) {
          errors.contract_end_date = requiredMsg;
        }
      }
    }
    if (Object.keys(errors).length > 0) {
      addSubcontractorsSaveFailure(errors);
    } else {
      addSubcontractorsSave(
        projectId,
        parentId,
        newSubcontractorId,
        addAnother,
        supplierType,
        supplierRole,
        contractType,
        contactPerson,
        contactEmail,
        this.state.contractStartDate,
        this.state.contractEndDate,
        this.state.contractWorkAreas,
        this.state.is_one_man_company,
        this.state.has_collective_agreement,
        this.state.collective_agreement_name !== '' ? this.state.collective_agreement_name : null,
        newSubcontractorExternalId
      );
    }
  }

  clearContactPersonError() {
    if ((this.state.errors || {}).supplier_contacts) {
      delete this.state.errors.supplier_contacts;
      addSubcontractorsSaveFailure(this.state.errors);
    }
  }

  hasSearchError() {
    return (
      !this.state.company_search_field.is_find_step && (this.foundNone() || this.foundMultiple())
    );
  }

  isPrivatePersonError() {
    return this.state.error_type === ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON;
  }

  canAddExisting() {
    return !this.state.company_search_field.is_find_step && this.foundSingle();
  }

  cannotAddThisCompanyHere() {
    return this.state.errors.hasOwnProperty('parent_id');
  }

  rolesAvailable() {
    let roles = [];
    if (!this.props.isRoot) {
      roles = [SUPPLIER];
    } else if (this.props.permissions.includes('create_privileged_suppliers')) {
      roles = SUPPLIER_ROLES;
    } else {
      roles = [SUPPLIER];
    }
    return roles;
  }

  isNextDisabled() {
    return !this.state.company_search_field.query || this.state.company_search_field.not_found;
  }

  isSaveButtonDisabled() {
    return this.state.save_in_progress;
  }

  renderSearchError() {
    const { formatMessage } = this.props.intl;
    const supplierSearchError = messages.supplierSearchErrorCheckIdOrVAT;

    if (this.hasSearchError()) {
      if (this.isPrivatePersonError()) {
        return (
          <div className="form-control-feedback err-msg">
            <FormattedMessage
              id="addSupplier.search_cannot_add_private person"
              description="Indicate that user cannot add a sole trader that is not a private person"
              defaultMessage="Cannot add private person that is not a sole trader"
            />
          </div>
        );
      }
      return (
        <div className="form-control-feedback err-msg">{formatMessage(supplierSearchError)}</div>
      );
    } else if (this.cannotAddThisCompanyHere()) {
      return (
        <div className="form-control-feedback err-msg">
          <FormattedMessage
            id="addSupplier.cannot_add_error"
            description="Same supplier already exists at this level error"
            defaultMessage="This company is already a supplier at this level"
          />
        </div>
      );
    }

    return null;
  }

  renderSearch() {
    const { formatMessage } = this.props.intl;
    let formGroupClasses = 'form-group mb-1';
    if (this.hasSearchError() || this.cannotAddThisCompanyHere()) {
      formGroupClasses += ' has-danger';
    }
    const searchFieldLabel = SubcontractorFormMessages.companyBusinessIdOrVatLabel;
    const searchFieldPlaceholder = messages.companyBusinessIdOrVAT;

    return (
      <div>
        <SearchableField
          className={formGroupClasses}
          label={formatMessage(searchFieldLabel)}
          placeholder={formatMessage(searchFieldPlaceholder)}
          input_value={this.state.company_search_field.query}
          onChange={this.onFindInput.bind(this)}
          reference={ref => {
            this.searchId = ref;
          }}
          renderErrors={this.renderSearchError()}
          required
        >
          <a
            href="#"
            className={classNames('input-group-addon', 'bg-white', 'no-decoration')}
            id="clear_search_id"
            onClick={this.onCompanySearchClear.bind(this)}
          >
            <SearchableFieldAction
              search_in_progress={this.state.company_search_field.search_in_progress}
            />
          </a>
        </SearchableField>
        <div className="form-group">
          <label className="field-help form-control-feedback">
            <span>
              <i className="fa fa-info-circle pr-3" />
            </span>
            {formatMessage(messages.searchByVATNotSupportedCountries)}
          </label>
        </div>
        <SubcontractorFormCountryDropdown
          className="form-group"
          togglerFieldId="select_company_country"
          reference={ref => {
            this.selectedCountry = ref;
          }}
          countries={Config.countriesList[this.props.intl.locale]}
          onChange={this.onCountryDropdownChanged.bind(this)}
        />
      </div>
    );
  }

  renderFoundCompany() {
    const { formatMessage } = this.props.intl;

    let companyName = '';
    if (this.getFoundCompany()) {
      companyName = this.getFoundCompany().name;
    }

    return (
      <div id="search_results">
        <SubcontractorFormFieldRO
          label={formatMessage(SubcontractorFormMessages.companyNameLabel)}
          value={companyName}
          fieldId="subcontractor_form_name"
        />
      </div>
    );
  }

  renderCompanyRoles() {
    if (!this.canAddExisting()) {
      return null;
    }

    return (
      <SubcontractorFormRoleDropdown
        togglerFieldId="add_user_role"
        roles={this.rolesAvailable()}
        roleSelected={this.state.supplierRole}
        toggleCallback={this.props.disableCtxBlockCallback}
        errors={this.state.errors}
      />
    );
  }

  renderContractType() {
    if (this.state.isPAEnabled && this.canAddExisting()) {
      return (
        <SubcontractorFormContractTypeDropdown
          togglerFieldId="add_contract_type"
          typeSelected={this.state.contractType}
          errors={this.state.errors}
          toggleCallback={this.props.disableCtxBlockCallback}
        />
      );
    }
    return null;
  }

  renderDates() {
    if (this.state.isPAEnabled && !this.state.skipPARegStep && this.canAddExisting()) {
      return <ContractDateRange />;
    }
    return null;
  }

  renderWorkAreas() {
    if (
      this.state.isPAEnabled &&
      this.canAddExisting() &&
      ELIGIBLE_CONTRACT_TYPES.includes(this.state.contractType)
    ) {
      return <WorkAreasCheckboxes />;
    }
    return null;
  }

  renderCollectiveAgreement() {
    if (this.state.isPAEnabled && this.state.skipPARegStep && this.canAddExisting()) {
      return (
        <div id="collective_agreement_wrapper" className="form-group mb-1">
          {this.editableOneManBusiness()}
          {this.state.is_one_man_company === false && this.editableCollectiveAgreement()}
          {this.state.is_one_man_company === false &&
            this.state.has_collective_agreement &&
            this.editableCollectiveAgreementType()}
        </div>
      );
    }
    return null;
  }

  renderCompany() {
    if (this.canAddExisting()) {
      return this.renderFoundCompany();
    }
    return null;
  }

  renderContactEmail() {
    if (!this.canAddExisting()) {
      return null;
    }

    return (
      <EmailVerifyInput
        query={this.state.contact_person_search_field.query}
        search_in_progress={this.state.contact_person_search_field.search_in_progress}
        found={this.state.contact_person_search_field.found}
        not_found={this.state.contact_person_search_field.not_found}
        results={this.state.contact_person_search_field.results}
        errors={
          (this.state.contact_person_search_field.search_errors || {}).email ||
          (this.state.errors || {}).supplier_contacts
        }
        required={this.state.isPAEnabled}
        isPASubsupplier={this.state.isPAEnabled && !this.props.isRoot}
      />
    );
  }

  renderCancelButton() {
    return (
      <button
        id="company_cancel_button"
        className="btn btn-sm btn-link"
        role="button"
        type="reset"
        onClick={this.props.closeCallback}
        disabled={this.state.save_in_progress}
      >
        <FormattedMessage
          id="supplierForm.cancel_action"
          description="subcontractor cancel button label"
          defaultMessage="Cancel"
        />
      </button>
    );
  }

  renderActions() {
    const findCompanyActions = (
      <div className="d-flex align-items-center add-supplier-actions">
        <div className="justify-self-one-third-width">
          <button
            id="company_find_button"
            className="btn btn-sm btn-primary mr-3"
            type="submit"
            role="button"
            ref={ref => {
              this.formActionBtn = ref;
            }}
            disabled={this.isNextDisabled()}
          >
            <FormattedMessage
              id="supplierForm.button_next"
              description="Add subcontractor Next button label"
              defaultMessage="Next"
            />
          </button>
        </div>
        <div className="justify-self-one-third-width justify-self-right">
          {this.renderCancelButton()}
        </div>
      </div>
    );
    const findContactPersonActions = (
      <div className="d-flex align-items-center add-supplier-actions">
        <div className="justify-self-one-third-width">
          <button
            id="contact_person_find_button"
            className="btn btn-sm btn-primary mr-3"
            type="submit"
            role="button"
            ref={ref => {
              this.formActionBtn = ref;
            }}
            disabled={
              !this.state.contact_person_search_field.query ||
              this.state.contact_person_search_field.search_errors
            }
          >
            <FormattedMessage
              id="supplierForm.button_next"
              description="Add subcontractor Next button label"
              defaultMessage="Next"
            />
          </button>
        </div>
        <div className="justify-self-one-third-width justify-self-right">
          {this.renderCancelButton()}
        </div>
      </div>
    );

    const saveActions = (
      <div className="d-flex align-items-center add-supplier-actions">
        <div className="justify-self-one-third-width">
          <button
            id="company_save_button"
            className="btn btn-sm btn-primary mr-3"
            type="submit"
            role="button"
            ref={ref => {
              this.formActionBtn = ref;
            }}
            disabled={this.isSaveButtonDisabled()}
          >
            <FormattedMessage
              id="addSupplier.save_button"
              description="Add supplier Save button label"
              defaultMessage="Save"
            />
          </button>
        </div>
        <div className="wrap-text">
          <button
            id="company_save_and_add_another"
            className="btn btn-sm btn-link"
            role="button"
            onClick={this.onSaveAndAdd.bind(this)}
            disabled={this.isSaveButtonDisabled()}
          >
            <FormattedMessage
              id="addSupplier.save_and_add_action"
              description="Add supplier Save and add another action"
              defaultMessage="Save and add another"
            />
          </button>
        </div>
        <div className="justify-self-one-third-width justify-self-right">
          {this.renderCancelButton()}
        </div>
      </div>
    );

    let actions = null;

    if (this.state.company_search_field.is_find_step) {
      actions = findCompanyActions;
    } else if (!this.canAddExisting()) {
      actions = findCompanyActions;
    } else if (
      this.canAddExisting() &&
      this.state.contact_person_search_field.is_find_step &&
      this.state.contact_person_search_field.query
    ) {
      actions = findContactPersonActions;
    } else {
      actions = saveActions;
    }

    return <div>{actions}</div>;
  }

  render() {
    return (
      <form id="add_subcontractor_find_company" onSubmit={this.onSubmit.bind(this)}>
        {this.renderSearch()}
        {this.renderCompany()}
        {this.renderCompanyRoles()}
        {this.renderContractType()}
        {this.renderDates()}
        {this.renderWorkAreas()}
        {this.renderCollectiveAgreement()}
        {this.renderContactEmail()}

        <SubcontractorFormGeneralError
          errors={{ ...this.state.errors, ...this.state.contact_person_search_field.search_errors }}
        />
        {this.renderActions()}
      </form>
    );
  }
}

export default injectIntl(AddSubcontractorFormComponent);
