/* eslint-disable max-len */
import PropTypes from 'prop-types';

import React from 'react';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import AddSubcontractorFormComponent from './AddSubcontractorFormComponent';
/* eslint-enable max-len */
class AddSubcontractorInlineForm extends React.Component {
  static get propTypes() {
    return {
      companyName: PropTypes.string,
      supplierId: PropTypes.string,
      supplierType: PropTypes.string,
      projectId: PropTypes.string,
      permissions: PropTypes.arrayOf(PropTypes.string),
      closeCallback: PropTypes.func.isRequired,
      isRoot: PropTypes.bool,
      extraClassNames: PropTypes.arrayOf(PropTypes.string),
    };
  }

  componentWillUnmount() {
    setTimeout(() => this.props.closeCallback(), 0);
  }

  renderTitle() {
    return (
      <div>
        <FormattedMessage
          id="supplier.submenu.add_subcontractor_form"
          description="Add subcontractor from title"
          defaultMessage="Add supplier under"
        />
        <br />
        <span>{this.props.companyName}</span>
      </div>
    );
  }

  render() {
    const defaultClassNames = ['context-block', 'card', 'inline-add-subcontractor-form'];
    const extraClassNames = this.props.extraClassNames ? this.props.extraClassNames : [];

    return (
      <div className={classNames(defaultClassNames.concat(extraClassNames))}>
        <div className="heading d-flex justify-content-between">
          <div className="mr-5 pointer-events-none">
            <FormattedMessage
              id="projectDetails.inlineAddSupplierFormTitle"
              description="Inline project details add supplier form title"
              defaultMessage="Add suppliers to the project"
            />
          </div>
          <div>
            <a href="#" onClick={(e)=> {
              e.preventDefault();
              this.props.closeCallback();
            }}>
              <i className="fa fa-close" />
            </a>
          </div>
        </div>
        <div className="content">
          <AddSubcontractorFormComponent
            companyName={this.props.companyName}
            supplierId={this.props.supplierId}
            supplierType={this.props.supplierType}
            projectId={this.props.projectId}
            permissions={this.props.permissions}
            isRoot={this.props.isRoot}
            closeCallback={this.props.closeCallback}
          />
        </div>
      </div>
    );
  }
}

export default AddSubcontractorInlineForm;
