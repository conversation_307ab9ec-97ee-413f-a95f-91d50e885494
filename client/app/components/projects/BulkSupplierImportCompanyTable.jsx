import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../../components/i18n/FormattedMessage'

const BulkSupplierImportCompanyTable = ({
  companies,
  showPendingProgress = true
}) => {
  const pendingSpinner = showPendingProgress ? (
    <span>
      &nbsp;&nbsp;&nbsp;
      <i className="fa fa-spin fa-spinner fa-lg text-primary" />
    </span>
  ) : null;

  const getErrorText = company => {
    // eslint-disable-line react/no-multi-comp
    if (company.error === 'private_person_not_sole_trader') {
      return (
        <span className="text-danger">
          <em>
            <FormattedMessage
              id="bulkImport.privatePerson"
              defaultMessage="Person, not a company"
            />
          </em>
        </span>
      );
    }

    if (company.error === 'subscription_failure') {
      return (
        <span className="text-danger">
          <em>
            <FormattedMessage
              id="bulkImport.subscriptionFailed"
              defaultMessage="Subscription failed"
            />
          </em>
        </span>
      );
    }

    return (
      <span className="text-danger">
        <em>
          <FormattedMessage
            id="bulkImport.companyNotFound"
            defaultMessage="ID not found in registries"
          />
        </em>
      </span>
    );
  };

  const pendingText = (
    <span>
      <em>
        <FormattedMessage id="bulkImport.companyPending" defaultMessage="Pending" />
        {pendingSpinner}
      </em>
    </span>
  );
  const companyStatusTexts = {
    already_in_project: (
      <span>
        <em>
          <FormattedMessage
            id="bulkImport.companyAlreadyInProject"
            defaultMessage="Already in supply chain"
          />
        </em>
      </span>
    ),
    pending: pendingText,
    in_progress: pendingText,
  };
  return (
    <table className="table stv-table pointer-events-none">
      <tbody>
        {companies.map((company, idx) => (
          <tr key={idx}>
            <td>{company.gov_org_id}</td>
            <td>
              {company.status === 'error'
                ? getErrorText(company)
                : companyStatusTexts[company.status] || company.name}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

BulkSupplierImportCompanyTable.propTypes = {
  companies: PropTypes.array.isRequired,
  showPendingProgress: PropTypes.bool,
};

export default BulkSupplierImportCompanyTable;
