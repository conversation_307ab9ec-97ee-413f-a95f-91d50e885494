/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { defineMessages, injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import TypeaheadInputComponent from '../shared/TypeaheadInputComponent';
import LocalizedText from '../i18n/LocalizedText';
import { StoreSubscription, cloneState } from '../../helpers/StoreSubscription';
import { projectUsersStore, projectUsersAddFormStore } from '../../stores/Stores';
import projectUsersAddFormClose from '../../actions/actionCreators/ProjectUsersAddFormClose';
import projectUsersAvailable from '../../actions/actionCreators/ProjectUsersAvailable';
import projectUsersAdd from '../../actions/actionCreators/ProjectUsersAdd';
import projectUsersAddSelectedChanged from '../../actions/actionCreators/ProjectUsersAddSelectedChanged';
import { ROLE_OPTIONS } from './ProjectUsersRoles';
import { preventDefault } from '../../helpers/EventHandlers';
import UserMessages from './ProjectUsersMessages';
import DropdownMenuComponent from '../shared/dropdown/DropDownMenuComponent';
import supplierRoleChanged from '../../actions/actionCreators/SupplierRoleChanged';
import { intlPropType } from '../i18n/IntlPropTypes';
/* eslint-enable max-len */

const messages = defineMessages({
  usersListEmpty: {
    id: 'projectUsers.usersListEmpty',
    description: 'Value displayed when list is empty',
    defaultMessage: 'List is empty',
  },
  usersListLoading: {
    id: 'loading.loading',
    description: 'Value displayed when list is loading',
    defaultMessage: 'Loading...',
  },
  usersListSelectPlaceholder: {
    id: 'projectUsers.usersListSelectPlaceholder',
    description: 'Placeholder for custom dropdown',
    defaultMessage: 'Enter user name or email',
  },
});

class ProjectUsersAddComponent extends React.Component {
  static get propTypes() {
    return {
      projectId: PropTypes.string.isRequired,
      intl: intlPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.projectUsersSub = new StoreSubscription(projectUsersStore, this.storeChanged.bind(this));
    this.projectUsersAddFormSub = new StoreSubscription(
      projectUsersAddFormStore,
      this.storeChanged.bind(this)
    );
    this.state = this.mapStoreToState(
      projectUsersStore.getState(),
      projectUsersAddFormStore.getState()
    );
  }

  componentDidMount() {
    this.projectUsersSub.activate();
    this.projectUsersAddFormSub.activate();
    this.loadAvailableUsers();
  }

  componentWillUnmount() {
    this.projectUsersSub.deactivate();
    this.projectUsersAddFormSub.deactivate();
  }

  mapStoreToState(projectUsersStoreState, projectUsersAddFormStoreState) {
    return {
      ...cloneState(projectUsersStoreState),
      ...cloneState(projectUsersAddFormStoreState),
    };
  }

  storeChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(projectUsersStore.getState(), projectUsersAddFormStore.getState())
    );
  }

  loadAvailableUsers() {
    setTimeout(() => projectUsersAvailable(this.props.projectId), 0);
  }

  _hasUserAddOption() {
    const { userAddOption } = this.state;
    return !!userAddOption && Object.keys(userAddOption).length >= 1;
  }

  _onUserChange(event) {
    projectUsersAddSelectedChanged(event[0]);
  }

  _onInputClear() {
    projectUsersAddSelectedChanged();
  }

  _onSubmit(event) {
    preventDefault(event);

    if (!this._hasUserAddOption()) {
      console.log('Ignoring a request to add user as no user is provided');
      return;
    }

    projectUsersAdd(
      this.props.projectId,
      this.state.userAddOption.employment_contract_resource_id,
      this.state.userAddOption.org_id,
      this.state.dropdownMenuOption,
      this.state.userAddOption?.person_id
    );
  }

  hasError(fieldName) {
    return !!this.state.errors[fieldName];
  }

  rolesAvailable() {
    if (this.state.userAddOption && Object.keys(this.state.userAddOption).length) {
      return this.state.userAddOption.available_roles;
    }
    return [];
  }

  optionsAvailable() {
    const options = {};
    this.rolesAvailable().forEach(role => {
      options[role] = ROLE_OPTIONS[role];
    });

    return Object.values(options);
  }

  roleCurrent() {
    let current = this.state.dropdownMenuOption;
    if (!this.rolesAvailable().includes(current)) {
      [current] = this.rolesAvailable();
    }

    return ROLE_OPTIONS[current];
  }

  saveIsDisabled() {
    return !this._hasUserAddOption() || this.state.saveInProgress;
  }

  callback(role) {
    supplierRoleChanged(role);
  }

  renderError(fieldName) {
    if (!this.hasError(fieldName)) {
      return null;
    }
    return (
      <div className="err-msg">
        {this.state.errors[fieldName]
          .map((error, idx) => <LocalizedText key={idx} translations={error} />)
          .join(' ')}
      </div>
    );
  }

  renderOptions(options) {
    return options.map(option => (
      <option key={option.value} value={option.value}>
        {option.title}
      </option>
    ));
  }

  renderLoadingOrEmptyText() {
    const { formatMessage } = this.props.intl;
    if (!this.state.availableUsersLoading && this.state.availableUsers.length === 0) {
      return (
        <div>
          <i className="fa fa-spin fa-spinner fa-lg text-primary mr-3" />
          <span>{formatMessage(messages.usersListLoading)}</span>
        </div>
      );
    }
    return formatMessage(messages.usersListEmpty);
  }

  renderRoleMenu(disabled = false) {
    const { formatMessage } = this.props.intl;
    const togglerText = disabled ? '' : formatMessage(this.roleCurrent().title);
    const toggler = (
      <button
        disabled={disabled}
        role="button"
        className={classNames(
          'form-control',
          'form-control-sm',
          'dropdown-toggle',
          'd-flex',
          'align-items-center'
        )}
        type="button"
        id="add_user_role"
        aria-haspopup="true"
        aria-expanded="true"
      >
        <span>{togglerText}</span>
      </button>
    );
    if (disabled) {
      return toggler;
    }

    return (
      <DropdownMenuComponent
        options={this.optionsAvailable()}
        toggler={toggler}
        callback={this.callback}
        className="dropdown-role dropdown-role-"
      />
    );
  }

  renderAddProjectUserForm() {
    if (this.state.saveSuccess) {
      return null;
    }

    const { formatMessage } = this.props.intl;
    const usersInDropdown = this.state.availableUsers.map((user, index) => ({
      id: index,
      label: user.registered
        ? user.full_name
        : `(${formatMessage(UserMessages.pending_invitation)}) ${user.email}`,
      additional_text: user.has_bol_permission
        ? null
        : formatMessage(UserMessages.no_bol_permissions),
      email: user.registered ? user.email : null,
      org_id: user.org_id,
      employment_contract_resource_id: user.employment_contract_resource_id,
      person_id: user.person_id,
      available_roles: user.available_roles,
    }));

    return (
      <div className="card card-default mb-5">
        <div className="card-block filter-block">
          <form id="add_project_user" onSubmit={this._onSubmit.bind(this)}>
            <div className="row">
              <div className="col-12 col-lg-6">
                <label className="form-control-label form-control-label-sm">
                  <FormattedMessage
                    id="projects.projectUsers.searchLabel"
                    description="Label for searching user by name or email"
                    defaultMessage="Find user"
                  />
                </label>
                <TypeaheadInputComponent
                  className="resize-inputs-wrapper-sm"
                  id="add_user_select_user"
                  name="add_user_select_user"
                  onChange={this._onUserChange.bind(this)}
                  onInputClear={this._onInputClear.bind(this)}
                  disabled={false}
                  options={usersInDropdown}
                  multiple={false}
                  defaultSelected={usersInDropdown.length ? [usersInDropdown[0]] : []}
                  emptyLabel={this.renderLoadingOrEmptyText()}
                  placeholder={formatMessage(messages.usersListSelectPlaceholder)}
                  maxResults={1000}
                  ref={elem => {
                    this.typeaheadField = elem;
                  }}
                />
                {this.renderError('add_user_select_user')}
              </div>
              <div className="col-6 col-lg-3">
                <label className="form-control-label form-control-label-sm">
                  <FormattedMessage
                    id="projects.projectUsers.roleLabel"
                    description="Label for new user role dropdown"
                    defaultMessage="Define role"
                  />
                </label>
                {this.renderRoleMenu(!this._hasUserAddOption())}
                {this.renderError('user_role')}
              </div>

              <div
                className={classNames(
                  'col-6',
                  'col-lg-3',
                  'no-col-gutter-left',
                  'd-flex',
                  'align-items-end',
                  'justify-content-around'
                )}
              >
                <button
                  id="add_user_save_button"
                  className="btn btn-sm btn-primary"
                  role="button"
                  type="submit"
                  disabled={this.saveIsDisabled()}
                >
                  <FormattedMessage
                    id="projectUsers.saveButton"
                    description="Add project user Save button label"
                    defaultMessage="Save"
                  />
                </button>
                <a
                  id="add_user_cancel"
                  className="btn btn-sm btn-link"
                  href="#"
                  onClick={(e)=> {
                    projectUsersAddFormClose();
                    e.preventDefault();
                  }}
                >
                  <FormattedMessage
                    id="projectUsers.cancelAction"
                    description="Add project user Cancel action"
                    defaultMessage="Cancel"
                  />
                </a>
              </div>
            </div>
            {this.state.availableUsersError && (
              <div className="text-danger mt-2">
                <LocalizedText translations={this.state.availableUsersError} />
              </div>
            )}
          </form>
        </div>
      </div>
    );
  }

  render() {
    return this.renderAddProjectUserForm();
  }
}

export default injectIntl(ProjectUsersAddComponent);
