import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../../components/i18n/FormattedMessage'

import Dialog from '../shared/Dialog';

const ConfirmRemoveContent = props => {
  let removeMessage;
  let removableSuppliers;
  if (props.childrenCount <= 0) {
    removeMessage = (
      <FormattedMessage
        id="projectTree.removeDialog.removeSingleSupplier"
        description="Text in remove supplier confirmation dialog when removing single node"
        defaultMessage={
          'This action will remove supplier from the tree and cannot be ' +
          'undone (however you may add this supplier again).'
        }
      />
    );
  } else {
    removeMessage = (
      <FormattedMessage
        id="projectTree.removeDialog.removeSupplierWithChildren"
        description="Text in remove supplier confirmation dialog when removing node with"
        defaultMessage={
          'This action will remove supplier together with its ' +
          '{childrenCount} {childrenCount, plural, one {child} other ' +
          '{children}} suppliers from the tree and cannot be undone.'
        }
        values={{ childrenCount: props.childrenCount }}
      />
    );

    const suppliersList = props.removableSuppliers.map(supplier => (
      <li key={supplier.organization.name}>{supplier.organization.name}</li>
    ));

    removableSuppliers = (
      <div className="removable-suppliers">
        <FormattedMessage
          id="projectSuppliers.removeDialog.removableSupplier"
          description="Tell which supplier will be removed"
          defaultMessage="Will be removed:"
        />
        <ul>
          <li>{props.supplierName}</li>
        </ul>
        <FormattedMessage
          id="projectSuppliers.removeDialog.removableSuppliers"
          description="List children suppliers will be removed"
          defaultMessage="Will be removed ADDITIONALLY:"
        />
        <ul>{suppliersList}</ul>
      </div>
    );
  }

  return (
    <div>
      <div>
        <p>{removeMessage}</p>
        {removableSuppliers}
      </div>
      <div>
        <button
          id="confirm_remove"
          className="btn btn-sm btn-primary"
          onClick={props.handleConfirm}
        >
          <FormattedMessage
            id="projectTree.removeDialog.removeButton"
            description="Remove button for confirm supplier remove dialog"
            defaultMessage="Remove"
          />
        </button>
        <button
          id="cancel_remove"
          className="btn btn-sm btn-primary right"
          onClick={props.handleCancel}
        >
          <FormattedMessage
            id="projectForm.cancelAction"
            description="Cancel removal of project supplier"
            defaultMessage="Cancel"
          />
        </button>
      </div>
    </div>
  );
};

ConfirmRemoveContent.propTypes = {
  supplierName: PropTypes.string,
  childrenCount: PropTypes.number,
  removableSuppliers: PropTypes.array,
  handleConfirm: PropTypes.func,
  handleCancel: PropTypes.func,
};

const ConfirmRemoveTitle = (
  props // eslint-disable-line react/no-multi-comp
) => (
  <FormattedMessage
    id="projectTree.removeDialog.title"
    description="Title for remove supplier dialog"
    defaultMessage="Remove supplier {name}"
    values={{ name: props.supplierName }}
  />
);

ConfirmRemoveTitle.propTypes = {
  supplierName: PropTypes.string,
};

const RemoveFlatSupplierDialogComponent = props => (
  // eslint-disable-line react/no-multi-comp
  <Dialog dialogType="CONFIRM_REMOVE_FLAT_SUPPLIER" placement={props.placement}>
    <ConfirmRemoveTitle />
    <ConfirmRemoveContent />
  </Dialog>
);

RemoveFlatSupplierDialogComponent.propTypes = {
  placement: PropTypes.string,
};

export default RemoveFlatSupplierDialogComponent;
