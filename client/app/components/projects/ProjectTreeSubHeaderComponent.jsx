import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';

class ProjectTreeSubHeaderComponent extends React.Component {
  static get defaultProps() {
    return {
      expanded: false,
      disableExpandUpdate: false,
    };
  }

  static get propTypes() {
    return {
      trigger: PropTypes.node,
      expanded: PropTypes.bool,
      // eslint-disable-next-line react/no-unused-prop-types
      disableExpandUpdate: PropTypes.bool,
      classNames: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
      children: PropTypes.node,
    };
  }

  constructor(props) {
    super(props);
    this.state = {
      isExpanded: props.expanded,
    };
  }

  // TODO: get rid of UNSAFE React lifecycle method below
  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    // BOL-2331: We don't want to change expanded state when doing anything else
    // but expanding all tree before print preview
    if (!nextProps.disableExpandUpdate && nextProps.expanded !== this.state.isExpanded) {
      this.setState({
        isExpanded: nextProps.expanded,
      });
    }
  }

  toggleExpand(event) {
    event.preventDefault();
    this.setState({
      isExpanded: !this.state.isExpanded,
    });
    return false;
  }

  render() {
    const cssClasses = classNames(this.props.classNames, {
      expanded: this.state.isExpanded,
    });
    return (
      <div className={cssClasses}>
        <div
          className={classNames(
            'Collapsible__trigger',
            this.state.isExpanded ? 'is-open' : 'is-closed'
          )}
          onClick={this.toggleExpand.bind(this)}
        >
          {this.props.trigger}
        </div>
        {this.state.isExpanded && <ul>{this.props.children}</ul>}
      </div>
    );
  }
}

export default ProjectTreeSubHeaderComponent;
