import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import {
  authStore,
  companyViewStore,
  paFormStore,
  projectViewStore,
  paReviewStore,
} from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import {
  PA_STATUS_CREATED,
  PA_STATUS_REGISTERED,
  PA_STATUS_CONFIRMED,
  PA_STATUS_REJECTED,
  PA_CONFIRM_STATE_WAIT,
} from '../../Constants';
import { featureActive } from '../../helpers/FeatureFlags';
import PreannouncementForm from './PreannouncementForm';
import paModalCloseClicked from '../../actions/actionCreators/PreannouncementModalClose';
import rejectPreannouncement from '../../actions/actionCreators/RejectPreannouncement';
import confirmPreannouncement from '../../actions/actionCreators/ConfirmPreannouncement';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';

class PreannouncementView extends React.Component {
  static get propTypes() {
    return {
      router: routerPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.projectViewSub = new StoreSubscription(projectViewStore, this.storeChanged.bind(this));
    this.companyViewSub = new StoreSubscription(companyViewStore, this.storeChanged.bind(this));
    this.paFormSub = new StoreSubscription(paFormStore, this.storeChanged.bind(this));
    this.authSub = new StoreSubscription(authStore, this.storeChanged.bind(this));
    this.paReviewSub = new StoreSubscription(paReviewStore, this.storeChanged.bind(this));
    this.state = this.mapStoreToState(
      projectViewStore.getState(),
      companyViewStore.getState(),
      paFormStore.getState(),
      authStore.getState(),
      paReviewStore.getState()
    );
  }

  componentDidMount() {
    this.authSub.activate();
    this.projectViewSub.activate();
    this.companyViewSub.activate();
    this.paFormSub.activate();
    this.paReviewSub.activate();
    this.showPopupIfNeeded();

    // Catch '#preannouncement-popup' modal close using Esc and clean up stores
    $(document).on('hide.bs.modal', '#preannouncement-popup', () => {
      paModalCloseClicked(this.state.projectId, this.props.router.navigate);
    });
  }

  componentDidUpdate(/* prevProps, prevState */) {
    this.showPopupIfNeeded();
  }

  componentWillUnmount() {
    this.authSub.deactivate();
    this.projectViewSub.deactivate();
    this.companyViewSub.deactivate();
    this.paFormSub.deactivate();
    this.paReviewSub.deactivate();
    $(document).off('hide.bs.modal', '#preannouncement-popup');
  }

  mapStoreToState(projectState, companyState, paFormState, authState, paReviewState) {
    return {
      authProfile: authState.profile,
      paViewOpen: paFormState.pa_view_is_open,
      currentPAstatus: paFormState.current_pa_status,
      company_details: paFormState.current_pa_company_details,
      company_status: companyState.status,
      startSubmit: paFormState.startSubmit,
      preannouncement_get_error: paFormState.preannouncement_get_error,
      preannouncement_updated_error: paFormState.preannouncement_updated_error,
      currentPaId: paFormState.current_pa_id,
      assignedToCompanyId: paFormState.assigned_to_company_id,
      assignedToSupplierId: paFormState.assigned_to_supplier_id,
      assignedToTime: paFormState.assigned_to_time,
      paLoading: paFormState.pa_loading || !companyState.company_loaded,
      paUpdateInProgress: paFormState.update_in_progress || paReviewState.update_in_progress,
      projectId: projectState.selected_project_id,
      created_by_supplier_org: paFormState.created_by_supplier_org,
      submitted_time: paFormState.submitted_time,
      update_pa_form_errors: paFormState.update_pa_form_errors,
      pa_can_confirm_status: paFormState.pa_can_confirm_status,
    };
  }

  storeChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(
        projectViewStore.getState(),
        companyViewStore.getState(),
        paFormStore.getState(),
        authStore.getState(),
        paReviewStore.getState()
      )
    );
  }

  registerClicked() {
    this.setState({ startSubmit: true });
  }

  confirmClicked() {
    confirmPreannouncement(this.state.currentPaId, PA_STATUS_CONFIRMED);
  }

  rejectClicked() {
    rejectPreannouncement(this.state.currentPaId, PA_STATUS_REJECTED);
  }

  showPopupIfNeeded() {
    setTimeout(() => {
      $(this.preannouncementPopup).modal({
        show: true,
      });
    }, 0);
  }

  isPASubmitted() {
    return this.state.submitted_time;
  }

  isPACreatorOrgSameAsActiveOrg() {
    return this.state.created_by_supplier_org.id === this.state.authProfile.organisation_id;
  }

  isPAOrgSameAsLoggedUserOrg() {
    return this.state.authProfile.organisation_id === this.state.company_details.company_id;
  }

  isAssignedToOrgSameAsActiveOrg() {
    return this.state.authProfile.organisation_id === this.state.assignedToCompanyId;
  }

  shouldShowRegisterButtonsPanel() {
    return this.state.currentPAstatus === PA_STATUS_CREATED && this.isPAOrgSameAsLoggedUserOrg();
  }

  shouldShowReviewButtonsPanel() {
    return (
      this.state.currentPAstatus === PA_STATUS_REGISTERED && this.isAssignedToOrgSameAsActiveOrg()
    );
  }

  canConfirmButtonDisabled() {
    if (featureActive('skip_pa_reg_step')) {
      return (
        this.state.paUpdateInProgress ||
        (!this.state.company_status &&
          (this.state.pa_can_confirm_status === PA_CONFIRM_STATE_WAIT ||
            this.state.pa_can_confirm_status === null))
      );
    }
    return this.state.paUpdateInProgress;
  }

  renderCancelButton() {
    return (
      <button data-dismiss="modal" aria-label="Close" className="btn btn-sm btn-link">
        <FormattedMessage
          id="preannouncementDetails.pa_register_close"
          description="Preannouncement modal close button"
          defaultMessage="Close"
        />
      </button>
    );
  }

  renderRegisterButton() {
    return (
      <button
        id="pa_register_button"
        className="btn btn-sm btn-primary mr-3"
        type="submit"
        role="button"
        disabled={this.state.paUpdateInProgress}
        onClick={this.registerClicked.bind(this)}
      >
        <FormattedMessage
          id="preannouncementDetails.pa_register"
          description="Register preannouncement button label"
          defaultMessage="Register"
        />
      </button>
    );
  }

  renderConfirmButton() {
    return (
      <button
        id="pa_confirm_button"
        className="btn btn-sm btn-primary mr-3"
        type="submit"
        role="button"
        disabled={this.canConfirmButtonDisabled()}
        onClick={this.confirmClicked.bind(this)}
      >
        <FormattedMessage
          id="preannouncementDetails.pa_confirm"
          description="Preannouncement modal confirm button"
          defaultMessage="Confirm"
        />
      </button>
    );
  }

  renderConfirmAndSubmitButton() {
    return (
      <button
        id="pa_confirm_button"
        className="btn btn-sm btn-primary mr-3"
        type="submit"
        role="button"
        disabled={this.canConfirmButtonDisabled()}
        onClick={this.confirmClicked.bind(this)}
      >
        <FormattedMessage
          id="preannouncementDetails.pa_confirm_by_buyer"
          description="Preannouncement modal confirm button"
          defaultMessage="Confirm and submit"
        />
      </button>
    );
  }

  renderRejectButton() {
    return (
      <button
        id="pa_reject_button"
        className="btn btn-sm btn-primary mr-3"
        type="submit"
        role="button"
        disabled={this.state.paUpdateInProgress}
        onClick={this.rejectClicked.bind(this)}
      >
        <FormattedMessage
          id="preannouncementDetails.pa_reject"
          description="Preannouncement modal Reject button"
          defaultMessage="Reject"
        />
      </button>
    );
  }

  renderRegisterButtonsPanel() {
    return (
      <div id="pa_register_buttons" className="aligned_buttons_panel">
        {this.renderRegisterButton()}
        {this.renderCancelButton()}
      </div>
    );
  }

  renderReviewButtonsPanel() {
    return (
      <div id="pa_review_buttons" className="aligned_buttons_panel">
        {this.isPACreatorOrgSameAsActiveOrg() && !this.isPASubmitted()
          ? this.renderConfirmAndSubmitButton()
          : this.renderConfirmButton()}
        {this.renderRejectButton()}
        {this.renderCancelButton()}
      </div>
    );
  }

  renderButtonsPanel() {
    if (this.state.paLoading) {
      // do not render PA buttons while PA data is loading
      return null;
    }
    if (this.shouldShowReviewButtonsPanel()) {
      return this.renderReviewButtonsPanel();
    } else if (this.shouldShowRegisterButtonsPanel()) {
      return this.renderRegisterButtonsPanel();
    }
    return this.renderCancelButton();
  }

  render() {
    if (!this.state.paViewOpen) {
      this.preannouncementPopup = null;
      return null;
    }
    return (
      <div
        id="preannouncement-popup"
        className="modal project-tree"
        role="dialog"
        aria-labelledby="PopupModalLabel"
        aria-hidden="false"
        data-backdrop="false"
        tabIndex="-1"
        data-focus="false"
        ref={preannouncementPopup => {
          this.preannouncementPopup = preannouncementPopup;
        }}
      >
        <div className="modal-dialog project-tree-modal-dialog">
          <div id="preannouncement-content" className="modal-content" role="document">
            <div className="modal-header d-flex justify-content-between">
              <h4 className="modal-title pointer-events-none" id="PopupModalLabel">
                {this.state.currentPAstatus === 'created' && this.isPAOrgSameAsLoggedUserOrg() ? (
                  <FormattedMessage
                    id="preannouncementDetails.preannouncement_header_created"
                    description="Title of the created preannouncement popup"
                    defaultMessage="Register preannouncement for"
                  />
                ) : (
                  <FormattedMessage
                    id="preannouncementDetails.preannouncement_header_registered"
                    description="Title of the registered preannouncement popup"
                    defaultMessage="Preannouncement for"
                  />
                )}
                <span>{this.state.company_details.name}</span>
              </h4>
              <div role="button" data-dismiss="modal" aria-label="Close">
                <i className="fa fa-close" />
              </div>
            </div>
            <div className="modal-body">
              <div className="modal-payload">
                {this.state.preannouncement_get_error ? (
                  <FormattedMessage
                    id="preannouncementDetails.cant_load_pa"
                    description="PA cant be loaded title"
                    defaultMessage="Can't load preannouncement"
                  />
                ) : (
                  <PreannouncementForm startSubmit={this.state.startSubmit} />
                )}
              </div>
              <div>
                {(this.state.preannouncement_updated_error || this.state.update_pa_form_errors) && (
                  <div className="preannouncement_update_error">
                    <b>
                      <FormattedMessage
                        id="preannouncementDetails.pa_register_error"
                        description="Preannouncement register error message"
                        defaultMessage="Not all required fields have been filled"
                      />
                    </b>
                  </div>
                )}
                <div className="modal-actions d-flex justify-content-center">
                  {this.renderButtonsPanel()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default injectIntl(withRouter(PreannouncementView));
