/* eslint-disable max-len */
import React from 'react';

import AddSubcontractorInlineForm from './AddSubcontractorInlineForm';
import StoreSubscription from '../../helpers/StoreSubscription';
import { SUPPLIER_LINKED } from '../../Constants';
import { projectViewStore } from '../../stores/Stores';
import closeAddSingleSupplierView from '../../actions/actionCreators/AddSingleSupplierViewClose';
/* eslint-enable max-len */

class AddSingleSupplierView extends React.Component {
  constructor(props) {
    super(props);

    this.projectViewSub = new StoreSubscription(projectViewStore, this.onStoreChanged.bind(this));

    this.state = this.mapStoreToState(projectViewStore.getState());

    this.onKeyUp = this.onKeyUp.bind(this);
  }

  componentDidMount() {
    this.projectViewSub.activate();
    document.addEventListener('keyup', this.onKeyUp);
  }

  componentWillUnmount() {
    this.projectViewSub.deactivate();
    document.removeEventListener('keyup', this.onKeyUp);
  }

  onStoreChanged(/* storeState */) {
    this.setState(this.mapStoreToState(projectViewStore.getState()));
  }

  onKeyUp(event) {
    event.stopPropagation();
    if (event.keyCode === 27) {
      event.preventDefault();
      this.handleClose();
    }
  }

  mapStoreToState(projectViewStoreState) {
    return {
      addSingleSupplierOpen: projectViewStoreState.add_single_supplier_is_open,
      projectId: projectViewStoreState.selected_project_id,
      companySupplierId: projectViewStoreState.company_supplier_id,
      permissions: projectViewStoreState.permissions,
    };
  }

  handleClose() {
    closeAddSingleSupplierView();
  }

  render() {
    if (!this.state.addSingleSupplierOpen) {
      return null;
    }
    return (
      <div
        id="add-single-supplier"
        className="modal add-supplier"
        role="dialog"
        aria-hidden="false"
        tabIndex="-1"
        data-focus="false"
      >
        <div className="modal-dialog">
          <AddSubcontractorInlineForm
            supplierType={SUPPLIER_LINKED}
            supplierId={this.state.companySupplierId}
            projectId={this.state.projectId}
            permissions={this.state.permissions}
            closeCallback={closeAddSingleSupplierView}
            isRoot={!this.state.companySupplierId}
          />
        </div>
      </div>
    );
  }
}

export default AddSingleSupplierView;
