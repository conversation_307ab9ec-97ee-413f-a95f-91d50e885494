import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
// eslint-disable-next-line max-len
import getProjectReportSuppliersDetails from '../../actions/actionCreators/ProjectReportSuppliersDetails';
import { projectReportSuppliersDetailsStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import DetailsListItem from '../shared/DetailsListItem';
import StatusIconComponent from '../shared/StatusIconComponent';
import { getGovOrgId } from '../../helpers/Company';
import LocalizedText from '../i18n/LocalizedText';
// eslint-disable-next-line max-len
import CompanyDetailsStatusItemViewComponent from '../companies/CompanyDetailsStatusItemViewComponent';
import Spinner from '../shared/Spinner';
import { intlPropType } from '../i18n/IntlPropTypes';


class ProjectReportSuppliersDetails extends React.Component {
  static get propTypes() {
    return {
      suppliers: PropTypes.array.isRequired,
      intl: intlPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.suppliersDetailsSub = new StoreSubscription(
      projectReportSuppliersDetailsStore,
      this.storeChanged.bind(this)
    );
    this.state = this.mapStoreToState(projectReportSuppliersDetailsStore.getState());
  }

  componentDidMount() {
    this.suppliersDetailsSub.activate();
    setTimeout(() => getProjectReportSuppliersDetails(this.props.suppliers), 0);
  }

  componentWillUnmount() {
    this.suppliersDetailsSub.deactivate();
  }

  mapStoreToState(storeState) {
    return {
      suppliers: storeState.suppliers,
      loaded: storeState.loaded,
      loading: storeState.loading,
      failed: storeState.failed,
    };
  }

  storeChanged() {
    this.setState(this.mapStoreToState(projectReportSuppliersDetailsStore.getState()));
  }

  formatDate(dateStr, locale) {
    if (!dateStr) {
      return '';
    }
    const d = new Date(dateStr);
    return d.toLocaleDateString(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour12: false,
    });
  }

  renderCompanyDetails(supplier) {
    const finnishGovOrgId = getGovOrgId('FIN', supplier.gov_org_ids, true);
    const govOrgId = getGovOrgId(
      supplier.country,
      supplier.gov_org_ids,
      false,
      'registration_number'
    );
    const vatNumber = getGovOrgId(supplier.country, supplier.gov_org_ids, false, 'vat_number');
    return (
      <div className="col-lg-6 col-md-12 top-space">
        <div className="card noborder">
          <div className="details-list--list">
            <DetailsListItem classNames="company-name" printMode>
              <FormattedMessage id="companyDetails.companyNameLabel" />
              {supplier.name}
            </DetailsListItem>
            <DetailsListItem classNames="business-id" printMode>
              <FormattedMessage id="companyDetails.businessIdLabel" />
              {govOrgId}
            </DetailsListItem>
            {vatNumber && (
              <DetailsListItem classNames="vat-number" printMode>
                <FormattedMessage id="companyDetails.vatNumberLabel" />
                {vatNumber}
              </DetailsListItem>
            )}
            <DetailsListItem classNames="country" printMode>
              <FormattedMessage id="companyDetails.countryLabel" />
              {supplier.country}
            </DetailsListItem>
          </div>
        </div>

        {supplier.country !== 'FIN' && finnishGovOrgId && (
          <div className="card noborder pointer-events-none">
            <div className="card-title text-uppercase">
              <FormattedMessage id="companyDetails.finnishDetailsHeading" />
            </div>
            <div className="details-list--list">
              <DetailsListItem classNames="business-id" printMode>
                <FormattedMessage id="companyDetails.businessIdLabel" />
                {finnishGovOrgId}
              </DetailsListItem>
            </div>
          </div>
        )}
      </div>
    );
  }

  renderCompanyStatus(supplier) {
    const { locale } = this.props.intl;
    const { status } = supplier;
    if (!status) {
      return (
        <div className="col-md-12">
          <div className="card noborder bottom-space">
            <FormattedMessage
              id="projectReport.noReportRetrieved"
              description="Note on status retrieval when no status is available"
              defaultMessage="Status cannot be determined
                              as the report has not been retrieved yet."
            />
          </div>
        </div>
      );
    }

    const updatedDate = this.formatDate(status.updated_date, locale);
    return (
      <div className="col-md-12">
        <div className="card noborder bottom-space">
          <div className="d-flex pointer-events-none card-title text-uppercase">
            <FormattedMessage id="companyDetails.companyStatusHeading" />
            <StatusIconComponent
              status={status.overall_status}
              additionalClasses={['mx-3', 'align-self-center', 'status-cell', 'company-status']}
            />
            <span className={`status-cell status-${status.overall_status}`}>
              <LocalizedText translations={status.overall_status_text} />
            </span>
          </div>
          <div className="details-list--list">
            {status.items.map(item => (
              <CompanyDetailsStatusItemViewComponent
                key={item.label.en}
                label={<LocalizedText translations={item.label} />}
                label_status={item.status}
                interpretation={item.interpretation}
                icon={item.icon}
              />
            ))}
          </div>
          <div className="text-right">
            <FormattedMessage
              id="projectReport.latestRegisteredChange"
              description="When details of the supplier were last updated?"
              defaultMessage="Latest registered change: "
            />
            {updatedDate}
          </div>
        </div>
      </div>
    );
  }

  renderComments(companyDetails) {
    // companyDetails already contains comments in "comments" array
    if (!companyDetails.comments || companyDetails.comments.length === 0) {
      return (
        <div className="col-md-12">
          <div className="card noborder bottom-space">
            <div className="card-title text-uppercase">
              <FormattedMessage
                id="comments.title"
                description="Comments section title"
                defaultMessage="Comments"
              />
            </div>
            <FormattedMessage
              id="comments.emptyList"
              description="Message shown when comments list is empty"
              defaultMessage="There are no comments for this supplier in this project."
            />
          </div>
        </div>
      );
    }

    return (
      <div className="col-md-12">
        <div className="card noborder bottom-space">
          <div className="card-title text-uppercase">
            <FormattedMessage
              id="comments.title"
              description="Comments section title"
              defaultMessage="Comments"
            />
          </div>
          {this.renderCommentsReadOnly(companyDetails.comments)}
        </div>
      </div>
    );
  }

  renderCommentsReadOnly(comments) {

    return (
      <div className="pseudo-table table-striped">
        {comments.map((comment, index) => (
          <div key={comment.id || index} className="pseudo-row" style={{
            display: 'grid',
            gridTemplateColumns: 'max-content max-content max-content 1fr',
            gap: '10px',
            padding: '10px 0',
            borderBottom: '1px solid #eee'
          }}>
            <div className="comment-author" style={{ gridRow: 1, gridColumn: 1 }}>
              {comment.author}
            </div>
            <div
              className="comment-info"
              style={{ gridRow: 1, gridColumn: 2 }}
              title={this.formatDate(comment.created_timestamp, this.props.intl.locale)}
            >
              {this.formatDate(comment.created_timestamp, this.props.intl.locale)}
            </div>
            {comment.updated_timestamp && (
              <div
                className="comment-state"
                style={{ gridRow: 1, gridColumn: 3 }}
                title={`${this.formatDate(comment.updated_timestamp, this.props.intl.locale)} by ${comment.updater || 'Unknown'}`}
              >
                <FormattedMessage
                  id="comments.updated"
                  description="Label for changed comments"
                  defaultMessage="updated"
                />
              </div>
            )}
            <div className="d-flex align-items-center" style={{ gridRow: 2, gridColumn: '1 / span 4' }}>
              {!comment.is_read && <i className="fa fa-circle new-indicator" style={{ marginRight: '5px' }} />}
              <span className="comment-content" style={{ whiteSpace: 'pre-wrap' }}>
                {comment.deleted_timestamp ? (
                  <span className="comment-deleted">
                    <FormattedMessage
                      id="comments.deleted"
                      description="Label for deleted comments"
                      defaultMessage="deleted"
                    />
                    {' '}
                    {this.formatDate(comment.deleted_timestamp, this.props.intl.locale)}
                    {' '}
                    <FormattedMessage
                      id="comments.by"
                      description="Used for 'updated by' and 'deleted by'"
                      defaultMessage="by"
                    />
                    {' '}
                    {comment.deleter}
                  </span>
                ) : (
                  comment.comment
                )}
              </span>
            </div>
          </div>
        ))}
      </div>
    );
  }

  render() {
    return (
      <div className="project-report-suppliers-details">
        <h2>
          <FormattedMessage
            id="projectReport.projectSuppliersDetails"
            description="Heading for supplier details section"
            defaultMessage="Detailed information about suppliers"
          />
        </h2>
        {this.state.loading && <Spinner /> }
        {this.state.loaded &&
          this.state.suppliers.map((item, index) => (
            <div className="card border-grey bottom-space" key={index}>
              {this.renderCompanyDetails(item)}
              {this.renderCompanyStatus(item)}
              {this.renderComments(item)}
            </div>
          ))}
        {this.state.failed && <FormattedMessage id="companyDetails.loadingFailedMessage" />}
      </div>
    );
  }
}

export default injectIntl(ProjectReportSuppliersDetails);
