import React from 'react';
import FormattedMessage from '../../components/i18n/FormattedMessage'

import StatusIconComponent from '../shared/StatusIconComponent';

const SupplierCriteriaForSwedishCompanies = () => (
  <div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.fTaxControl"
        defaultMessage="F-tax control: Holds F-tax or FA-tax (sole trader)"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.companyStatus"
        defaultMessage="Company status: No status impact noted"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.companyForm"
        defaultMessage="Company/Organizational form for Swedish companies: AB, EF or HB/KB"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.vatRegister"
        defaultMessage="Swedish VAT register: Registered in the Swedish VAT-register
                        or the organizational form is not normally registered for VAT"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.payrollRegister"
        defaultMessage="Swedish payroll register: Registered in the payroll register"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.representativeCheck"
        defaultMessage="Representative check Swedish AB (LLC): No negative information found"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.debtBalanceTaxesAndFees"
        defaultMessage="Debt balance taxes and fees at Swedish Enforcement Authority:
                        The company has no tax debts or a smaller debt balance"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.debtBalanceCompanyOrPrivate"
        defaultMessage="Debt balance company or private individual at Swedish Enforcement Authority:
                        The company has no or small debts regarding company or private individuals"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.auditor"
        defaultMessage="Control of requirement for auditor: Has auditor"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.rating"
        defaultMessage="Rating: Moderate risk to very low risk for insolvency within 12 months"
      />
    </div>
  </div>
);

const SupplierCriteriaForForeignCompanies = () => (
  <div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForForeignCompanies.vatRegistration"
        defaultMessage="VAT registration: Registered to local VAT register"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.companyStatusActive"
        defaultMessage="Company status: Active"
      />
    </div>
    <div className="criterion">
      <StatusIconComponent status="ok" />
      <FormattedMessage
        id="projectReport.supplierCriteriaForSwedishCompanies.rating"
        defaultMessage="Rating: Moderate risk to very low risk for insolvency within 12 months"
      />
    </div>
  </div>
);

const ProjectReportDescription = () => (
  <div className="project-report-description">
    <h2>
      <FormattedMessage
        id="projectReport.reportDescription"
        description="Heanding for section Report description"
        defaultMessage="Report description"
      />
    </h2>
    <div className="symbols">
      <h3>
        <FormattedMessage
          id="projectReport.symbols"
          description="Heander for report legend"
          defaultMessage="Symbols"
        />
      </h3>
      <div className="symbol">
        <StatusIconComponent status="ok" />
        <FormattedMessage
          id="projectReport.okSymbolDescription"
          description="OK status description"
          defaultMessage="This company fulfills the criteria in ID06 Bolagsdeklaration"
        />
      </div>
      <div className="symbol">
        <StatusIconComponent status="attention" />
        <FormattedMessage
          id="projectReport.attentionSymbolDescription"
          description="Attention status description"
          defaultMessage="This company should provide additional information
                          regarding the reported deviations
                          or some information should be noted"
        />
      </div>
      <div className="symbol">
        <StatusIconComponent status="incomplete" />
        <FormattedMessage
          id="projectReport.incompleteSymbolDescription"
          description="Incomplete status description"
          defaultMessage="Information is missing or
                          we are waiting for confirmation from Creditsafe"
        />
      </div>
      <div className="symbol">
        <StatusIconComponent status="investigate" />
        <FormattedMessage
          id="projectReport.investigateSymbolDescription"
          description="Investigate status description"
          defaultMessage="This company does not fulfill the criteria
                          and needs to be investigated"
        />
      </div>
      <div className="symbol">
        <StatusIconComponent status="stop" />
        <FormattedMessage
          id="projectReport.stopSymbolDescription"
          description="Stop status description"
          defaultMessage="VERIFY THE COMPANY!"
        />
      </div>
    </div>
    <div className="supplier-criteria">
      <div>
        <h3>
          <FormattedMessage
            id="projectReport.supplierCriteriaForSwedishCompanies"
            description="Sub-section title for Supplier criteria SWE section"
            defaultMessage="Supplier criteria for Swedish companies"
          />
        </h3>
        <SupplierCriteriaForSwedishCompanies />
      </div>
      <div>
        <h3>
          <FormattedMessage
            id="projectReport.supplierCriteriaForForeignCompanies"
            description="Sub-section title for Supplier criteria non-SWE section"
            defaultMessage="Supplier criteria for foreign companies"
          />
        </h3>
        <SupplierCriteriaForForeignCompanies />
      </div>
    </div>
    <div className="enclosure">
      <div className="information-prep">
        <FormattedMessage
          id="projectReport.enclosure"
          description="Project report dislcaimer"
          defaultMessage="Information presented in ID06 Bolagsdeklaration
                          has been prepared by
                          Creditsafe i Sverige AB (556514-4408),
                          which is a credit reporting company authorized under
                          the Credit Information Act."
        />
      </div>
      <div className="information-coverage">
        <FormattedMessage
          id="projectReport.informationCoverage"
          description="Disclaimer about publishing certificates"
          defaultMessage="Information below is not covered by
                           the publishing certificates."
        />
      </div>
    </div>
  </div>
);

export default ProjectReportDescription;
