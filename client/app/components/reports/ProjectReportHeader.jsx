import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import id06Logo from '../../../static/img/id06-plain-logo.png';
import ReactToPrint from '../shared/ReactToPrint';
import { intlPropType } from '../i18n/IntlPropTypes';

class ProjectReportHeader extends React.Component {
  static get propTypes() {
    return {
      className: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      printContent: PropTypes.object,
      intl: intlPropType.isRequired
    };
  }

  getDateTime(locale) {
    const d = new Date();
    return d.toLocaleDateString(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  }

  render() {
    const { locale } = this.props.intl;
    const dateTimeStr = this.getDateTime(locale);

    return (
      <div className={this.props.className}>
        <div className="logo">
          <img src={id06Logo} alt="" />
          <div className="bottom-text">
            <FormattedMessage
              id="basepage.ID06"
              description="ID06 text bellow logo on print project tree preview"
              defaultMessage="ID06"
            />
          </div>
        </div>
        <span className="title">
          <FormattedMessage
            id="projectTree.title"
            description="Title of the project tree popup"
            defaultMessage="Project: "
          />{' '}
          <span>{this.props.name}</span>
        </span>
        <span className="float-right text-right">
          <div>
            <ReactToPrint
              removeAfterPrint
              content={() => this.props.printContent}
              trigger={() => (
                <a className="node-action action-link print-link" href="#">
                  <i className="fa fa-print fa-pr--bol" aria-hidden="true" />
                  <FormattedMessage id="projects.node.print" />
                </a>
              )}
            />
          </div>
          <div className="date">{dateTimeStr}</div>
        </span>
        <hr />
      </div>
    );
  }
}

export default injectIntl(ProjectReportHeader);
