import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import ProjectTreeComponent from '../projects/ProjectTreeComponent';

class ProjectReportSupplyChain extends React.Component {
  static get propTypes() {
    return {
      project: PropTypes.object.isRequired,
    };
  }

  render() {
    return (
      <div className="project-report-supply-chain">
        <h2>
          <FormattedMessage
            id="projectReport.projectSupplyChain"
            description="Title for section Project supply chain"
            defaultMessage="Project supply chain"
          />
        </h2>
        <ProjectTreeComponent
          projectId={this.props.project.projectId}
          expandedTree
          projectReportMode
        />
      </div>
    );
  }
}

export default injectIntl(ProjectReportSupplyChain);
