import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import ProjectDetailsComponent from '../projects/ProjectDetailsComponent';
import ProjectStatusChartLegendComponent from '../projects/ProjectStatusChartLegendComponent';

class ProjectReportSummary extends React.Component {
  static get propTypes() {
    return {
      project: PropTypes.object.isRequired,
    };
  }

  render() {
    const { statuses } = this.props.project;
    return (
      <div className="noborder details-overview">
        <h2>
          <FormattedMessage
            id="projectReport.reportSummary"
            description="Section header for project report summary"
            defaultMessage="Summary"
          />
        </h2>
        <div className="left-col col-3">
          <div className="card noborder">
            <div className="card-title text-uppercase pointer-events-none">
              <FormattedMessage
                id="projectDetails.detailsHeading"
                description="Section subheading in the project popup"
                defaultMessage="Project information"
              />
            </div>
            <ProjectDetailsComponent printMode project={this.props.project} />
          </div>
        </div>
        {statuses && (
          <div className="right-col col-3">
            <div className="card noborder pointer-events-none">
              <div className="card-title text-uppercase">
                <FormattedMessage
                  id="projectDetails.statusHeading"
                  description="Piechart legennd heading"
                  defaultMessage="Project status"
                />
              </div>
              <ProjectStatusChartLegendComponent
                statuses={statuses}
                visitorTotal={this.props.project.visitorTotal}
                nonPaedTotal={this.props.project.nonPaedTotal}
              />
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default injectIntl(ProjectReportSummary);
