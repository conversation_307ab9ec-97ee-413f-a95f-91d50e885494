import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl, defineMessages } from 'react-intl';

import { featureActive } from '../../helpers/FeatureFlags';
import { stopPropagation } from '../../helpers/EventHandlers';
import ReportIconComponent from '../shared/ReportIconComponent';
import StatusIconComponent from '../shared/StatusIconComponent';

const messages = defineMessages({
  openCompnyReportIconLabel: {
    id: 'openCompanyDetailsIconLabel',
    description: 'Label for company details link icon',
    defaultMessage: 'Open company details',
  },
});

const companyDetailsUrl = (orgId) => {
  const bolPrefix = featureActive('core_mitt_id06') ? 'bol' : '';
  return `/${bolPrefix}#/companies/${orgId}`
}

const CompanyReportRowComponent = props => (
  <div className="status-report-row">
    <div className="company-status-width">
      <StatusIconComponent status={props.company_status} />
    </div>
    <div className="company-name-width">{props.name}</div>
    <div className="company-id-width">{props.company_id}</div>
    <div className="vat-number-width">{props.vat_number}</div>
    {featureActive('projects') && <div className="projects-count-width">{props.project_count}</div>}
    <div className="company-report-width">
      <ReportIconComponent
        company_resource_id={props.company_resource_id}
        report_available={props.report_available}
      />
    </div>
    <div className="company-details-width">
      <a
        title={props.intl.formatMessage(messages.openCompnyReportIconLabel)}
        onClick={stopPropagation}
        href={companyDetailsUrl(props.company_resource_id)}
        target="_blank"
        rel="noopener noreferrer"
        className="no-decoration"
      >
        <span className="fa fa-link text-muted" />
      </a>
    </div>
  </div>
);

CompanyReportRowComponent.propTypes = {
  company_resource_id: PropTypes.string.isRequired,
  company_id: PropTypes.string.isRequired,
  vat_number: PropTypes.string,
  company_status: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  project_count: PropTypes.number,
  report_available: PropTypes.bool.isRequired,
  intl: PropTypes.object.isRequired,
};

export default injectIntl(CompanyReportRowComponent);
