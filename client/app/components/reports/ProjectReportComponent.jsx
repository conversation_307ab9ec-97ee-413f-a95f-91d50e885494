import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import ProjectReportHeader from './ProjectReportHeader';
import ProjectReportSummary from './ProjectReportSummary';
import ProjectReportDescription from './ProjectReportDescription';
import ProjectReportSupply<PERSON>hain from './ProjectReportSupplyChain';
import ProjectReportSuppliersDetails from './ProjectReportSuppliersDetails';
import Spinner from '../shared/Spinner';
import {
  projectViewStore,
  projectStore,
  projectInternalIdSaveStore,
  companyViewContextualStore,
} from '../../stores/Stores';
import { featureActive } from '../../helpers/FeatureFlags';
import StoreSubscription from '../../helpers/StoreSubscription';
import { isTreeEmpty, getFlatSuppliers } from '../../helpers/ProjectTree';
import selectProject from '../../actions/actionCreators/SelectProject';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';

class ProjectReportComponent extends React.Component {
  static get propTypes() {
    return {
      params: routerPropType
    };
  }

  constructor(props) {
    super(props);

    this.projectViewSub = new StoreSubscription(projectViewStore, this.storeChanged.bind(this));
    this.projectSub = new StoreSubscription(projectStore, this.storeChanged.bind(this));
    this.projectInternalIdSub = new StoreSubscription(
      projectInternalIdSaveStore,
      this.storeChanged.bind(this)
    );
    this.companyViewContextualStore = new StoreSubscription(
      companyViewContextualStore,
      this.storeChanged.bind(this)
    );
    this.state = this.mapStoreToState(
      projectViewStore.getState(),
      projectStore.getState(),
      projectInternalIdSaveStore.getState(),
      companyViewContextualStore.getState()
    );
  }

  componentDidMount() {
    this.projectViewSub.activate();
    this.projectSub.activate();
    this.projectInternalIdSub.activate();
    this.companyViewContextualStore.activate();
    this.loadProjectDetails();
  }

  componentWillUnmount() {
    this.projectSub.deactivate();
    this.projectViewSub.deactivate();
    this.projectInternalIdSub.deactivate();
    this.companyViewContextualStore.deactivate();
  }

  getStatuses(statusCounts) {
    return ['stop', 'investigate', 'incomplete', 'attention', 'ok'].map(value => ({
      value,
      contractors: statusCounts[value] || 0,
    }));
  }

  loadProjectDetails() {
    setTimeout(() => selectProject({ id: this.props.params.projectId }), 0);
  }

  mapStoreToState(storeState, projectStoreState, internalIdState, detailsStoreState) {
    let statuses;
    let visitorTotal;
    let nonPaedTotal;

    if (storeState.project_tree && storeState.project_tree.root) {
      statuses = this.getStatuses(storeState.project_tree.root.children_status_counts);
      visitorTotal = storeState.project_tree.root.visitor_count;
      nonPaedTotal = storeState.project_tree.root.nonpaed_count;
    } else {
      statuses = this.getStatuses({});
      visitorTotal = 0;
      nonPaedTotal = 0;
    }

    const projectTreeEmpty =
      storeState.project_tree_loaded && !storeState.project_tree_loading
        ? isTreeEmpty(storeState.project_tree.root)
        : false;

    /* eslint-disable indent */
    const suppliers =
      storeState.project_tree_loaded && storeState.project_tree.root
        ? getFlatSuppliers(
            storeState.project_tree.root.suppliers,
            storeState.project_tree.root.unlinked_suppliers,
            storeState.project_tree.root.project_responsible_org_name,
            storeState.project_tree.root.company_id
          ).filter(supplier => supplier.can_see_report)
        : [];
    /* eslint-enable indent */

    return {
      projectId: storeState.selected_project_id,
      projectCreatorName: storeState.created_by_org_name,
      addedClientName: storeState.added_client_name,
      addedClientBlocked: storeState.added_client_blocked,
      name:
        storeState.name ||
        (projectStoreState.projectById[storeState.selected_project_id] || {}).name,
      internalId: storeState.project_id,
      taxId: storeState.tax_id,
      state: storeState.state,
      startDate: storeState.start_date,
      endDate: storeState.end_date,
      permissions: storeState.permissions,
      projectTreeEmpty,
      loading: storeState.project_tree_loading,
      loaded: storeState.project_tree_loaded,
      failedToLoad: storeState.project_tree_failed,
      inlineAddSubcontractorOpen: storeState.inline_add_subcontractor_is_open,
      bulkSupplierAddOpen: storeState.bulk_supplier_add_is_open,
      internalIdForm: {
        formOpened: internalIdState.formOpened,
        formSaveInProgress: internalIdState.saveInProgress,
        formSaveFailed: internalIdState.saveFailed,
        formSaveSuccess: internalIdState.saveSuccess,
        errors: internalIdState.errors,
        errorsMetaData: internalIdState.errorsMetaData,
      },
      suppliers,
      company_details_target_ref: detailsStoreState.company_details_target_ref,
      company_details_is_open: detailsStoreState.company_details_is_open,
      company_details_company_id: detailsStoreState.company_details_company_id,
      company_details_company_name: detailsStoreState.company_details_company_name,
      confirmation_dialog_is_open: storeState.confirmation_dialog_is_open,
      statuses,
      visitorTotal,
      nonPaedTotal,
    };
  }

  storeChanged(/* storeState */) {
    this.setState(
      this.mapStoreToState(
        projectViewStore.getState(),
        projectStore.getState(),
        projectInternalIdSaveStore.getState(),
        companyViewContextualStore.getState()
      )
    );
  }

  renderLoadingError() {
    return (
      <div className="project-report ">
        <FormattedMessage
          id="projectReport.failedOrNoPermissions"
          description="Loading failed message in the project report"
          defaultMessage="Loading failed or no permission"
        />
      </div>
    );
  }

  render() {
    if (this.state.loaded) {
      if (featureActive('add_project_client') && this.state.addedClientBlocked) {
        return this.renderLoadingError();
      }
      return (
        <div className="project-report" id="project_report_page">
          <ProjectReportHeader
            className="report-header"
            name={this.state.name}
            printContent={this}
          />
          <ProjectReportSummary project={this.state} />
          <ProjectReportDescription />
          <ProjectReportSupplyChain project={this.state} />
          <ProjectReportSuppliersDetails suppliers={this.state.suppliers} />
        </div>
      );
    }
    const loadingMessage = this.state.loading ? <Spinner /> : this.renderLoadingError();
    return <div className="project-report ">{loadingMessage}</div>;
  }
}

export default injectIntl(withRouter(ProjectReportComponent));
