/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import moment from 'moment';
import classNames from 'classnames';
import { injectIntl } from 'react-intl';
import Collapsible from 'react-collapsible';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import setActiveOrganisation from '../../actions/actionCreators/SetActiveOrganisation';
import reloadApplication from '../../actions/actionCreators/ReloadApplication';
import { SELECTED_MITTID06_ORG_ID_STORAGE_KEY } from '../../Constants';
import StatusMessages from '../shared/StatusMessages';
import StoreSubscription from '../../helpers/StoreSubscription';
import { canRepresentOrg } from '../../helpers/Authentication';
import loadStatusChangeReport from '../../actions/actionCreators/LoadStatusChangeReport';
import { authStore, statusChangeReportStore } from '../../stores/Stores';
import { AuthStates } from '../../stores/AuthStore';
import ProjectListRowComponent from '../projects/ProjectListRowComponent';
import ProjectListHeadComponent from '../projects/ProjectListHeadComponent';
import CompanyReportHeadComponent from '../reports/CompanyReportHeadComponent';
import CompanyReportRowComponent from '../reports/CompanyReportRowComponent';
import Spinner from '../shared/Spinner';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { featureActive } from '../../helpers/FeatureFlags';
import { isQvarnCompanyId, convertQvarnCompanyIdToUuid } from '../../helpers/Company';
/* eslint-enable max-len */

class CompanyStatusChangeReportComponent extends React.Component {
  static contextType = require('../../MittID06Context').default;

  static get propTypes() {
    return {
      params: PropTypes.object.isRequired,
      intl: intlPropType.isRequired,
    };
  }

  constructor(props) {
    super(props);
    this.reportStore = new StoreSubscription(statusChangeReportStore, this.storeChanged.bind(this));
    this.authStore = new StoreSubscription(authStore, this.storeChanged.bind(this));
    this.state = this.mapStoreToState(statusChangeReportStore.getState(), authStore.getState());
  }

  componentDidMount() {
    this.reportStore.activate();
    this.authStore.activate();
    this.loadReport();
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.params.itemId !== this.props.params.itemId ||
      prevProps.params.orgId !== this.props.params.orgId
    ) {
      this.loadReport();
    }
  }

  componentWillUnmount() {
    this.authStore.deactivate();
    this.reportStore.deactivate();
  }

  storeChanged(/* storeState */) {
    this.setState(this.mapStoreToState(statusChangeReportStore.getState(), authStore.getState()));
  }

  mapStoreToState(storeState, authStoreState) {
    const { authState, auth_info: authInfo } = authStoreState;

    let selectedOrgId = null;
    if (featureActive('core_mitt_id06')) {
      selectedOrgId = window.localStorage.getItem(SELECTED_MITTID06_ORG_ID_STORAGE_KEY);
    } else {
      selectedOrgId = authInfo.topMenuParameters.selectedRepresentedOrganizationId
    }

    return {
      unauthorized: false,
      loading: storeState.loading,
      failed: storeState.failed,
      report: storeState.reportById[this.props.params.itemId],
      userProfile: authState === AuthStates.SIGNED_IN ? authInfo.userProfile : null,
      activeOrgId:
        authStoreState.authState === AuthStates.SIGNED_IN
          ? selectedOrgId
          : null,
    };
  }

  loadReport() {
    const { mittData } = this.context;
    let orgId = this.props.params.orgId;
    if (featureActive('core_mitt_id06') && isQvarnCompanyId(orgId)) {
      orgId = convertQvarnCompanyIdToUuid(orgId);
    }
    if (orgId && orgId !== this.state.activeOrgId) {
      if (canRepresentOrg(this.state.userProfile, orgId, mittData)) {
        setTimeout(() => {
          setActiveOrganisation(orgId, false);
          if (featureActive('core_mitt_id06')) {
            this.context.setSelectedOrganisation({uuid: orgId});
          } else {
            // Note: Reloading application and change to correct company in order to
            // load status change report only works for legacy BOL.
            reloadApplication();
          }
        });
      } else {
        this.setState({ unauthorized: true, report: null, loading: false });
      }
    } else {
      setTimeout(() => loadStatusChangeReport(this.props.params.itemId), 0);
    }
  }

  openCompanyInNewTab(id) {
    window.open(`/#/companies/${id}`, '_blank');
  }

  openProjectInNewTab(id) {
    const bolPrefix = featureActive('core_mitt_id06') ? 'bol' : '';
    window.open(`/${bolPrefix}#/projects/${id}`, '_blank');
  }

  prepareProject(project) {
    const projectIds =
      project.project_ids && project.project_ids.length ? project.project_ids[0] : null;
    const preparedProject = {
      id: project.id,
      project_id: projectIds && projectIds.length ? projectIds[0].project_id : '',
      name: project.names,
      project_status: project.project_status,
      project_state: project.state,
    };
    return preparedProject;
  }

  renderStatus(status) {
    const { formatMessage } = this.props.intl;
    return formatMessage(StatusMessages[status]);
  }

  renderStatuses() {
    return this.state.report.statuses.map(status => this.renderStatus(status)).join('/');
  }

  renderDate(timestamp) {
    return moment(timestamp).format('YYYY-MM-DD');
  }

  renderDates() {
    const oldDate = this.renderDate(this.state.report.old_status_cutoff_timestamp);
    const newDate = this.renderDate(this.state.report.new_status_cutoff_timestamp);

    return `${oldDate} - ${newDate}`;
  }

  renderTitle() {
    return (
      <FormattedMessage
        id="companyStatusChangeReport.title"
        description="Company status change report title"
        defaultMessage={'Companies that have changed their status to {status} ({dates})'}
        values={{
          status: this.renderStatuses(),
          dates: this.renderDates(),
        }}
      />
    );
  }

  render() {
    if (!this.state.report) {
      let message = (
        <FormattedMessage
          id="companyStatusChangeReport.failedMessage"
          description="Company status change report loading failed message"
          defaultMessage="Could not load report: bad link or server failure."
        />
      );

      if (this.state.loading) {
        message = <Spinner />;
      } else if (this.state.unauthorized) {
        message = (
          <FormattedMessage
            id="companyStatusChangeReport.unauthorizedMessage"
            description="Company status change report unauthorized message"
            defaultMessage="Could not load report: unauthorized."
          />
        );
      }

      return (
        <div id="company_status_change_report_page" className="container page">
          {message}
        </div>
      );
    }
    const daily = !!(this.state.report.kind && this.state.report.kind.toLowerCase() === 'daily');
    return (
      <div id="company_status_change_report_page" className="container page">
        <h3 className="text-primary pt-5 pointer-events-none">{this.renderTitle()}</h3>
        <CompanyReportHeadComponent />
        {this.state.report.companies.map(company => (
          <div key={company.company_id}>
            <Collapsible
              trigger={
                <CompanyReportRowComponent
                  key={company.company_id}
                  company_resource_id={company.company_id}
                  company_id={company.gov_org_id}
                  vat_number={company.vat_number}
                  company_status={company.new_status}
                  project_count={company.project_count}
                  name={company.company_name}
                  report_available={!!(company.report_link && company.report_link.length)}
                  intl={this.props.intl}
                />
              }
              open={daily}
            >
              <table
                className={classNames(
                  'table',
                  'table--bol',
                  'table-striped',
                  'table-striped--bol',
                  'project-list'
                )}
              >
                <thead>
                  <ProjectListHeadComponent hideStatus hideSuppliers hideAddProject />
                </thead>
                <tbody className="table-clickable-rows--bol">
                  {company.projects.map(project => (
                    <ProjectListRowComponent
                      key={project.id}
                      project={this.prepareProject(project)}
                      onClick={() => this.openProjectInNewTab(project.id)}
                      noColspan
                      hideSuppliers
                      hideStatus
                      fixedWidth
                    />
                  ))}
                </tbody>
              </table>
            </Collapsible>
          </div>
        ))}
      </div>
    );
  }
}

export default injectIntl(withRouter(CompanyStatusChangeReportComponent));
