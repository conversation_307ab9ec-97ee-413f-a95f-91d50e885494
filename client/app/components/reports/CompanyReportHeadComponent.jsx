import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { featureActive } from '../../helpers/FeatureFlags';

const CompanyReportHeadComponent = () => (
  <div className="status-report-row header-text pointer-events-none">
    <div className="company-status-width">
      <FormattedMessage
        id="companies.status"
        description="Company status table column heading"
        defaultMessage="Status"
      />
    </div>
    <div className="company-name-width">
      <FormattedMessage
        id="companies.nameColumn"
        description="Company name table column heading"
        defaultMessage="Company"
      />
    </div>
    <div className="company-id-width">
      <FormattedMessage
        id="companies.businessIdColumn"
        description="Company id table column heading"
        defaultMessage="Business ID"
      />
    </div>
    <div className="vat-number-width">
      <FormattedMessage
        id="companies.VATColumn"
        description="Company VAT number"
        defaultMessage="VAT"
      />
    </div>
    {featureActive('projects') && (
      <div className="projects-count-width">
        <FormattedMessage
          id="companies.ProjectsColumn"
          description="Company projects count table column heading"
          defaultMessage="Projects"
        />
      </div>
    )}
    <div className="company-report-width">
      <FormattedMessage
        id="companies.reportColumn"
        description="Company report table column heading"
        defaultMessage="Report"
      />
    </div>
    <div className="company-details-width">
      <FormattedMessage
        id="companies.companyDetailsLink"
        description="Company details link table column heading"
        defaultMessage="Details"
      />
    </div>
  </div>
);

export default injectIntl(CompanyReportHeadComponent);
