import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../components/i18n/FormattedMessage'
import { featureActive } from '../helpers/FeatureFlags';
import authSignIn from '../actions/actionCreators/AuthSignIn';

function signIn(event) {
  event.preventDefault();
  authSignIn();
}

const LoginPageComponent = props => (
  <div id="login_page" className="container page">
    <h1>
      {featureActive('use_stv_theme') ? (
        <FormattedMessage id="basepage.stv.page_title" defaultMessage="Welcome to BOL" />
      ) : (
        <FormattedMessage
          id="basepage.id06.page_title"
          defaultMessage="Welcome to ID06 Bolagsdeklaration"
        />
      )}
    </h1>
    <p>
      {featureActive('use_stv_theme') ? (
        <FormattedMessage
          id="basepage.stv.welcome_text"
          defaultMessage={'BOL is a company screening service provided by {provider}.'}
          values={{
            provider: props.provider,
          }}
        />
      ) : (
        <FormattedMessage
          id="basepage.id06.welcome_text"
          defaultMessage={
            'ID06 Bolagsdeklaration is a company screening service provided by {provider}.'
          }
          values={{
            provider: props.provider,
          }}
        />
      )}
    </p>
    <p className="text-center">
      <button
        id="login_page_signin_link"
        role="button"
        href="#"
        className="btn btn-primary btn-two-line btn-xl"
        onClick={signIn}
      >
        <FormattedMessage
          id="basepage.sign_in_button"
          defaultMessage="Please{br}Sign in"
          values={{ br: <br /> }}
        />
      </button>
    </p>

    {featureActive('use_stv_theme') && (
      <p>
        <FormattedMessage
          id="basepage.register_new_company_body"
          defaultMessage={'If you do not have a user account, please {register_new_company}.'}
          values={{
            register_new_company: (
              <a
                href={props.registerNewCompanyUrl}
                target="_blank"
                rel="noopener noreferrer"
                id="register-url"
              >
                <FormattedMessage
                  id="basepage.register_new_company_link"
                  defaultMessage="register an organisation"
                />
              </a>
            ),
          }}
        />
      </p>
    )}
  </div>
);

LoginPageComponent.propTypes = {
  provider: PropTypes.any.isRequired,
  registerNewCompanyUrl: PropTypes.string.isRequired,
};

export default LoginPageComponent;
