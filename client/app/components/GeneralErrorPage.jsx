import React, { useState, useEffect } from 'react';
import LocalizedText from './i18n/LocalizedText';
import StoreSubscription from '../helpers/StoreSubscription';
import { generalErrorStore } from '../stores/Stores';
import FormattedMessage from './i18n/FormattedMessage';

const GeneralErrorPage = () => {
  const [state, setState] = useState(generalErrorStore.getState());

  useEffect(() => {
    const storeChanged = (storeState) => {
      setState({
        title: storeState.title,
        details: storeState.details,
      });
    };

    const storeSubscription = new StoreSubscription(generalErrorStore, storeChanged);
    storeSubscription.activate();

    return () => {
      storeSubscription.deactivate();
    };
  }, []);

  const { title, details } = state;

  return (
    <div style={containerStyle}>
      <h1><FormattedMessage
        id={title}
        description="Error title"
        defaultMessage="Something went wrong"
      /></h1>
      <LocalizedText translations={details} />
    </div>
  );
};

const containerStyle = {
  textAlign: 'center',
  padding: 20,
};

export default GeneralErrorPage;
