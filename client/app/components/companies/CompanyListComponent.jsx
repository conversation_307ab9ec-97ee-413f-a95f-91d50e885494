/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import InfiniteScroll from 'react-infinite-scroller';
import { nodeScrollIntoView } from '../../helpers/positioning';
import { featureActive } from '../../helpers/FeatureFlags';

import loadCompanyList from '../../actions/actionCreators/CompanyListLoading';
import loadMoreCompanyList from '../../actions/actionCreators/CompanyListLoadMore';
import { companyStore, userActionsStore } from '../../stores/Stores';
import CompanyListHeadComponent from './CompanyListHeadComponent';
import CompanyListRowComponent from './CompanyListRowComponent';
import StoreSubscription from '../../helpers/StoreSubscription';
import ListMessageRowComponent from '../shared/ListMessageRowComponent';
import companyDetailsOpen from '../../actions/actionCreators/CompanyDetailsOpen';
import detailsViewClose from '../../actions/actionCreators/DetailsViewClose';
import Spinner from '../shared/Spinner';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';
/* eslint-enable max-len */

const StatusOrder = ['stop', 'investigate', 'incomplete', 'attention', 'ok'];

class CompanyListComponent extends React.Component {

  static get propTypes() {
    return {
      narrow: PropTypes.bool,
      selectedItemId: PropTypes.string,
      router: routerPropType.isRequired,
    };
  }

  static get defaultProps() {
    return {
      narrow: false,
      selectedItemId: null,
    };
  }

  constructor(props) {
    super(props);
    this.companySubscription = new StoreSubscription(
      companyStore,
      this.companiesChanged.bind(this)
    );
    this.userActionsSubscription = new StoreSubscription(
      userActionsStore,
      this.userActionsChanged.bind(this)
    );
    const storeState = companyStore.getState();
    this.state = Object.assign({}, this.mapStoreToState(storeState), {
      sortBy: 'name',
      sortDir: -1,
    });
    this.userScrollInProgress = userActionsStore.getState().scrolling;
    this.onSort = this.onSort.bind(this);
    this.selectedRowRef = React.createRef();
  }

  componentDidMount() {
    this.companySubscription.activate();
    this.userActionsSubscription.activate();
    setTimeout(() => {
      loadCompanyList(false);
    }, 0);
  }

  componentDidUpdate() {
    if (!this.userScrollInProgress && this.props.narrow && !this.state.loading) {
      // https://jira.tilaajavastuu.fi/browse/BOL-1766
      // if selected company does exist in the details view list,
      // scroll it into the view
      if (this.selectedRowRef.current) {
        nodeScrollIntoView(this.selectedRowRef);
      } else if (this.state.loadMore) {
        // load next available page
        setTimeout(() => {
          loadCompanyList(true);
        }, 0);
      }
    }
  }

  componentWillUnmount() {
    this.companySubscription.deactivate();
    this.userActionsSubscription.deactivate();
  }

  onSort(column) {
    const { sortBy, sortDir } = this.state;
    this.setState({
      sortBy: column,
      sortDir: column === sortBy ? -sortDir : 1,
    });
  }

  mapStoreToState(storeState) {
    return {
      loaded: storeState.loaded,
      loading: storeState.loading,
      companies: storeState.companies,
      loadMore: storeState.loadMore,
    };
  }

  companiesChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  userActionsChanged(storeState) {
    // https://jira.tilaajavastuu.fi/browse/BOL-1766
    // do not use state for scrolling property since this causes
    // firing of componentDidUpdate() during user scrolling and
    // breaks down 'scroll into view' behaviour
    this.userScrollInProgress = storeState.scrolling;
  }

  rowClicked(company) {
    let companyId = company.id;
    if (featureActive('archived_reports')) {
      companyId = companyId.substring(companyId.indexOf('/') + 1);
    }

    if (companyId === this.props.selectedItemId) {
      detailsViewClose();
      this.props.router.navigate('/companies');

      return;
    }

    companyDetailsOpen(companyId, this.props.router.navigate);
  }

  loadMore() {
    if (!this.state.loading && this.state.loadMore) {
      setTimeout(loadMoreCompanyList, 0);
    }
  }

  colSpan() {
    const many = featureActive('projects') ? 5 : 4;
    return this.props.narrow ? 1 : many;
  }

  render() {
    const { narrow } = this.props;
    const { sortBy, sortDir } = this.state;

    let rows = [...this.state.companies];
    if (featureActive('archived_reports')) {
      rows.sort((a, b) => {
        if (sortBy === 'name') {
          if (a.name < b.name) return sortDir;
          if (a.name > b.name) return -sortDir;
          return 0;
        }

        if (sortBy === 'status') {
          return (
            -sortDir *
            (StatusOrder.indexOf(a.company_status) - StatusOrder.indexOf(b.company_status))
          );
        }

        if (sortBy === 'country') {
          if (a.country < b.country) return sortDir;
          if (a.country > b.country) return -sortDir;
          return 0;
        }

        return 0;
      });
    }

    rows = rows.map(company => {
      let companyId = company.id;
      if (featureActive('archived_reports')) {
        companyId = companyId.substring(companyId.indexOf('/') + 1);
      }

      const selected = companyId === this.props.selectedItemId;
      return (
        <CompanyListRowComponent
          key={companyId}
          narrow={narrow}
          selected={selected}
          onClick={() => this.rowClicked(company)}
          ref={selected?this.selectedRowRef:null}
          company_resource_id={companyId}
          hasCombinedReport={company.has_combined_report}
          {...company}
        />
      );
    });

    if (!rows.length) {
      let message = '';
      let className = '';

      if (this.state.loaded) {
        message = 'messageEmpty';
      } else if (this.state.loading) {
        message = 'messageLoading';
      } else {
        // Technically there are two possibilities at this point: we
        // failed to load the project list (state.failed is true) or we
        // never initiated the loading (state.failed is false).  But we're
        // calling loadCompanyList() from componentDidMount(), so
        // the 2nd option is impossible.
        className = 'text-warning pointer-events-none';
        message = 'messageFailed';
      }
      rows = (
        <ListMessageRowComponent message={message} className={className} colSpan={this.colSpan()} />
      );
    }

    return (
      <InfiniteScroll
        pageStart={0}
        loadMore={this.loadMore.bind(this)}
        hasMore={this.state.loadMore}
        loader={
          <div key={0}>
            <Spinner />
          </div>
        }
      >
        <div className="company-container">
          <table
            className={classNames(
              'table',
              'table--bol',
              'table-striped',
              'table-striped--bol',
              'table-hover',
              'company-list',
              // Note that more than one of these
              // can be active at the same time:
              // - loaded means the initial load succeeded (eventually)
              // - failed means the last load failed
              // - loading means a load is in progress
              {
                loaded: this.state.loaded,
                failed: this.state.failed,
                loading: this.state.loading,
              }
            )}
          >
            <thead>
              <CompanyListHeadComponent
                sortBy={featureActive('archived_reports') ? sortBy : null}
                sortDir={featureActive('archived_reports') ? sortDir : null}
                narrow={narrow}
                onSort={featureActive('archived_reports') ? this.onSort : null}
              />
            </thead>
            <tbody className="table-clickable-rows--bol">{rows}</tbody>
          </table>
        </div>
      </InfiniteScroll>
    );
  }
}

export default withRouter(CompanyListComponent);
