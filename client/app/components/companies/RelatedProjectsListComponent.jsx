/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';
import BootstrapTable from 'react-bootstrap-table-next';
import classNames from 'classnames';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import { companyProjectsStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import { statusComparator } from '../../helpers/ProjectTree';
import StatusIconComponent from '../shared/StatusIconComponent';
import companyProjectsSelect from '../../actions/actionCreators/CompanyProjectsSelect';
import companyProjectsClear from '../../actions/actionCreators/CompanyProjectsClear';
import projectPageOpen from '../../actions/actionCreators/ProjectPageOpen';
import TableMessages from '../shared/TableMessages';
import Spinner from '../shared/Spinner';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';
/* eslint-enable max-len */

class RelatedProjectsListComponent extends React.Component {
  static get propTypes() {
    return {
      companyId: PropTypes.string,
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired,
    };
  }

  constructor(props) {
    super(props);
    this.companyProjectsStore = new StoreSubscription(
      companyProjectsStore,
      this.storeChanged.bind(this)
    );
    this.state = this.mapStoreToState(companyProjectsStore.getState());
  }

  componentDidMount() {
    this.companyProjectsStore.activate();
    if (this.props.companyId) {
      this.loadProjects(this.props.companyId);
    }
  }

  componentWillUnmount() {
    setTimeout(() => companyProjectsClear(), 0);
    this.companyProjectsStore.deactivate();
  }

  loadProjects(companyId) {
    setTimeout(() => companyProjectsSelect(companyId), 0);
  }

  mapStoreToState(storeState) {
    return {
      projects: storeState.projects.map(project => ({
        project: {
          name: project.name,
          tax_id: project.tax_id,
          internal_id: project.project_id,
          id: project.id,
        },
        status: project.status,
        start_date: project.start_date,
        end_date: project.end_date,
      })),

      loading: storeState.loading,
      loaded: storeState.loaded,
      failed: storeState.failed,
    };
  }

  storeChanged() {
    this.setState(this.mapStoreToState(companyProjectsStore.getState()));
  }

  statusClassNameFormat(fieldValue /* , row, rowIdx, colIdx */) {
    return `company-status status-cell status-${fieldValue}`;
  }

  statusFormatter(cell /* , row */) {
    return <StatusIconComponent status={cell} />;
  }

  openProject(projectId, navigate) {
    projectPageOpen(navigate, projectId);
  }

  projectFormatter(cell /* , row */) {
    return (
      <div
        onClick={() => {
          if (this.state.loaded) {
            this.openProject(cell.id, this.props.router.navigate);
          }
        }}
      >
        <span className="stv-table-cell-title">{cell.name}</span>
        <br />
        <span className="text-muted sub-title">
          <span>{cell.tax_id}</span> / <span>{cell.internal_id}</span>
        </span>
      </div>
    );
  }

  projectSortFunc(a, b, order, _dataField, rowA, rowB) {
    let result = statusComparator(rowA.status, rowB.status);
    if (result === 0) {
      result = a.name.localeCompare(b.name);
    }
    return order === 'asc' ? result : -result;
  }

  render() {
    const { formatMessage } = this.props.intl;
    const defaultSorted = [
      {
        dataField: 'project',
        order: 'asc',
      },
    ];
    const headerClasses = classNames('pointer-events-none');
    const columns = [
      {
        dataField: 'status',
        text: formatMessage(TableMessages.status),
        formatter: this.statusFormatter,
        columnClassName: this.statusClassNameFormat,
        headerClasses,
        headerAlign: 'left',
        align: 'center',
        style: { width: '55px' },
      },
      {
        dataField: 'project',
        text: formatMessage(TableMessages.project),
        sort: true,
        sortFunc: this.projectSortFunc,
        formatter: this.projectFormatter.bind(this),
        headerClasses,
      },
      {
        dataField: 'start_date',
        text: formatMessage(TableMessages.startDate),
        headerClasses,
      },
      {
        dataField: 'end_date',
        text: formatMessage(TableMessages.endDate),
        headerClasses,
      },
    ];
    if (this.state.loaded) {
      return (
        <BootstrapTable
          keyField="project.name"
          columns={columns}
          data={this.state.projects}
          striped
          hover
          bordered={false}
          defaultSorted={defaultSorted}
          defaultSortDirection="asc"
          noDataIndication={() => formatMessage(TableMessages.no_data_to_display)}
          wrapperClasses="related-projects"
          classes={classNames('table-striped--bol', 'table--bol-compact')}
          rowClasses="table-clickable-rows--bol"
        />
      );
    } else if (this.state.loading) {
      return (
        <div>
          <Spinner />
        </div>
      );
    } /* if (this.state.failed) */
    return (
      <div>
        <FormattedMessage id="relatedProjects.loadingFailed" defaultMessage="Loading failed" />
      </div>
    );
  }
}

export default injectIntl(withRouter(RelatedProjectsListComponent));
