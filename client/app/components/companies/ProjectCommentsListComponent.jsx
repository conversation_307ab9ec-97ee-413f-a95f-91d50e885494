import React, { useState, useEffect } from 'react';
import { injectIntl, defineMessages } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import StoreSubscription from '../../helpers/StoreSubscription';
import { companyViewStore } from '../../stores/Stores';
import { formatTime } from '../../helpers/Time';
import styled from 'styled-components';
import Spinner from '../shared/Spinner';
import companyProjectCommentsOpenDeleteConfirmation
  from '../../actions/actionCreators/CompanyProjectCommentsOpenDeleteConfirmation';
import ProjectCommentsDeleteModalComponent from './ProjectCommentsDeleteModalComponent';
import ProjectCommentsAddCommentComponent from './ProjectCommentsAddCommentComponent';
import companyProjectCommentsToggleEdit from 
  '../../actions/actionCreators/CompanyProjectCommentsToggleEdit';

const messages = defineMessages({
  by: {
    id: 'comments.by',
    description: 'Used for "updated by" and "deleted by"',
    defaultMessage: 'by',
  },
  edit: {
    id: 'comments.edit',
    description: 'Label for edit action',
    defaultMessage: 'edit',
  },
  emptyList: {
    id: 'comments.emptyList',
    description: 'Label for empty list',
    defaultMessage: 'There are no comments for this supplier in this project.',
  },
  delete: {
    id: 'comments.delete',
    description: 'Label for delete action',
    defaultMessage: 'delete',
  },
  deleted: {
    id: 'comments.deleted',
    description: 'Label for deleted comments',
    defaultMessage: 'deleted',
  },
  deleteFailed: {
    id: 'comments.deleteFailed',
    description: 'Label for failed delete action',
    defaultMessage: 'Failed to delete comment.',
  },
  updated: {
    id: 'comments.updated',
    description: 'Label for changed comments',
    defaultMessage: 'updated',
  },
});

const Template = styled.div`
  display: grid;
  grid-template-columns: max-content max-content max-content 1fr max-content;
`;
const Author = styled.div`
  grid-row: 1;
  grid-column: 1;
`;
const Created = styled.div`
  grid-row: 1;
  grid-column: 2;
`;
const Updated = styled.div`
  grid-row: 1;
  grid-column: 3;
`;
const EditAction = styled.a`
  grid-row: 1;
  grid-column: 5;
`;
const DeleteAction = styled.a`
  grid-row: 2;
  grid-column: 5;
`;
const Content = styled.div`
  grid-row: 2;
  grid-column: 1 / span 4;
  white-space: pre-wrap;
`;
const EditRow = styled.div`
  grid-row: 1/-1;
  grid-column: 1/-1;
`;
const ProjectCommentsListComponent = injectIntl(( props ) => {
  const { formatMessage } = props.intl;
  const [state, setState] = useState(companyViewStore.getState());

  useEffect(() => {
    const storeChanged = (storeState) => {
      setState({
        comments: storeState.comments,
        comments_loading: storeState.comments_loading,
        comments_failed: storeState.comments_failed,
        supplier_id: storeState.supplier_id,
        comment_delete_id: storeState.comment_delete_id,
        comment_delete_failed: storeState.comment_delete_failed,
      });
    };

    const storeSubscription = new StoreSubscription(companyViewStore, storeChanged);
    storeSubscription.activate();

    return () => {
      storeSubscription.deactivate();
    };
  }, []);

  let {
    comments,
    comments_loading,
    comments_failed,
    comment_delete_id,
    comment_delete_failed,
  } = state;

  function getCommentText(comment) {
    if (comment.loading) {
      return <Spinner/>
    }
    if (comment.data.deleted_timestamp) {
      return <span className='comment-deleted'>
        {[
          formatMessage(messages.deleted),
          formatTime(comment.data.deleted_timestamp, props.intl.locale, false),
          formatMessage(messages.by),
          comment.data.deleter
        ].join(' ')}
      </span>
    }
    return <span className='comment-content'>{comment.data.comment}</span>;
  }

  const tableRow = (comment) => {
    return <Template 
      className='pseudo-row'
      key={comment.data.id}
    >
      <Author className='comment-author'>{comment.data.author}</Author>
      <Created
        className='comment-info'
        title={formatTime(comment.data.created_timestamp, props.intl, false)}>
        {formatTime(comment.data.created_timestamp, props.intl)}
      </Created>
      {comment.data.updated_timestamp && <Updated
        className='comment-state'
        title={[
          formatTime(comment.data.updated_timestamp, props.intl),
          formatMessage(messages.by),
          comment.data.updater
        ].join(' ')}
      >
        {formatMessage(messages.updated)}
      </Updated>}
      {comment.data.permissions.includes('update') && !comment.loading &&
        <EditAction
          href="#"
          onClick={(e) => {
            e.preventDefault();
            companyProjectCommentsToggleEdit(comment.data.id, true);
          }}
        >
          <i className="fa fa-pencil fa-pr--bol" />
          <FormattedMessage
            id="comments.edit"
            description="Label for edit action"
            defaultMessage="edit"
          />
        </EditAction>
      }
      {comment.data.permissions.includes('delete') && !comment.loading &&
        <DeleteAction
          href="#"
          onClick={(e) => {
            e.preventDefault();
            companyProjectCommentsOpenDeleteConfirmation(comment.data.id);
          }}
        >
          <i className="fa fa-trash-o fa-pr--bol" />
          <FormattedMessage
            id="comments.delete"
            description="Label for delete action"
            defaultMessage="delete"
          />
        </DeleteAction>
      }
      <Content className='d-flex align-items-center'>
        {!comment.data.is_read && !comment.loading
          && <i className="fa fa-circle new-indicator" />}
        {getCommentText(comment)}
      </Content> 
    </Template>
  };

  const editRow = (comment) => {
    return <Template 
      className='pseudo-row'
      key={comment.data.id}
    >
      <EditRow>
        <ProjectCommentsAddCommentComponent 
          editCommentId={comment.data.id}
          editCommentText={comment.data.comment}
        />
      </EditRow>
    </Template>
  }

  let content;
  if (comments_loading) {
    content = <Spinner/>
  } else if (comments_failed) {
    content = <FormattedMessage
      id="comments.loadingFailed"
      description="Failed to load comments"
      defaultMessage="Failed to load comments"
    />
  } else if (!comments?.length) {
    content = <FormattedMessage
      id="comments.emptyList"
      description="Message shown when comments table is empty"
      defaultMessage="There are no comments for this supplier in this project."
    />
  } else {
    content = (
      <div className='pseudo-table table-striped'>
        {comments.map(comment => comment.editing ? editRow(comment) : tableRow(comment) )}
      </div>
    )
  }

  return <>
    {comment_delete_failed &&
      <span className='d-flex justify-content-center alert-danger mb-4'>
        {formatMessage(messages.deleteFailed)}
      </span>
    }
    {content}
    {comment_delete_id && <ProjectCommentsDeleteModalComponent/>}
  </>
});

export default ProjectCommentsListComponent;
