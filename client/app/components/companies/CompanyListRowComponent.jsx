import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { useIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { getFilename } from '../../helpers/Reports';
import { featureActive } from '../../helpers/FeatureFlags';
import ReportIconComponent from '../shared/ReportIconComponent';
import StatusIconComponent from '../shared/StatusIconComponent';
import StatusMessages from '../shared/StatusMessages';

const CompanyListRowComponent = React.forwardRef(({
  selected,
  onClick,
  company_resource_id,
  company_id,
  vat_number,
  company_status,
  country,
  name,
  narrow = false,
  project_count,
  report_available,
  hasCombinedReport = false,
}, ref) => {
  const intl = useIntl();
  return (
    <tr className={classNames({ 'table-active': selected })} ref={ref}>
      <td onClick={onClick}>
        <div>
          {/*
          We could use `d-flex` here if it wasn't for an IE bug
          see commit 51f610d9ee7b9b16fa7612c7eeb8f73519d68236
        */}
          <div className="left title-block">
            <span>
              {name || (
                <FormattedMessage
                  id="companies.nameunavailable"
                  description="Company name when no name is available"
                  defaultMessage="Name not available"
                />
              )}
            </span>
            <br />
            <span
              className={classNames('sub-title', {
                'text-muted': !featureActive('archived_reports'),
              })}
            >
              {company_id}
              {vat_number && ` / ${vat_number}`}
            </span>
          </div>
          <div className={classNames('right', { hidden: !narrow })}>
            <StatusIconComponent status={company_status} />
          </div>
        </div>
      </td>
      <td
        className={classNames('company-status', 'status-cell', `status-${company_status}`, {
          hidden: narrow,
        })}
        onClick={onClick}
      >
        <span className="text-nowrap p-0">
          <StatusIconComponent status={company_status} />
          <span>{intl.formatMessage(StatusMessages[company_status])}</span>
        </span>
      </td>
      <td className={classNames({ hidden: narrow })}>
        <ReportIconComponent
          company_resource_id={
            featureActive('archived_reports')
              ? `${country}/${company_id}`
              : company_resource_id
          }
          report_available={report_available}
          filename={getFilename(country, company_id, name)}
          hasCombinedReport={hasCombinedReport}
        />
      </td>
      <td className={classNames({ hidden: narrow })} onClick={onClick}>
        {country}
      </td>
      {featureActive('projects') && (
        <td className={classNames({ hidden: narrow })} onClick={onClick}>
          {project_count}
        </td>
      )}
    </tr>);
});

CompanyListRowComponent.propTypes = {
  selected: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  company_resource_id: PropTypes.string.isRequired,
  company_id: PropTypes.string,
  vat_number: PropTypes.string,
  company_status: PropTypes.string,
  country: PropTypes.string,
  name: PropTypes.string,
  narrow: PropTypes.bool,
  project_count: PropTypes.number,
  report_available: PropTypes.bool,
  hasCombinedReport: PropTypes.bool,
};

export default CompanyListRowComponent;

