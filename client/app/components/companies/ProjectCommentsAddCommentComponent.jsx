import React, { useRef, useState, useEffect } from 'react';
import { FormattedMessage, injectIntl, defineMessages } from 'react-intl';
import StoreSubscription from '../../helpers/StoreSubscription';
import { companyViewStore } from '../../stores/Stores';
import companyProjectCommentsAdd from '../../actions/actionCreators/CompanyProjectCommentsAdd';
import companyProjectCommentsUpdate from
  '../../actions/actionCreators/CompanyProjectCommentsUpdate';
import companyProjectCommentsToggleEdit from 
  '../../actions/actionCreators/CompanyProjectCommentsToggleEdit';
import LocalizedText from '../i18n/LocalizedText';
import GrowingTextareaComponent from '../shared/GrowingTextareaComponent';
import PropTypes from 'prop-types';

const messages = defineMessages({
  save: {
    id: 'comments.save',
    description: 'Label for save action',
    defaultMessage: 'save',
  },
  cancel: {
    id: 'comments.cancel',
    description: 'Label for cancel action',
    defaultMessage: 'cancel',
  },
  placeholder: {
    id: 'comments.addPlaceholder',
    description: 'Label for placeholder',
    defaultMessage: 'Write a comment about the supplier in the project',
  },
  helpText: {
    id: 'comments.addHelpText',
    description: 'Label for help text',
    defaultMessage: 'Comments are only visible to the main contractor, the project\'s client and' +
    ' supervisors. Other suppliers in the project cannot see or write comments.',
  }
});


const ProjectCommentsAddCommentComponent = injectIntl(( props ) => {
  const { formatMessage} = props.intl;
  const [state, setState] = useState(companyViewStore.getState());
  const [hasFocus, setFocusState] = useState(false);
  const [content, setContent] = useState(props.editCommentText ?? '');
  const textareaRef = useRef(null)

  useEffect(() => {
    const storeChanged = (storeState) => {
      setState({
        supplier_id: storeState.supplier_id,
        comment_add_in_progress: storeState.comment_add_in_progress,
        comment_add_errors: storeState.comment_add_errors,
        comment_map: storeState.comment_map,
      });
    };

    const storeSubscription = new StoreSubscription(companyViewStore, storeChanged);
    storeSubscription.activate();

    return () => {
      storeSubscription.deactivate();
    };
  }, []);

  const clearInput = (e) => {
    e.preventDefault();
    if (props.editCommentId) {
      companyProjectCommentsToggleEdit(props.editCommentId, false);
    } else {
      textareaRef.current.value = '';
      setContent('');
    }
  }
  const submitComment = async (e) => {
    e.preventDefault();
    const content = textareaRef.current.value.trim();
    const succeeded = props.editCommentId ? 
      await companyProjectCommentsUpdate(
        state.supplier_id,
        props.editCommentId,
        content
      )
      : await companyProjectCommentsAdd(
        state.supplier_id,
        content
      );
    if (succeeded) {
      clearInput(e);
    }
  }
  const handleInput = () => {
    setContent(textareaRef.current.value);
  };
  const handleFocus = () => {
    setFocusState(true);
  };
  const handleBlur = () => {
    setFocusState(false);
  };

  const hasContent = content.trim().length > 0;
  const hasNewContent = content.trim() !== props.editCommentText;
  const errors = props.editCommentId
    ? state.comment_map[props.editCommentId].errors
    : state.comment_add_errors;
  const saveInProgress = props.editCommentId
    ? state.comment_map[props.editCommentId].loading
    : state.comment_add_in_progress

  return <div className={
    'form-group d-flex-column' +
    (errors?.length ? ' has-danger' : '')
  }>
    <GrowingTextareaComponent
      name="new-comment-field"
      className={'form-control mb-2'}
      ref={textareaRef}
      placeholder={hasFocus || props.editCommentText ? '' : formatMessage(messages.placeholder)}
      onInput={handleInput}
      onFocus={handleFocus}
      onBlur={handleBlur}
      disabled={saveInProgress}
      value={content}
    />
    {errors && errors.length > 0 && 
      // Errors are either shaped like messages (coming from the client),
      // or translations (coming from frontback)
      errors.map((error, idx) => {
        if (error.id) {
          return <FormattedMessage {...error} key={idx} />;
        } else {
          return <LocalizedText translations={error} key={idx} />;
        }
      })
    }
    <span className="field-help d-block mt-4">
      <FormattedMessage {...messages.helpText} />
    </span>
    {(hasFocus || hasContent) &&
    <div className="mt-4 col-md-12 d-flex justify-content-center">
      <button
        className="btn btn-sm btn-primary mr-3"
        role="button"
        disabled={!hasContent || !hasNewContent || saveInProgress}
        onClick={submitComment}
      >
        <FormattedMessage {...messages.save} />
      </button>
      <a
        href="#"
        className="btn btn-sm btn-link"
        onClick={clearInput}
        role="button"
      >
        <FormattedMessage {...messages.cancel} />
      </a>
    </div>}
  </div>
});

ProjectCommentsAddCommentComponent.propTypes = {
  editCommentId: PropTypes.number,
  editCommentText: PropTypes.string,
};

export default ProjectCommentsAddCommentComponent;
