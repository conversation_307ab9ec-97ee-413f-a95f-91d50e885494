import React from 'react';
import PropTypes from 'prop-types';
import { defineMessages, injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { getGovOrgId } from '../../helpers/Company';
import { featureActive } from '../../helpers/FeatureFlags';
import { getCountryCodeAlpha3 } from '../../helpers/Subsidiaries';
import DetailsListItem from '../shared/DetailsListItem';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  terminatedTooltip: {
    id: 'companyDetails.terminated.tooltip',
    description: 'Search company details terminated tooltip',
    defaultMessage: 'Information source: Asiakastieto',
  },
});

class CompanyDetailsComponent extends React.Component {
  static get propTypes() {
    return {
      className: PropTypes.string,
      showVat: PropTypes.bool,
      company: PropTypes.object.isRequired,
      intl: intlPropType.isRequired,
    };
  }

  static get defaultProps() {
    return {
      showVat: false,
      intl: intlPropType.isRequired,
    };
  }

  render() {
    const { showVat, company } = this.props;
    const { formatMessage } = this.props.intl;
    const { companyId, country, govOrgIds } = company;
    const finnishGovOrgId = getGovOrgId('FIN', govOrgIds, true);
    const govOrgId = getGovOrgId(country, govOrgIds, false, 'registration_number');
    const vatNumber = getGovOrgId(country, govOrgIds, false, 'vat_number');
    const showTerminated =
      featureActive('archived_reports') && company.terminated && !company.isReliablePartner;

    return (
      <div className={this.props.className}>
        <div className="card noborder">
          <div className="card-title text-uppercase pointer-events-none">
            <FormattedMessage
              id="companyDetails.detailsHeading"
              description="Section subheading in the company popup"
              defaultMessage="Company information"
            />
          </div>
          <div className="details-list--list">
            <DetailsListItem classNames="company-name">
              <FormattedMessage
                id="companyDetails.companyNameLabel"
                description="Label for company name"
                defaultMessage="Company name: "
              />
              {company.name}
            </DetailsListItem>
            <DetailsListItem classNames="business-id">
              <FormattedMessage
                id="companyDetails.businessIdLabel"
                description="Label for company business ID"
                defaultMessage="Business ID: "
              />
              {govOrgId || companyId.split('/')[1]}
            </DetailsListItem>
            {showVat && vatNumber && (
              <DetailsListItem classNames="vat-number">
                <FormattedMessage
                  id="companyDetails.vatNumberLabel"
                  description="Label for VAT number"
                  defaultMessage="VAT: "
                />
                {vatNumber}
              </DetailsListItem>
            )}
            <DetailsListItem classNames="country">
              <FormattedMessage
                id="companyDetails.countryLabel"
                description="Label for company's country"
                defaultMessage="Country: "
              />
              {country || getCountryCodeAlpha3(companyId.split('/')[0])}
            </DetailsListItem>
            {showTerminated && (
              <DetailsListItem classNames="company-status">
                <FormattedMessage
                  id="companyDetails.companyStatusLabel"
                  description="Label for company's status"
                  defaultMessage="Status: "
                />
                <span
                  className="badge badge-danger pointer-events-auto"
                  data-tip={formatMessage(messages.terminatedTooltip)}
                >
                  <FormattedMessage
                    id="companyDetails.status.terminated"
                    description="Company status terminated"
                    defaultMessage="Terminated"
                  />
                </span>
              </DetailsListItem>
            )}
          </div>
        </div>
        {country !== 'FIN' && finnishGovOrgId && (
          <div className="card noborder pointer-events-none">
            <div className="card-title text-uppercase">
              <FormattedMessage
                id="companyDetails.finnishDetailsHeading"
                description="Finnish details subheading in the company popup"
                defaultMessage="Finnish company information"
              />
            </div>
            <div className="details-list--list">
              <DetailsListItem classNames="business-id">
                <FormattedMessage
                  id="companyDetails.businessIdLabel"
                  description="Label for company business ID"
                  defaultMessage="Business ID: "
                />
                {finnishGovOrgId}
              </DetailsListItem>
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default injectIntl(CompanyDetailsComponent);
