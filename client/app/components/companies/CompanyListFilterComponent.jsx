import React from 'react';
import { injectIntl, defineMessages} from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import filterCompanyList from '../../actions/actionCreators/CompanyListFilter';
import detailsViewClose from '../../actions/actionCreators/DetailsViewClose';
import StatusMessages from '../shared/StatusMessages';
import ListFilterOptionsComponent from '../shared/ListFilterOptionsComponent';
import { featureActive } from '../../helpers/FeatureFlags';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';

const messages = defineMessages({
  compSearchPlaceholder: {
    id: 'companies.filter.compSearchPlaceholder',
    description: 'Company place holder for search filter field',
    defaultMessage: 'Company name or number',
  },
});

class CompanyListFilterComponent extends React.Component {
  static get propTypes() {
    return {
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired,
    }
  }

  _getStatusValue() {
    return this.statusRef ? this.statusRef.options[this.statusRef.selectedIndex].value : '';
  }

  _onFilter(event) {
    event.preventDefault();
    filterCompanyList({
      search: this.searchRef ? this.searchRef.value : '',
      status: this._getStatusValue(),
    });
    detailsViewClose();
    this.props.router.navigate('/companies');
    return false;
  }

  _onStatusChange() {
    filterCompanyList({
      status: this._getStatusValue(),
    });
  }

  _onClear(event) {
    event.preventDefault();
    document.getElementById('companies_filter_form').reset();
    this._onFilter(event);
  }

  renderOptions(options) {
    return options.map(option => (
      <option key={option.value} value={option.value}>
        {option.title}
      </option>
    ));
  }

  render() {
    const { formatMessage } = this.props.intl;

    const statusChoices = [
      { value: '', title: formatMessage(StatusMessages.any) },
      { value: 'ok', title: formatMessage(StatusMessages.ok) },
      { value: 'not:ok', title: formatMessage(StatusMessages.notOk) },
      { value: 'stop', title: formatMessage(StatusMessages.stop) },
      {
        value: 'investigate',
        title: formatMessage(StatusMessages.investigate),
      },
      { value: 'incomplete', title: formatMessage(StatusMessages.incomplete) },
      { value: 'attention', title: formatMessage(StatusMessages.attention) },
    ];

    return (
      <div className="card noborder list-filter-with-options vertical-compact">
        <div className="card card-default mb-0 list-filter">
          <div className="card-block filter-block">
            <form id="companies_filter_form" onSubmit={this._onFilter.bind(this)}>
              <div className="row">
                <div className="col-md-6">
                  <div>
                    <label className="form-control-label form-control-label-sm">
                      <FormattedMessage
                        id="projects.filter.searchLabel"
                        description="Label for search input"
                        defaultMessage="Search"
                      />
                    </label>
                    <div className="input-group">
                      <input
                        name="compsearch"
                        className="form-control"
                        placeholder={formatMessage(messages.compSearchPlaceholder)}
                        type="search"
                        ref={search => {
                          this.searchRef = search;
                        }}
                      />
                      <button
                        id="companies_filter_button"
                        role="button"
                        type="submit"
                        className="input-group-addon text-primary bg-white"
                      >
                        <span className="fa fa-search" aria-hidden="true" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="col-md-3" />
                <div className="col-md-3">
                  {!featureActive('archived_reports') && (
                    <div>
                      <label
                        /* prettier max-len break */
                        className="form-control-label form-control-label-sm"
                      >
                        <FormattedMessage
                          id="projects.filter.statusLabel"
                          description="Label for status filter"
                          defaultMessage="Status"
                        />
                      </label>
                      <select
                        name="company_status"
                        className="form-control"
                        onChange={this._onStatusChange.bind(this)}
                        ref={status => {
                          this.statusRef = status;
                        }}
                        role="button"
                      >
                        {this.renderOptions(statusChoices, '')}
                      </select>
                    </div>
                  )}
                </div>
              </div>

              {featureActive('archived_reports') && (
                <ListFilterOptionsComponent callback={this._onClear.bind(this)} />
              )}
            </form>
          </div>
        </div>

        {!featureActive('archived_reports') && (
          <ListFilterOptionsComponent callback={this._onClear.bind(this)} />
        )}
      </div>
    );
  }
}

export default injectIntl(withRouter(CompanyListFilterComponent));
