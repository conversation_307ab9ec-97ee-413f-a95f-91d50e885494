import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl, defineMessages } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { formatTime } from '../../helpers/Time';
import styled from 'styled-components';
import Spinner from '../shared/Spinner';
import companyProjectCommentsOpenDeleteConfirmation
  from '../../actions/actionCreators/CompanyProjectCommentsOpenDeleteConfirmation';
import companyProjectCommentsToggleEdit from 
  '../../actions/actionCreators/CompanyProjectCommentsToggleEdit';
import ProjectCommentsAddCommentComponent from './ProjectCommentsAddCommentComponent';

const messages = defineMessages({
  by: {
    id: 'comments.by',
    description: 'Used for "updated by" and "deleted by"',
    defaultMessage: 'by',
  },
  edit: {
    id: 'comments.edit',
    description: 'Label for edit action',
    defaultMessage: 'edit',
  },
  delete: {
    id: 'comments.delete',
    description: 'Label for delete action',
    defaultMessage: 'delete',
  },
  deleted: {
    id: 'comments.deleted',
    description: 'Label for deleted comments',
    defaultMessage: 'deleted',
  },
  updated: {
    id: 'comments.updated',
    description: 'Label for changed comments',
    defaultMessage: 'updated',
  },
});

const Template = styled.div`
  display: grid;
  grid-template-columns: ${props => props.readOnly ?
    '1fr' : 'max-content max-content max-content 1fr max-content'};
  ${props => props.readOnly ? 'border-bottom: none; padding: 10px 0;' : ''}
`;
const Author = styled.div`
  grid-row: 1;
  grid-column: 1;
  ${props => props.readOnly ? 'color: #666; font-size: 0.9em;' : ''}
`;
const Created = styled.div`
  grid-row: 1;
  grid-column: 2;
  ${props => props.readOnly ? 'color: #666; font-size: 0.9em;' : ''}
`;
const Updated = styled.div`
  grid-row: 1;
  grid-column: 3;
  ${props => props.readOnly ? 'color: #666; font-size: 0.9em;' : ''}
`;
const MetaInfo = styled.div`
  grid-row: 1;
  grid-column: 1;
  color: #999;
  font-size: 0.85em;
  font-weight: normal;
  margin-bottom: 5px;
`;
const EditAction = styled.a`
  grid-row: 1;
  grid-column: 5;
`;
const DeleteAction = styled.a`
  grid-row: 2;
  grid-column: 5;
`;
const Content = styled.div`
  grid-row: ${props => props.readOnly ? '2' : '2'};
  grid-column: ${props => props.readOnly ? '1' : '1 / span 4'};
  white-space: pre-wrap;
`;
const EditRow = styled.div`
  grid-row: 1/-1;
  grid-column: 1/-1;
`;

const ProjectCommentsDisplayComponent = injectIntl((props) => {
  const { formatMessage } = props.intl;
  const { comments, readOnly = false } = props;

  function getCommentText(comment) {
    // Handle different comment data structures
    const commentData = comment.data || comment;
    const isLoading = comment.loading || false;
    
    if (isLoading) {
      return <Spinner/>
    }
    if (commentData.deleted_timestamp) {
      return <span className='comment-deleted'>
        {[
          formatMessage(messages.deleted),
          formatTime(commentData.deleted_timestamp, props.intl.locale, false),
          formatMessage(messages.by),
          commentData.deleter
        ].join(' ')}
      </span>
    }
    return <span className='comment-content'>{commentData.comment}</span>;
  }

  const tableRow = (comment) => {
    // Handle different comment data structures
    const commentData = comment.data || comment;
    const isLoading = comment.loading || false;

    if (readOnly) {
      // Print-ready layout: date/time + company name, then comment content
      return <Template
        className='pseudo-row'
        key={commentData.id}
        readOnly={readOnly}
      >
        <MetaInfo>
          {formatTime(commentData.created_timestamp, props.intl, false)}
          {/* Debug: let's see what fields are available */}
          {console.log('Comment data fields:', Object.keys(commentData))}
          {(commentData.company_name || commentData.org_name || commentData.organization_name) &&
            ` • ${commentData.company_name || commentData.org_name || commentData.organization_name}`}
          {commentData.updated_timestamp && (
            <span>
              {' • '}
              <FormattedMessage
                id="comments.updated"
                description="Label for changed comments"
                defaultMessage="updated"
              />
              {' '}
              {formatTime(commentData.updated_timestamp, props.intl, false)}
            </span>
          )}
        </MetaInfo>
        <Content className='comment-content' readOnly={readOnly}>
          {getCommentText(comment)}
        </Content>
      </Template>
    }

    // Interactive layout (original)
    return <Template
      className='pseudo-row'
      key={commentData.id}
      readOnly={readOnly}
    >
      <Author className='comment-author' readOnly={readOnly}>{commentData.author}</Author>
      <Created
        className='comment-info'
        readOnly={readOnly}
        title={formatTime(commentData.created_timestamp, props.intl, false)}>
        {formatTime(commentData.created_timestamp, props.intl)}
      </Created>
      {commentData.updated_timestamp && <Updated
        className='comment-state'
        readOnly={readOnly}
        title={[
          formatTime(commentData.updated_timestamp, props.intl),
          formatMessage(messages.by),
          commentData.updater
        ].join(' ')}
      >
        {formatMessage(messages.updated)}
      </Updated>}
      {!readOnly && commentData.permissions &&
      commentData.permissions.includes('update') && !isLoading &&
        <EditAction
          href="#"
          onClick={(e) => {
            e.preventDefault();
            companyProjectCommentsToggleEdit(commentData.id, true);
          }}
        >
          <i className="fa fa-pencil fa-pr--bol" />
          <FormattedMessage
            id="comments.edit"
            description="Label for edit action"
            defaultMessage="edit"
          />
        </EditAction>
      }
      {!readOnly && commentData.permissions &&
       commentData.permissions.includes('delete') && !isLoading &&
        <DeleteAction
          href="#"
          onClick={(e) => {
            e.preventDefault();
            companyProjectCommentsOpenDeleteConfirmation(commentData.id);
          }}
        >
          <i className="fa fa-trash-o fa-pr--bol" />
          <FormattedMessage
            id="comments.delete"
            description="Label for delete action"
            defaultMessage="delete"
          />
        </DeleteAction>
      }
      <Content className='d-flex align-items-center' readOnly={readOnly}>
        {!readOnly && !commentData.is_read && !isLoading
          && <i className="fa fa-circle new-indicator" />}
        {getCommentText(comment)}
      </Content>
    </Template>
  };

  const editRow = (comment) => {
    const commentData = comment.data || comment;
    
    return <Template 
      className='pseudo-row'
      key={commentData.id}
      readOnly={readOnly}
    >
      <EditRow>
        <ProjectCommentsAddCommentComponent 
          editCommentId={commentData.id}
          editCommentText={commentData.comment}
        />
      </EditRow>
    </Template>
  }

  if (!comments || comments.length === 0) {
    return (
      <FormattedMessage
        id="comments.emptyList"
        description="Message shown when comments table is empty"
        defaultMessage="There are no comments for this supplier in this project."
      />
    );
  }

  return (
    <div className={readOnly ? 'comments-print-view' : 'pseudo-table table-striped'}>
      {comments.map(comment => {
        // Only show edit row for interactive mode and when comment is being edited
        if (!readOnly && comment.editing) {
          return editRow(comment);
        }
        return tableRow(comment);
      })}
    </div>
  );
});

ProjectCommentsDisplayComponent.propTypes = {
  comments: PropTypes.array.isRequired,
  readOnly: PropTypes.bool,
  intl: PropTypes.object.isRequired,
};

export default ProjectCommentsDisplayComponent;
