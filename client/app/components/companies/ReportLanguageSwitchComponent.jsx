import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import NodeMenuComponent from '../projects/NodeMenuComponent';
import NodeMenuItem from '../projects/NodeMenuItem';
import { stopPropagation } from '../../helpers/EventHandlers';
import { getReportURL } from '../../helpers/Reports';

class ReportLanguageSwitchComponent extends React.Component {
  static get defaultProps() {
    return {
      title: null,
      selectedLanguageCallback: null,
    };
  }

  static get propTypes() {
    return {
      id: PropTypes.string,
      title: PropTypes.string,
      selectedLanguageCallback: PropTypes.func,
      companyId: PropTypes.string,
      filename: PropTypes.string,
    };
  }

  onLanguageSelect(lang) {
    const url = getReportURL(this.props.companyId, this.props.filename, lang);
    const win = window.open(url, '_blank');
    if (win) {
      win.focus();
    }
    if (this.props.selectedLanguageCallback) {
      this.props.selectedLanguageCallback(lang);
    }
  }

  renderMenu() {
    const selectLanguage = lang => e => {
      stopPropagation(e);
      return this.onLanguageSelect(lang);
    };

    return (
      <NodeMenuComponent nodeTitle={this.props.title}>
        <NodeMenuItem visible className="dropdown-in-finnish" onClick={selectLanguage('fi')}>
          <FormattedMessage
            id="companies.reportLangSw.inFinish"
            description="View report in FI"
            defaultMessage="In Finnish"
          />
        </NodeMenuItem>
        <NodeMenuItem visible className="dropdown-in-swedish" onClick={selectLanguage('sv')}>
          <FormattedMessage
            id="companies.reportLangSw.inSwedish"
            description="View report in SV"
            defaultMessage="In Swedish"
          />
        </NodeMenuItem>
        <NodeMenuItem visible className="dropdown-in-english" onClick={selectLanguage('en')}>
          <FormattedMessage
            id="companies.reportLangSw.inEnglish"
            description="View report in EN"
            defaultMessage="In English"
          />
        </NodeMenuItem>
      </NodeMenuComponent>
    );
  }

  render() {
    return (
      <span id={this.props.id} className="btn btn-primary p-0">
        {this.renderMenu()}
      </span>
    );
  }
}

export default injectIntl(ReportLanguageSwitchComponent);
