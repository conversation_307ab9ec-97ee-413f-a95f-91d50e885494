/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import { defineMessages, injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import BootstrapTable from 'react-bootstrap-table-next';
import Datetime from 'react-datetime';
import moment from 'moment';
import classNames from 'classnames';
import ReactTooltip from 'react-tooltip';

import DialogTypes from '../../actions/DialogTypes';
import Dialog from '../shared/Dialog';
import Spinner from '../shared/Spinner';
import LoadingMessages from '../shared/LoadingMessages';
import { buildApiURL } from '../../helpers/ApiClient';
import { getFilename } from '../../helpers/Reports';
import { getISOCountry } from '../../helpers/Countries';
import { authStore, companyReportsStore, subscriptionStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import { statusComparator } from '../../helpers/ProjectTree';
import StatusIconComponent from '../shared/StatusIconComponent';
import ListFilterOptionsComponent from '../shared/ListFilterOptionsComponent';
import companyArchivedReportDelete from '../../actions/actionCreators/CompanyArchivedReportDelete';
import companyArchivedReportDeleteAll from '../../actions/actionCreators/CompanyArchivedReportDeleteAll';
import { Link } from 'react-router-dom';
import { intlPropType } from '../i18n/IntlPropTypes';

/* eslint-enable max-len */

const messages = defineMessages({
  disabledReportTooltip: {
    id: 'archivedReports.disabledReportTooltip',
    description: 'Tooltip when an archived report is disabled',
    defaultMessage:
      'This report is unavailable because it was fetched before ' +
      'your current subscription began',
  },
  status: {
    id: 'archivedReports.statusLabel',
    description: 'Label for Status column in archived reports',
    defaultMessage: 'Status',
  },
  archiveId: {
    id: 'archivedReports.archiveIdLabel',
    description: 'Label for Archive number column in archived reports',
    defaultMessage: 'Archive number',
  },
  accessTime: {
    id: 'archivedReports.accessTimeLabel',
    description: 'Label for Access time column in archived reports',
    defaultMessage: 'Downloaded',
  },
  report: {
    id: 'archivedReports.reportIconLabel',
    description: 'Label for report icon column in archived reports',
    defaultMessage: 'Report',
  },
  delete: {
    id: 'archivedReports.deleteReportLabel',
    description: 'Label for delete column in archived reports',
    defaultMessage: 'Delete',
  },
  hidden: {
    id: 'archivedReports.statusHidden',
    description: 'Shown when a status is hidden',
    defaultMessage: 'Hidden',
  },
});

const ConfirmRemoveContent = props => (
  <div>
    <div>
      <p>
        {props.isRemoveAll ? (
          <FormattedMessage
            id="archivedReports.confirmDeleteAll"
            description="Confirmation message when deleting all archived reports"
            defaultMessage="Are you sure you want to delete ALL archived reports for this company?"
          />
        ) : (
          <FormattedMessage
            id="archivedReports.confirmDelete"
            description="Confirmation message when deleting an archived report"
            defaultMessage="Are you sure you want to delete this archived report?"
          />
        )}
      </p>
    </div>

    <div>
      <button id="confirm_remove" className="btn btn-sm btn-primary" onClick={props.handleConfirm}>
        <FormattedMessage
          id="archivedReports.removeDialog.removeButton"
          description="Remove button for archived reports remove dialog"
          defaultMessage="Remove"
        />
      </button>
      <button
        id="cancel_remove"
        className="btn btn-sm btn-primary right"
        onClick={props.handleCancel}
      >
        <FormattedMessage
          id="archivedReports.removeDialog.cancelButton"
          description="Cancel button for archived reports remove dialog"
          defaultMessage="Cancel"
        />
      </button>
    </div>
  </div>
);

ConfirmRemoveContent.propTypes = {
  isRemoveAll: PropTypes.bool,
  handleConfirm: PropTypes.func,
  handleCancel: PropTypes.func,
};

class ArchivedReportsListComponent extends React.Component {
  static get propTypes() {
    return {
      companyId: PropTypes.string,
      country: PropTypes.string,
      govOrgId: PropTypes.string,
      name: PropTypes.string,
      openedReports: PropTypes.array.isRequired,
      intl: intlPropType.isRequired,
    };
  }

  constructor(props) {
    super(props);

    this.targetRef = null;
    this.targetRefs = {};

    this.subscriptionStore = new StoreSubscription(
      subscriptionStore,
      this.subscriptionStoreChanged.bind(this)
    );
    this.companyReportsStore = new StoreSubscription(
      companyReportsStore,
      this.companyReportsStoreChanged.bind(this)
    );
    this.authStore = new StoreSubscription(authStore, this.authStoreChanged.bind(this));

    const subscriptionState = subscriptionStore.getState();
    const companyReportState = companyReportsStore.getState();
    const authState = authStore.getState();

    this.state = {
      subscription: subscriptionState.subscription,

      reports: companyReportState.reports.map(report => ({
        access_id: report.access_id,
        access_time: report.access_time,
        archive_id: report.archive_id,
        status: report.status,
      })),

      teaserTotal: companyReportState.teaserTotal,

      loading: companyReportState.loading,
      loaded: companyReportState.loaded,
      failed: companyReportState.failed,

      deleteLoading: companyReportState.deleteLoading,
      deleteFailed: companyReportState.deleteFailed,

      profile: authState.profile,

      startDate: '',
      endDate: '',
    };
  }

  componentDidMount() {
    this.subscriptionStore.activate();
    this.companyReportsStore.activate();
  }

  componentWillUnmount() {
    this.subscriptionStore.deactivate();
    this.companyReportsStore.deactivate();
  }

  onStartDateChange = date => {
    this.setState({ startDate: date });
  };

  onEndDateChange = date => {
    if (this.isValidDate(date)) {
      this.setState({ endDate: date.endOf('day') });
    } else {
      this.setState({ endDate: date });
    }
  };

  onRowClick = (_e, row) => {
    if (!this.isReadableReport(row)) return;

    const country = getISOCountry(this.props.country).alpha3;
    const filename = getFilename(country, this.props.govOrgId, this.props.name, row.access_time);

    window.open(
      buildApiURL(
        `/company/${this.props.companyId}/archived-reports/${row.archive_id}/${filename}`,
        true
      ),
      '_blank'
    );
  };

  onClear = () => {
    this.setState({ startDate: '', endDate: '' });
  };

  onDelete = (e, accessId) => {
    e.preventDefault();
    e.stopPropagation();

    const country = getISOCountry(this.props.country).alpha2;
    const { govOrgId } = this.props;

    companyArchivedReportDelete(this.targetRefs[accessId], country, govOrgId, accessId);
  };

  onDeleteAll = e => {
    e.preventDefault();

    const country = getISOCountry(this.props.country).alpha2;
    const { govOrgId } = this.props;

    const archiveIdPrefix = `${country}${govOrgId.replace('-', '')}`;
    companyArchivedReportDeleteAll(this.targetRef, country, govOrgId, archiveIdPrefix);
  };

  isValidDate(date) {
    return date instanceof moment && date.isValid();
  }

  subscriptionStoreChanged(storeState) {
    this.setState({
      subscription: storeState.subscription,
    });
  }

  companyReportsStoreChanged(storeState) {
    this.setState({
      reports: storeState.reports.map(report => ({
        access_id: report.access_id,
        access_time: report.access_time,
        archive_id: report.archive_id,
        status: report.status,
      })),

      teaserTotal: storeState.teaserTotal,
      teaserCurrent: storeState.teaserCurrent,

      loading: storeState.loading,
      loaded: storeState.loaded,
      failed: storeState.failed,

      deleteLoading: storeState.deleteLoading,
      deleteFailed: storeState.deleteFailed,
      deleteAllLoading: storeState.deleteAllLoading,
      deleteAllFailed: storeState.deleteAllFailed,

      startDate: null,
      endDate: null,
    });
  }

  authStoreChanged(storeState) {
    this.setState({
      profile: storeState.profile,
    });
  }

  statusClassNameFormat = (fieldValue /* row, rowIdx, colIdx */) => {
    const { subscription } = this.state;
    return classNames('company-status', 'status-cell', {
      [`status-${fieldValue}`]: subscription.active,
    });
  };

  statusFormatter = (cell, row) => {
    const { formatMessage } = this.props.intl;
    if (!this.isReadableReport(row)) {
      return <span>{formatMessage(messages.hidden)}</span>;
    }
    if (!cell) return null;
    return <StatusIconComponent status={cell} />;
  };

  reportStatusSortFunc(a, b, order /* , sortField, extraData */) {
    const result = statusComparator(a.status, b.status);
    return order === 'asc' ? result : -result;
  }

  accessTimeFormatter(cell /* row */) {
    return moment(cell).format('L');
  }

  deleteFormatter = (_cell, row) =>
    this.state.deleteLoading[row.access_id] ? (
      <div className="table-cell-spinner">
        <Spinner />
      </div>
    ) : (
      <a
        ref={r => {
          this.targetRefs[row.access_id] = r;
        }}
        href="#"
        className="table-cell-link"
        onClick={e => this.onDelete(e, row.access_id)}
      >
        <span className="fa fa-trash-o" />
      </a>
    );

  rowClassNameFormat = row =>
    this.isReadableReport(row) ? 'table-clickable-rows--bol' : 'table-row-disabled--bol';

  rowAttrFunc = (_cell, row) => {
    const { formatMessage } = this.props.intl;
    /* eslint-disable indent */
    return this.isReadableReport(row)
      ? {}
      : {
          'data-tip': formatMessage(messages.disabledReportTooltip),
          'data-for': 'archived-reports',
        };
    /* eslint-enable indent */
  };

  isReadableReport(report) {
    const accessTime = moment(report.access_time);

    const { subscription } = this.state;
    if (!subscription.active) return false;

    const subscriptionTime = moment(subscription.timestamp);

    return accessTime >= subscriptionTime;
  }

  datetimeWorkaround(value) {
    // workaround for https://github.com/arqex/react-datetime/issues/760: instead of
    // <Datetime value={foo} /> do
    // <Datetime value={foo} inputProps={{...this.datetimeWorkarond(foo)}} />
    // and you can reset the value to blank again.
    if (value === '') return { value: '' };
    return {};
  }

  render() {
    const { subscription } = this.state;
    const { locale, formatMessage } = this.props.intl;

    const defaultSorted = [
      {
        dataField: 'access_time',
        order: 'desc',
      },
    ];
    const rowEvents = {
      onClick: this.onRowClick,
    };
    const headerClasses = classNames('pointer-events-none');
    const columns = [
      {
        dataField: 'status',
        text: formatMessage(messages.status),
        formatter: this.statusFormatter,
        formatExtraData: this.props.intl,
        sort: true,
        sortFunc: this.reportStatusSortFunc,
        attrs: this.rowAttrFunc,
        columnClassName: this.statusClassNameFormat,
        headerClasses,
        headerAlign: 'left',
        align: 'left',
        style: { width: someUnavailable ? '150px' : '100px' },
      },
      {
        dataField: 'archive_id',
        text: formatMessage(messages.archiveId),
        // actually, we don't need a formatter to use formatExtraData
        formatExtraData: this.props.intl,
        attrs: this.rowAttrFunc,
        headerClasses,
      },
      {
        dataField: 'access_time',
        text: formatMessage(messages.accessTime),
        formatter: this.accessTimeFormatter,
        formatExtraData: this.props.intl,
        attrs: this.rowAttrFunc,
        headerClasses,
      },
      {
        isDummyField: true,
        dataField: 'report',
        text: formatMessage(messages.report),
        formatter: () => <span className="fa fa-file-text-o" />,
        formatExtraData: this.props.intl,
        attrs: this.rowAttrFunc,
        headerClasses,
        headerAlign: 'center',
        align: 'center',
        style: { width: '55px' },
      },
      {
        isDummyField: true,
        dataField: 'delete',
        text: formatMessage(messages.delete),
        formatter: this.deleteFormatter,
        formatExtraData: this.props.intl,
        attrs: this.rowAttrFunc,
        headerClasses,
        headerAlign: 'center',
        align: 'center',
        style: { width: '80px', padding: 0 },
      },
    ];
    if (this.state.deleteFailed || this.state.deleteAllFailed) {
      return (
        <div>
          <FormattedMessage
            id="archivedReports.deleteFailed"
            description="Message when report deletion failed"
            defaultMessage="Archived report deletion failed."
          />
        </div>
      );
    }

    if ((!this.state.loaded && !this.state.loading) || this.state.failed) {
      return (
        <div>
          <FormattedMessage {...LoadingMessages.messageFailed} />
        </div>
      );
    }

    if (this.state.loading || this.state.deleteAllLoading) {
      return <Spinner />;
    }

    if (!subscription) {
      const { profile } = this.state;
      const canManageSubscription = profile.userRole === 'main';

      const htmlMessage = formatMessage({
        id: 'archivedReports.salesPitch',
        description: 'Archived reports sales pitch',
        defaultMessage:
          '<p>Your company has fetched reports {allReports, number} ' +
          'times, including {currentReports, number} times for the ' +
          'company you are looking at right now. <b>Did you remember to ' +
          'archive those reports?</b></p><p>By ordering the Archive ' +
          'service, reports that you have fetched are archived ' +
          'automatically!</p>',
      }, {
        allReports: this.state.teaserTotal,
        currentReports: this.state.teaserCurrent,
      });

      return (
        <div>
          <div>
            <div dangerouslySetInnerHTML={{ __html: htmlMessage }} />
          </div>

          {canManageSubscription ? (
            <Link to="/subscription" className="btn btn-primary">
              <FormattedMessage
                id="archivedReports.orderNowButton"
                description="Archived reports sales pitch order now button"
                defaultMessage="Order now!"
              />
            </Link>
          ) : (
            <FormattedMessage
              id="archivedReports.orderNowWithoutPermission"
              description="Archived reports sales pitch ordering no permission"
              defaultMessage="Ask the main user of your company about ordering the Archive service!"
            />
          )}
        </div>
      );
    }

    const openedReports = this.props.openedReports.filter(or => {
      const report = this.state.reports.find(r => r.archive_id === or.archive_id);

      if (!report) return true;
      if (!this.isReadableReport(report) && this.isReadableReport(or)) {
        return true;
      }

      return false;
    });
    const reports = [...openedReports, ...this.state.reports].filter(r => {
      const country = getISOCountry(this.props.country).alpha2;
      const govOrgId = this.props.govOrgId.replace('-', '');
      const archiveId = `${country}${govOrgId}`;

      if (!r.archive_id.startsWith(archiveId)) return false;

      const accessTime = moment(r.access_time);
      const { startDate, endDate } = this.state;

      if (startDate && accessTime < startDate) return false;
      if (endDate && accessTime > endDate) return false;
      return true;
    });

    const someUnavailable = reports.some(r => !this.isReadableReport(r));

    return (
      <div>
        <ReactTooltip id="archived-reports" />

        <Dialog dialogType={DialogTypes.CONFIRM_REMOVE_ARCHIVED_REPORT}>
          <FormattedMessage
            id="archivedReports.confirmRemoval"
            description="Archived reports confirm removal dialog title"
            defaultMessage="Confirm removal"
          />

          <ConfirmRemoveContent />
        </Dialog>

        <div
          ref={r => {
            this.targetRef = r;
          }}
          className="row"
        >
          <div className="col-md-4 col-xl-3 form-group">
            <label htmlFor="start_date" className="form-control-label">
              <FormattedMessage
                id="archivedReports.startDate"
                description="Archived reports filter: Start date label"
                defaultMessage="Start date"
              />
            </label>
            <Datetime
              inputProps={{
                id: 'start_date',
                name: 'start_date',
                className: 'form-control',
                ...this.datetimeWorkaround(this.state.startDate),
              }}
              isValidDate={current =>
                !this.state.endDate || current.isSameOrBefore(this.state.endDate)
              }
              timeFormat={false}
              locale={locale}
              onChange={this.onStartDateChange}
              value={this.state.startDate}
              dateFormat="YYYY-MM-DD"
              closeOnSelect
            />
          </div>
          <div className="col-md-4 col-xl-3 form-group">
            <label htmlFor="end_date" className="form-control-label">
              <FormattedMessage
                id="archivedReports.endDate"
                description="Archived reports filter: End date label"
                defaultMessage="End date"
              />
            </label>
            <Datetime
              inputProps={{
                id: 'end_date',
                name: 'end_date',
                className: 'form-control',
                ...this.datetimeWorkaround(this.state.endDate),
              }}
              isValidDate={current =>
                !this.state.startDate || current.isSameOrAfter(this.state.startDate)
              }
              timeFormat={false}
              locale={locale}
              onChange={this.onEndDateChange}
              value={this.state.endDate}
              dateFormat="YYYY-MM-DD"
              closeOnSelect
            />
          </div>

          <div
            className={classNames(
              'd-flex',
              'flex-column',
              'col-md-4',
              'col-xl-6',
              'align-items-end',
              'justify-content-center'
            )}
          >
            <ListFilterOptionsComponent callback={this.onClear} />

            <a href="#" onClick={this.onDeleteAll}>
              <i className="fa fa-trash-o" />{' '}
              <FormattedMessage
                id="archivedReports.deleteAllReports"
                description="Delete all archived reports button"
                defaultMessage="Delete all archived reports"
              />
            </a>
          </div>
        </div>

        <div
          className={classNames('row', 'mt-4', 'mb-4', {
            'd-none': !someUnavailable || !subscription.active,
          })}
        >
          <div className="col-12 text-muted font-italic">
            <FormattedMessage
              id="archivedReports.disabledHint"
              description="Archived reports hint about disabled reports"
              defaultMessage={
                'Some of your archived reports are unavailable, because ' +
                'they were fetched before your current subscription began. ' +
                'Please contact customer service if you have any questions.'
              }
            />
          </div>
        </div>

        <div
          className={classNames('row', 'mt-4', 'mb-4', {
            'd-none': !someUnavailable || subscription.active,
          })}
        >
          <div className="col-12 text-muted font-italic">
            <FormattedMessage
              id="archivedReports.disabledNoSubscriptionHint"
              description={
                'Archived reports hint about disabled reports when user ' +
                'has no active subscription'
              }
              defaultMessage={
                'Your archived reports are unavailable, because your ' +
                'Arkisto license has been terminated. ' +
                'Please contact customer service if you have any questions.'
              }
            />
          </div>
        </div>

        <BootstrapTable
          keyField="access_time"
          columns={columns}
          data={reports}
          striped
          hover
          bordered={false}
          defaultSorted={defaultSorted}
          defaultSortDirection="desc"
          rowEvents={rowEvents}
          noDataIndication={() => (
            <FormattedMessage
              id="archivedReports.empty"
              description="Message shown when archived reports table is empty"
              defaultMessage="There is no data to display"
            />
          )}
          wrapperClasses="archived-reports"
          classes={classNames('table-striped--bol', 'table--bol-compact')}
          rowClasses={this.rowClassNameFormat}
        />
      </div>
    );
  }
}

export default injectIntl(ArchivedReportsListComponent);
