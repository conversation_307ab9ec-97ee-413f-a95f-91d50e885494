/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import CompanyDetailsViewComponent from './CompanyDetailsViewComponent';
import companyDetailsCloseClicked from '../../actions/actionCreators/CompanyDetailsCloseClicked';
import ContextBlock from '../shared/ContextBlock';
/* eslint-enable max-len */

class CompanyDetailsContextualComponent extends React.Component {
  static get defaultProps() {
    return {
      isOpen: false,
      targetRef: null,
      companyName: null,
    };
  }

  static get propTypes() {
    return {
      isOpen: PropTypes.bool,
      targetRef: PropTypes.object,
      companyName: PropTypes.string,
      companyId: PropTypes.string,
      placement: PropTypes.string,
    };
  }

  renderTitle() {
    return this.props.companyName ? (
      this.props.companyName
    ) : (
      <FormattedMessage
        id="companyDetails.contextual_title"
        description="Company details popup title in context"
        defaultMessage="Company details"
      />
    );
  }

  render() {
    if (!this.props.isOpen) {
      return null;
    }

    return (
      <ContextBlock
        isOpen={this.props.isOpen}
        target={this.props.targetRef}
        title={this.renderTitle()}
        toggle={companyDetailsCloseClicked}
        twoColumn
        outerClasses={classNames('company-details-view details-view')}
        placement={this.props.placement}
      >
        <div>
          <CompanyDetailsViewComponent
            formatting="context-block"
            companyId={this.props.companyId}
          />
        </div>
      </ContextBlock>
    );
  }
}

export default CompanyDetailsContextualComponent;
