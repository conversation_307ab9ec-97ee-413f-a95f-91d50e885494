import React from 'react';
import PropTypes from 'prop-types';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import classNames from 'classnames';

import { preventDefault } from '../../helpers/EventHandlers';
import { featureActive } from '../../helpers/FeatureFlags';

class CompanyListHeadComponent extends React.Component {
  static get propTypes() {
    return {
      sortBy: PropTypes.string,
      sortDir: PropTypes.number,
      narrow: PropTypes.bool,
      onSort: PropTypes.func,
    };
  }

  static get defaultProps() {
    return {
      sortBy: null,
      sortDir: 1,
      narrow: false,
      onSort: null,
    };
  }

  onSort(event, column) {
    preventDefault(event);
    if (this.props.onSort) this.props.onSort(column);
  }

  renderChevron(column) {
    const { sortBy, sortDir } = this.props;
    if (!column || column !== sortBy) return null;

    return (
      <span className="text-primary">
        <i
          className={classNames('fa', {
            'fa-chevron-down': sortDir === -1,
            'fa-chevron-up': sortDir === 1,
          })}
          style={{ position: 'relative', top: '-2px', fontSize: '12px' }}
        />
      </span>
    );
  }

  render() {
    const { narrow, onSort } = this.props;
    const thClasses = classNames('th-label', {
      'pointer-events-none': !onSort,
    });

    return (
      <tr>
        <th>
          {this.renderChevron('name')}

          <a href={onSort ? '#' : null} onClick={e => this.onSort(e, 'name')} className={thClasses}>
            <FormattedMessage
              id="companies.nameColumn"
              description="Company name table column heading"
              defaultMessage="Company"
            />
          </a>
        </th>
        <th className={narrow ? 'hidden' : null}>
          {this.renderChevron('status')}

          <a
            href={onSort ? '#' : null}
            onClick={e => this.onSort(e, 'status')}
            className={thClasses}
          >
            {featureActive('archived_reports') ? (
              <FormattedMessage
                id="companies.latestStatusColumn"
                description="Company latest status table column heading"
                defaultMessage="Latest status"
              />
            ) : (
              <FormattedMessage
                id="companies.statusColumn"
                description="Company status table column heading"
                defaultMessage="Status report"
              />
            )}
          </a>
        </th>
        <th className={narrow ? 'hidden' : null}>
          <a onClick={preventDefault} className="th-label pointer-events-none">
            <FormattedMessage
              id="companies.reportColumn"
              description="Company report table column heading"
              defaultMessage="Report"
            />
          </a>
        </th>
        <th className={narrow ? 'hidden' : null}>
          {this.renderChevron('country')}

          <a
            href={onSort ? '#' : null}
            onClick={e => this.onSort(e, 'country')}
            className={thClasses}
          >
            <FormattedMessage
              id="companies.countryColumn"
              description="Company country table column heading"
              defaultMessage="Country"
            />
          </a>
        </th>
        {featureActive('projects') && (
          <th className={narrow ? 'hidden' : null}>
            <a onClick={preventDefault} className="th-label pointer-events-none">
              <FormattedMessage
                id="companies.ProjectsColumn"
                description="Company projects count table column heading"
                defaultMessage="Projects"
              />
            </a>
          </th>
        )}
      </tr>
    );
  }
}

export default CompanyListHeadComponent;
