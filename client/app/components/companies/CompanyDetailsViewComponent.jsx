/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import classNames from 'classnames';

import { companyViewStore, companyStore, subscriptionStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import { featureActive } from '../../helpers/FeatureFlags';
import { getGovOrgId } from '../../helpers/Company';
import TabsComponent from '../shared/TabsComponent';
import TabComponent from '../shared/TabComponent';
import RelatedProjectsListComponent from './RelatedProjectsListComponent';
import ArchivedReportsListComponent from './ArchivedReportsListComponent';
import ProjectCommentsListComponent from './ProjectCommentsListComponent';
import ProjectCommentsAddCommentComponent from './ProjectCommentsAddCommentComponent';
import Spinner from '../shared/Spinner';
import CompanyDetailsComponent from './CompanyDetailsComponent';
import CompanyStatusComponent from './CompanyStatusComponent';

/* eslint-enable max-len */

class CompanyDetailsViewComponent extends React.Component {
  static get propTypes() {
    return {
      formatting: PropTypes.oneOf(['modal', 'context-block']),
      companyId: PropTypes.string,
      companyName: PropTypes.string,
    };
  }

  static get defaultProps() {
    return {
      companyName: '',
    };
  }

  constructor(props) {
    super(props);
    this.companyViewSub = new StoreSubscription(companyViewStore, this.storeChanged.bind(this));
    this.companySub = new StoreSubscription(companyStore, this.storeChanged.bind(this));
    this.subscriptionSub = new StoreSubscription(
      subscriptionStore,
      this.subscriptionStoreChanged.bind(this)
    );
    this.state = Object.assign(
      {
        openedReports: [],
        dropdownToggle: false,
      },
      this.mapStoreToState(companyViewStore.getState(), companyStore.getState()),
      this.mapSubscriptionStoreToState(subscriptionStore.getState())
    );
  }

  componentDidMount() {
    this.companyViewSub.activate();
    this.companySub.activate();
  }

  componentDidUpdate() {
    ReactTooltip.rebuild();
  }

  componentWillUnmount() {
    this.companyViewSub.deactivate();
    this.companySub.deactivate();
  }

  mapStoreToState(storeState) {
    return {
      companyId: storeState.company_id || this.props.companyId,
      name: storeState.name,
      govOrgIds: storeState.gov_org_ids,
      country: storeState.country,
      status: storeState.status,
      terminated: storeState.terminated,
      permissions: storeState.permissions,
      isReliablePartner: storeState.is_reliable_partner,
      hasSubsidiaries: storeState.has_subsidiaries,
      hasCombinedReport: storeState.has_combined_report,

      loading: storeState.company_loading,
      loaded: storeState.company_loaded,
      failed: storeState.company_failed,
    };
  }

  mapSubscriptionStoreToState(storeState) {
    return {
      subscription: storeState.subscription,
    };
  }

  storeChanged() {
    this.setState(this.mapStoreToState(companyViewStore.getState()));
  }

  subscriptionStoreChanged() {
    this.setState(this.mapSubscriptionStoreToState(subscriptionStore.getState()));
  }

  renderCertificatePublishingInfo(columnClasses) {
    return (
      <div className="row">
        <div className={columnClasses} />
        <div className={columnClasses}>
          <span className="certificate_publication_info">
            <FormattedMessage
              id="companyDetails.certificate_publication"
              description="Information about certificate of publication"
              defaultMessage="Information below is not covered by the publishing certificates"
            />
          </span>
        </div>
      </div>
    );
  }

  renderTabs() {
    const rowClass = this.props.formatting !== 'context-block' ? 'row' : '';

    const viewArchivedReports = featureActive('archived_reports');

    const viewRelatedProjects = this.state.permissions.includes('view_related_projects');

    const canViewComments = this.state.permissions.includes('can_view_comments');

    if (!viewArchivedReports && !viewRelatedProjects && !canViewComments) {
      return null;
    }

    const govOrgId = getGovOrgId(this.state.country, this.state.govOrgIds);

    return (
      <TabsComponent rowClass={rowClass}>
        <TabComponent
          enabled={canViewComments}
          navClass="comments-tab"
          title={
            <FormattedMessage
              id="companyDetails.comments"
              description="Label for comments"
              defaultMessage="Comments"
            />
          }
        >
          <ProjectCommentsAddCommentComponent />
          <ProjectCommentsListComponent />
        </TabComponent>
        <TabComponent
          enabled={viewRelatedProjects && featureActive('projects')}
          navClass="related-projects-tab"
          title={
            <FormattedMessage
              id="companyDetails.relatedProject"
              description="Label for company related projects"
              defaultMessage="Related projects "
            />
          }
        >
          <RelatedProjectsListComponent companyId={this.state.companyId} />
        </TabComponent>
        <TabComponent
          enabled={viewArchivedReports}
          navClass="archived-reports-tab"
          title={
            <FormattedMessage
              id="companyDetails.archivedReports"
              description="Label for company archived reports"
              defaultMessage="Archived reports"
            />
          }
        >
          <ArchivedReportsListComponent
            companyId={this.props.companyId}
            country={this.state.country || this.props.companyId.split('/')[0]}
            govOrgId={govOrgId || this.props.companyId.split('/')[1]}
            name={this.state.name || this.props.companyName}
            openedReports={this.state.openedReports}
          />
        </TabComponent>
      </TabsComponent>
    );
  }

  render() {
    if (this.state.loaded || (!this.state.loading && featureActive('archived_reports'))) {
      const viewClasses = classNames({
        row: true,
        'details-overview u-border': this.props.formatting !== 'context-block',
      });
      const columnClasses = classNames({
        'col-lg-6': this.props.formatting !== 'context-block',
        'col-md-12': true,
      });
      return (
        <div>
          <div id="company_details_View" className={viewClasses}>
            <div className="col-12">
              {!featureActive('archived_reports') &&
                this.renderCertificatePublishingInfo(columnClasses)}
              <div className="row">
                <CompanyDetailsComponent className={columnClasses} showVat company={this.state} />
                <CompanyStatusComponent className={columnClasses} company={this.state} />
              </div>
            </div>
          </div>
          {this.renderTabs()}
        </div>
      );
    }
    const loadingMessage = this.state.loading ? (
      <Spinner />
    ) : (
      <FormattedMessage
        id="companyDetails.loadingFailedMessage"
        description="Loading message in the company details box"
        defaultMessage="Loading failed"
      />
    );
    return (
      <div id="company_details_View" className="row details-loading u-border">
        <div className="col-12">
          <div className="row">{loadingMessage}</div>
        </div>
      </div>
    );
  }
}

export default injectIntl(CompanyDetailsViewComponent);
