import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import LocalizedText from '../i18n/LocalizedText';

class CompanyDetailsStatusItemViewComponent extends React.Component {
  renderStatus() {
    const status = this.props.label_status;

    let snippet = null;
    if (Array.isArray(status)) {
      // status is an array
      if (status.length === 1) {
        snippet = (
          <span>
            <LocalizedText translations={status[0]} />
          </span>
        );
      } else {
        snippet = (
          <ul>
            {status.map(item => (
              <li key={item.sv}>
                <LocalizedText translations={item} />
              </li>
            ))}
          </ul>
        );
      }
    } else if (typeof status === 'object') {
      // status is a string
      snippet = (
        <span>
          <LocalizedText translations={status} />
        </span>
      );
    }
    return snippet;
  }

  render() {
    return (
      <div
        className={classNames(
          'collected-status',
          'd-flex',
          'justify-content-start',
          'align-content-start',
          'pointer-events-none'
        )}
      >
        <small className="pt-2 mr-3">
          <i
            className={classNames(
              'material-icons',
              'text-normal',
              `status-${this.props.interpretation}`
            )}
          >
            {this.props.icon}
          </i>
        </small>
        <div className="d-inline row-status-text">
          <span>{this.props.label}: </span>
          {this.renderStatus()}
        </div>
      </div>
    );
  }
}

CompanyDetailsStatusItemViewComponent.propTypes = {
  interpretation: PropTypes.string.isRequired,
  icon: PropTypes.node.isRequired,
  label: PropTypes.node.isRequired,
  label_status: PropTypes.oneOfType([PropTypes.object, PropTypes.array]).isRequired,
};

export default CompanyDetailsStatusItemViewComponent;
