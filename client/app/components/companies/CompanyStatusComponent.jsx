import React from 'react';
import classNames from 'classnames';
import moment from 'moment';
import PropTypes from 'prop-types';
import { defineMessages, injectIntl } from 'react-intl';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import LocalizedText from '../i18n/LocalizedText';
import { getGovOrgId } from '../../helpers/Company';
import { featureActive } from '../../helpers/FeatureFlags';
import { getReportURL, getFilename } from '../../helpers/Reports';
import CompanyDetailsStatusItemViewComponent from './CompanyDetailsStatusItemViewComponent';
import StatusIconComponent from '../shared/StatusIconComponent';
import ViewLatestReportButtonComponent from '../shared/ViewLatestReportButtonComponent';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  viewLatestReport: {
    id: 'companyDetails.companyLatestReportLink',
    description: 'Label company latest report link',
    defaultMessage: 'View latest report',
  },
});

class CompanyStatusComponent extends React.Component {
  static get propTypes() {
    return {
      className: PropTypes.string,
      company: PropTypes.object.isRequired,
      renderSubsidiaries: PropTypes.func,
      showStatusLabel: PropTypes.bool,
      intl: intlPropType.isRequired,
    };
  }

  static get defaultProps() {
    return {
      showStatusLabel: true,
    };
  }

  render() {
    const { formatMessage } = this.props.intl;
    const { className, company, showStatusLabel } = this.props;
    const {
      country,
      isReliablePartner,
      status,
      name,
      govOrgIds,
      subscription,
      hasCombinedReport,
      hasSubsidiaries,
    } = company;

    const hideLatest = featureActive('archived_reports') && (!isReliablePartner || hasSubsidiaries);

    if (hideLatest) {
      return (
        <div className={className}>
          <div className="card noborder">
            <div className="card-title text-uppercase">
              <FormattedMessage
                id="companyDetails.rpDetailsHeading"
                description="RP details subheading in the company popup"
                defaultMessage="Reliable Partner information"
              />
            </div>
            {hasSubsidiaries ? (
              this.props.renderSubsidiaries()
            ) : (
              <div className="d-flex">
                <FormattedMessage
                  id="searchCompanyDetails.noReportAvailable"
                  description="Search company no report available"
                  defaultMessage="No reports available"
                />
              </div>
            )}
          </div>
        </div>
      );
    }
    if (!status) return null;

    const shouldShowCountryWarning =
      featureActive('archived_reports') && !['FIN', 'EST'].includes(country);

    const shouldShowSubscriptionWarning =
      featureActive('archived_reports') &&
      ['FIN', 'EST'].includes(country) &&
      subscription &&
      !subscription.active;

    const govOrgId = getGovOrgId(country, govOrgIds);
    let { companyId } = company;
    if (featureActive('archived_reports') && !companyId) {
      companyId = `${country}/${govOrgId}`;
    }

    const showUpdatedAt = ['LTU', 'SWE', 'POL', 'LVA'].includes(country);
    const filename = getFilename(country, govOrgId, name);

    const shouldShowOnlyPdfsWarning =
      featureActive('archived_reports') &&
      featureActive('web_reports') &&
      ['FIN'].includes(country) &&
      subscription &&
      subscription.active &&
      !hasCombinedReport;

    const enableLangSelection =
      featureActive('archived_reports') &&
      featureActive('web_reports') &&
      !showUpdatedAt &&
      !hasCombinedReport;

    return (
      <div className={className}>
        <div className="card noborder">
          {featureActive('archived_reports') && (
            <div className="card-title text-uppercase">
              <FormattedMessage
                id="companyDetails.rpDetailsHeading"
                description="RP details subheading in the company popup"
                defaultMessage="Reliable Partner information"
              />
            </div>
          )}
          <div
            className={classNames('d-flex', 'pointer-events-none', {
              'card-title': !featureActive('archived_reports'),
              'text-uppercase': !featureActive('archived_reports'),
            })}
          >
            {showStatusLabel && (
              <FormattedMessage
                id="companyDetails.companyStatusHeading"
                description="Company status heading"
                defaultMessage="Status: "
              />
            )}
            <StatusIconComponent
              status={status.overall_status}
              additionalClasses={[
                showStatusLabel ? 'mx-3' : 'mr-3',
                'align-self-center',
                'status-cell',
                'company-status',
              ]}
            />
            <span className={`status-cell status-${status.overall_status}`}>
              <LocalizedText translations={status.overall_status_text} />
            </span>
          </div>
          <div className="details-list--list">
            {status.items.map(item => (
              <CompanyDetailsStatusItemViewComponent
                key={item.label.en}
                label={<LocalizedText translations={item.label} />}
                label_status={item.status}
                interpretation={item.interpretation}
                icon={item.icon}
              />
            ))}
            {featureActive('archived_reports') && showUpdatedAt && (
              <FormattedMessage
                id="companyDetails.companyReportUpdatedAt"
                description="Info about report updated at date"
                defaultMessage="Report updated: {updatedAt}"
                values={{
                  updatedAt: moment(status.updated_at).format('L'),
                }}
              />
            )}
            <div className="pt-5">
              {featureActive('archived_reports') && (
                <div className="card-title">
                  <FormattedMessage
                    id="companyDetails.checkReportReminder"
                    description="Reminder text to check the report"
                    defaultMessage="Download, check and save the report"
                  />
                </div>
              )}
              <div>
                {featureActive('archived_reports') ? (
                  <ViewLatestReportButtonComponent
                    title={formatMessage(messages.viewLatestReport)}
                    govOrgId={govOrgId}
                    country={country}
                    filename={filename}
                    enableLangSelection={enableLangSelection}
                    companyId={companyId}
                  />
                ) : (
                  <a
                    href={getReportURL(companyId, filename, this.props.intl.locale)}
                    id="company_report_link"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <i
                      id="company_details_view.report_pdf_icon"
                      className="fa fa-file-pdf-o fa-pr--bol"
                      aria-hidden="true"
                    />
                    <FormattedMessage
                      id="companyDetails.companyReportLink"
                      description="Label company report link"
                      defaultMessage="View complete report"
                    />
                  </a>
                )}
              </div>
            </div>
          </div>

          {shouldShowCountryWarning && (
            <div id="company-alert-warning" className="mt-5 alert alert-warning">
              <FormattedMessage
                id="companyDetails.nonArchivable"
                description="Message shown when a report will not be archived"
                defaultMessage={
                  'This report will not be archived. ' +
                  'Only Finnish and Estonian company reports can be archived.'
                }
              />
            </div>
          )}

          {shouldShowSubscriptionWarning && (
            <div id="company-alert-warning" className="mt-5 alert alert-warning">
              <FormattedMessage
                id="companyDetails.noSubscription"
                description="Message shown in company search details when user has no subscription"
                defaultMessage={
                  'This report will not be available for download in ' +
                  'Search history since your Report PRO subscription is ' +
                  'not active.'
                }
              />
            </div>
          )}

          {shouldShowOnlyPdfsWarning && (
            <div id="company-alert-warning" className="mt-5 alert alert-warning">
              <FormattedMessage
                id="companyDetails.pdfsOnly"
                description="Message shown archived report will be in pdf only"
                defaultMessage="The notification for only PDFs in the archive."
              />
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default injectIntl(CompanyStatusComponent);
