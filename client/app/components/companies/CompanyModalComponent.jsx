/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { companyStore, companyViewStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import { featureActive } from '../../helpers/FeatureFlags';
import DetailsModalComponent from '../shared/DetailsModalComponent';
import CompanyDetailsViewComponent from './CompanyDetailsViewComponent';
import selectCompanyDetails from '../../actions/actionCreators/CompanyDetailsSelect';
import detailsViewClose from '../../actions/actionCreators/DetailsViewClose';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';

class CompanyModalComponent extends React.Component {
  static get propTypes() {
    return {
      params: PropTypes.object,
      router: routerPropType.isRequired,
    };
  }

  constructor(props) {
    super(props);

    this.companyViewStore = new StoreSubscription(companyViewStore, this.storeChanged.bind(this));
    this.companyStore = new StoreSubscription(companyStore, this.storeChanged.bind(this));
    this.state = this.mapStoreToState(companyViewStore.getState(), companyStore.getState());
  }

  componentDidMount() {
    this.companyViewStore.activate();
    this.companyStore.activate();
    this.loadCompanyDetails();
  }

  componentDidUpdate(prevProps) {
    const { itemId: prevItemId } = prevProps.params;
    const { itemId } = this.props.params;

    if (prevItemId !== itemId) {
      this.loadCompanyDetails();
    }
  }

  componentWillUnmount() {
    this.companyViewStore.deactivate();
    this.companyStore.deactivate();
  }

  onDetailsClose() {
    detailsViewClose();
    this.props.router.navigate('/companies');
  }

  storeChanged(/* storeState */) {
    this.setState(this.mapStoreToState(companyViewStore.getState(), companyStore.getState()));
  }

  mapStoreToState(storeState, companyStoreState) {
    let company = null;
    if (featureActive('archived_reports')) {
      // BOL-3049 if comapny is not found in db, use its name from company list
      // to avoid null in archived reports filenames
      company = companyStoreState.companies.find(c => c.id.includes(this.props.params.itemId));
    } else {
      company = companyStoreState.companyById[this.props.params.itemId];
    }

    return {
      name: storeState.name || (company || {}).name,
      loading: storeState.company_loading,
    };
  }

  loadCompanyDetails() {
    const { itemId } = this.props.params;
    setTimeout(() => selectCompanyDetails(itemId), 0);
  }

  render() {
    const { loading, name } = this.state;

    let title = null;
    if (name) {
      title = name;
    } else if (loading) {
      title = (
        <FormattedMessage
          id="companyDetails.defaultModalTitle"
          description="Company details window default title"
          defaultMessage="Loading"
        />
      );
    } else {
      title = featureActive('use_stv_theme') ? (
        <FormattedMessage
          id="companyDetails.loadingErrorMessage"
          description="Loading error message in the company details box"
          defaultMessage="Error"
        />
      ) : (
        <FormattedMessage
          id="companyDetails.loadingFailedMessage"
          description="Loading message in the company details box"
          defaultMessage="Loading failed"
        />
      );
    }

    return (
      <DetailsModalComponent
        title={title}
        onDetailsClose={() => this.onDetailsClose()}
        extraClassNames={{
          'company-details-view tabbed-details-view details-view': true,
        }}
      >
        <CompanyDetailsViewComponent
          formatting="modal"
          companyId={this.props.params.itemId}
          companyName={name}
        />
      </DetailsModalComponent>
    );
  }
}

export default withRouter(CompanyModalComponent);
