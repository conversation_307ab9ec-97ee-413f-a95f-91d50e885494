/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import ReactTooltip from 'react-tooltip';
import { defineMessages, injectIntl } from 'react-intl';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { companyViewStore, subscriptionStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import { getGovOrgId } from '../../helpers/Company';
import {
  getSubsidiaryGovOrgId,
  getSubsidiaryForCurrentOrg,
  getCountryCodeAlpha3,
} from '../../helpers/Subsidiaries';
import Spinner from '../shared/Spinner';
import companySearchDetailsOpenParent from '../../actions/actionCreators/CompanySearchDetailsOpenParent';
import { getZeckitHostName } from '../../helpers/App';
import CompanyDetailsComponent from '../companies/CompanyDetailsComponent';
import CompanyStatusComponent from '../companies/CompanyStatusComponent';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';
/* eslint-enable max-len */

const messages = defineMessages({
  whatIsZeckitLink: {
    id: 'searchCompanyDetails.zeckitWhatIsLink',
    description: 'What is Zeckit link URL in the company popup',
    defaultMessage: '/en/en-what-is-zeckit/',
  },
  viewLatestReport: {
    id: 'companyDetails.companyLatestReportLink',
    description: 'Label company latest report link',
    defaultMessage: 'View latest report',
  },
});

class SearchDetailsViewComponent extends React.Component {
  static get propTypes() {
    return {
      formatting: PropTypes.oneOf(['modal', 'context-block']),
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired
    };
  }

  constructor(props) {
    super(props);
    this.companyViewSub = new StoreSubscription(
      companyViewStore,
      this.companyViewStoreChanged.bind(this)
    );
    this.subscriptionSub = new StoreSubscription(
      subscriptionStore,
      this.subscriptionStoreChanged.bind(this)
    );
    this.state = Object.assign(
      {},
      this.mapCompanyViewStoreToState(companyViewStore.getState()),
      this.mapSubscriptionStoreToState(subscriptionStore.getState())
    );
  }

  componentDidMount() {
    this.companyViewSub.activate();
    this.subscriptionSub.activate();
  }

  componentDidUpdate() {
    ReactTooltip.rebuild();
  }

  componentWillUnmount() {
    this.companyViewSub.deactivate();
    this.subscriptionSub.deactivate();
  }

  mapCompanyViewStoreToState(storeState) {
    return {
      companyId: storeState.company_id,
      name: storeState.name,
      govOrgIds: storeState.gov_org_ids,
      country: storeState.country,
      status: storeState.status,
      terminated: storeState.terminated,
      permissions: storeState.permissions,
      isReliablePartner: storeState.is_reliable_partner,
      hasSubsidiaries: storeState.has_subsidiaries,
      subsidiaries: storeState.subsidiaries,
      hasCombinedReport: storeState.has_combined_report,

      loading: storeState.company_loading,
      loaded: storeState.company_loaded,
      failed: storeState.company_failed,
      error: storeState.error,
    };
  }

  mapSubscriptionStoreToState(storeState) {
    return {
      subscription: storeState.subscription,
    };
  }

  companyViewStoreChanged() {
    this.setState(this.mapCompanyViewStoreToState(companyViewStore.getState()));
  }

  subscriptionStoreChanged() {
    this.setState(this.mapSubscriptionStoreToState(subscriptionStore.getState()));
  }

  renderSubsidiaries(state) {
    // get subsidiary by current gov_org_id and country
    const govOrgId = getGovOrgId(state.country, state.govOrgIds);
    const subsidiary = getSubsidiaryForCurrentOrg(state.country, govOrgId, state.subsidiaries);
    const subsGovOrgId = getSubsidiaryGovOrgId(subsidiary.country, subsidiary.gov_org_ids);
    const subsCountry = getCountryCodeAlpha3(subsidiary.country);
    const hasMultipleSubsidiaries = state.subsidiaries.length > 1;
    return (
      <div>
        <div className="d-flex">
          <FormattedMessage
            id="searchCompanyDetails.hasSubsidiaries"
            description="Search company has subsidiaries"
            defaultMessage="The form of this company is a foreign organisation"
          />
        </div>
        {hasMultipleSubsidiaries ? (
          <div className="mt-4 text-danger">
            <FormattedMessage
              id="searchCompanyDetails.multipleSubsidiaries"
              description="Multiple subsidiaries info message"
              defaultMessage="Multiple parent companies found"
            />
          </div>
        ) : (
          <div className="mt-4">
            <a
              className="btn btn-primary"
              href="#"
              onClick={() =>
                setTimeout(
                  () =>
                    companySearchDetailsOpenParent(
                      subsidiary.country,
                      subsCountry,
                      subsGovOrgId,
                      this.props.router.navigate
                    ),
                  0
                )
              }
              id="goto_parent_company_link"
            >
              <FormattedMessage
                id="searchCompanyDetails.gotoParentCompany"
                description="Go to parent company link"
                defaultMessage="Go to parent company"
              />
            </a>
          </div>
        )}
      </div>
    );
  }

  renderWidget() {
    const { locale, formatMessage } = this.props.intl;
    const { country, govOrgIds } = this.state;
    const zeckitHost = getZeckitHostName();
    const whatIsZeckitLink = formatMessage(messages.whatIsZeckitLink);
    if (country !== 'FIN') return null;

    const govOrgId = getGovOrgId(country, govOrgIds);

    return (
      <div className="col-lg-9 col-md-12">
        <div className="card noborder">
          <div className="d-flex align-items-center mb-4">
            <div className="card-title text-uppercase mb-0">
              <FormattedMessage
                id="searchCompanyDetails.zeckitDetailsHeading"
                description="Zeckit details subheading in the company popup"
                defaultMessage="Zeckit information"
              />
            </div>

            {/* eslint-disable react/jsx-no-target-blank */}
            <small>
              <a
                href={`https://www.vastuugroup.fi${whatIsZeckitLink}`}
                className="ml-4"
                target="_blank"
                rel="noopener"
              >
                <FormattedMessage
                  id="searchCompanyDetails.zeckitWhatIs"
                  description="What is Zeckit link text in the company popup"
                  defaultMessage="What is Zeckit?"
                />
              </a>
            </small>

            {/* eslint-enable react/jsx-no-target-blank */}
          </div>

          <iframe
            title="Zeckit widget"
            // eslint-disable-next-line max-len
            src={`https://${zeckitHost}/selvitys/widget/embed/FI/${govOrgId}?lang=${locale}`}
          />
        </div>
      </div>
    );
  }

  render() {
    if (this.state.loaded) {
      const viewClasses = classNames({
        row: true,
        'details-overview u-border': this.props.formatting !== 'context-block',
      });
      const columnClasses = classNames({
        'col-lg-6': this.props.formatting !== 'context-block',
        'col-md-12': true,
      });
      return (
        <div>
          <div id="company_details_View" className={viewClasses}>
            <div className="col-12">
              <div className="row">
                <CompanyDetailsComponent className={columnClasses} company={this.state} />
                <CompanyStatusComponent
                  className={columnClasses}
                  company={this.state}
                  renderSubsidiaries={() => this.renderSubsidiaries(this.state)}
                />
              </div>
            </div>

            <div className="col-12">
              <div className="row">{this.renderWidget()}</div>
            </div>
          </div>
        </div>
      );
    }

    let loadingMessage = <Spinner />;
    if (!this.state.loading) {
      loadingMessage = (
        <FormattedMessage
          id="companyDetails.loadingFailedMessage"
          description="Loading message in the company details box"
          defaultMessage="Loading failed"
        />
      );

      const { error } = this.state;
      if (error && error.status === 404) {
        loadingMessage = (
          <FormattedMessage
            id="companyDetails.notFoundMessage"
            description="Missing company message in the company details box"
            defaultMessage={
              'Company details are unavailable. Please contact the ' +
              'customer service for assistance.'
            }
          />
        );
      }
    }

    return (
      <div id="company_details_View" className="row details-loading u-border">
        <div className="col-12">
          <div className="row">{loadingMessage}</div>
        </div>
      </div>
    );
  }
}

export default injectIntl(withRouter(SearchDetailsViewComponent));
