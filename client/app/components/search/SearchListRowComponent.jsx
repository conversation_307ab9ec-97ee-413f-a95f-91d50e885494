import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { defineMessages } from 'react-intl';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  terminatedTooltip: {
    id: 'search.row.terminated.tooltip',
    description: 'Search company row terminated tooltip',
    defaultMessage: 'Information source: Bisnode',
  },
});

const isIE = false || !!document.documentMode;
const isEdge = !isIE && !!window.StyleMedia;
const isIEorEdge = isIE || isEdge;

const renderTerminated = intl => (
  <FormattedMessage
    id="search.row.terminated"
    description="Search company row terminated label"
    defaultMessage="Terminated"
  >
    {content => (
      <span
        className="badge badge-danger align-self-start ml-4"
        data-tip={intl.formatMessage(messages.terminatedTooltip)}
      >
        {content}
      </span>
    )}
  </FormattedMessage>
);

const SearchListRowComponent = ({
  selected,
  onClick,
  gov_org_id,
  country,
  name,
  rala,
  is_reliable_partner,
  terminated,
  narrow = false
}) => (
  <tr className={classNames({ 'table-active': selected })}>
    <td onClick={onClick}>
      <div>
        {/*
          We could use `d-flex` here if it wasn't for an IE bug
          see commit 51f610d9ee7b9b16fa7612c7eeb8f73519d68236
        */}
        <div
          className={classNames(
            'left',
            { 'title-block': !isIEorEdge },
            { 'title-block-ie': isIEorEdge },
            { 'title-block-narrow-ie': isIEorEdge && narrow }
          )}
        >
          <div className="d-flex">
            {name}
            {terminated &&
              !is_reliable_partner &&
              !narrow &&
              renderTerminated(intl)}
          </div>
          <span className="sub-title">{gov_org_id}</span>
        </div>
      </div>
    </td>
    <td className={classNames({ hidden: narrow })} onClick={onClick}>
      {country}
    </td>
    <td className={classNames({ 'pl-0': narrow })} onClick={onClick}>
      {is_reliable_partner && !narrow && (
        <img
          alt="Reliable Partner"
          height="40"
          src={`/static/img/reliable_partner_${intl.locale}.png`}
        />
      )}

      {is_reliable_partner && narrow && <i className="fa fa-check" />}

      {!is_reliable_partner && <span>-</span>}
    </td>
    <td className={classNames({ 'pl-0': narrow })} onClick={onClick}>
      {rala && rala.competence && (
        <img
          alt="RALA competence"
          height="40"
          className="mx-2"
          src={`/static/img/rala_comp_${intl.locale}.jpg`}
        />
      )}

      {rala && rala.certified && (
        <img
          alt="RALA certificate"
          height="40"
          className="mx-2"
          src={`/static/img/rala_cert_${intl.locale}.jpg`}
        />
      )}
      {terminated
        && !is_reliable_partner
        && narrow && renderTerminated(intl)}
    </td>
  </tr>
);

SearchListRowComponent.propTypes = {
  selected: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  gov_org_id: PropTypes.string,
  country: PropTypes.string,
  name: PropTypes.string,
  rala: PropTypes.object,
  is_reliable_partner: PropTypes.bool,
  terminated: PropTypes.bool,
  narrow: PropTypes.bool,
  intl: intlPropType.isRequired
};

export default SearchListRowComponent;
