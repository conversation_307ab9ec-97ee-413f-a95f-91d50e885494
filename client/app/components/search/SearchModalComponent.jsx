/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../../components/i18n/FormattedMessage'

import { companyViewStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import { featureActive } from '../../helpers/FeatureFlags';
import DetailsModalComponent from '../shared/DetailsModalComponent';
import SearchDetailsViewComponent from './SearchDetailsViewComponent';
import selectCompanySearchDetails from '../../actions/actionCreators/CompanySearchDetailsSelect';
import detailsViewClose from '../../actions/actionCreators/DetailsViewClose';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';

class SearchModalComponent extends React.Component {
  static get propTypes() {
    return {
      params: PropTypes.object,
      router: routerPropType.isRequired
    };
  }

  constructor(props) {
    super(props);

    this.companyViewStore = new StoreSubscription(companyViewStore, this.storeChanged.bind(this));
    this.state = this.mapStoreToState(companyViewStore.getState());
  }

  componentDidMount() {
    this.companyViewStore.activate();
    this.loadCompanyDetails();
  }

  componentDidUpdate(prevProps) {
    const { itemId: prevItemId } = prevProps.params;
    const { itemId } = this.props.params;

    if (prevItemId !== itemId) {
      this.loadCompanyDetails();
    }
  }

  componentWillUnmount() {
    this.companyViewStore.deactivate();
  }

  onDetailsClose() {
    detailsViewClose();
    this.props.router.navigate('/search');
  }

  storeChanged(/* storeState */) {
    this.setState(this.mapStoreToState(companyViewStore.getState()));
  }

  mapStoreToState(storeState) {
    return {
      name: storeState.name,
      loading: storeState.company_loading,
    };
  }

  loadCompanyDetails() {
    const { itemId } = this.props.params;
    const [country, govOrgId] = itemId.split('/');
    setTimeout(() => selectCompanySearchDetails(country, govOrgId), 0);
  }

  render() {
    const { loading, name } = this.state;

    let title = null;
    if (name) {
      title = name;
    } else if (loading) {
      title = (
        <FormattedMessage
          id="companyDetails.defaultModalTitle"
          description="Company details window default title"
          defaultMessage="Loading"
        />
      );
    } else {
      title = featureActive('use_stv_theme') ? (
        <FormattedMessage
          id="companyDetails.loadingErrorMessage"
          description="Loading error message in the company details box"
          defaultMessage="Error"
        />
      ) : (
        <FormattedMessage
          id="companyDetails.loadingFailedMessage"
          description="Loading message in the company details box"
          defaultMessage="Loading failed"
        />
      );
    }

    return (
      <DetailsModalComponent
        title={title}
        onDetailsClose={() => this.onDetailsClose()}
        extraClassNames={{
          'company-details-view tabbed-details-view details-view': true,
        }}
      >
        <SearchDetailsViewComponent formatting="modal" />
      </DetailsModalComponent>
    );
  }
}

export default withRouter(SearchModalComponent);
