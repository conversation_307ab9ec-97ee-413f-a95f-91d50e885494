/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import ReactTooltip from 'react-tooltip';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import InfiniteScroll from 'react-infinite-scroller';

import { searchStore } from '../../stores/Stores';
import SearchListHeadComponent from './SearchListHeadComponent';
import SearchListRowComponent from './SearchListRowComponent';
import StoreSubscription from '../../helpers/StoreSubscription';
import ListMessageRowComponent from '../shared/ListMessageRowComponent';
import companySearchDetailsOpen from '../../actions/actionCreators/CompanySearchDetailsOpen';
import { getGovOrgId } from '../../helpers/Company';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { routerPropType } from '../../helpers/RouterPropTypes';
/* eslint-enable max-len */

class SearchListComponent extends React.Component {

  static get propTypes() {
    return {
      narrow: PropTypes.bool,
      selectedItemId: PropTypes.string,
      router: routerPropType.isRequired
    };
  }

  static get defaultProps() {
    return {
      narrow: false,
    };
  }

  constructor(props) {
    super(props);
    this.searchSubscription = new StoreSubscription(searchStore, this.searchChanged.bind(this));
    const storeState = searchStore.getState();
    this.state = this.mapStoreToState(storeState);
  }

  componentDidMount() {
    this.searchSubscription.activate();
  }

  componentDidUpdate() {
    ReactTooltip.rebuild();
  }

  componentWillUnmount() {
    this.searchSubscription.deactivate();
  }

  mapStoreToState(storeState) {
    return {
      loaded: storeState.loaded,
      loading: storeState.loading,
      failed: storeState.failed,
      companies: storeState.companies,
      tooManyResults: storeState.tooManyResults,
      loadMore: storeState.loadMore,
    };
  }

  searchChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  rowClicked(company) {
    const govOrgId = getGovOrgId(company.country, company.gov_org_ids);
    companySearchDetailsOpen(company.country, govOrgId, this.props.router.navigate);
  }

  colSpan() {
    return this.props.narrow ? 1 : 4;
  }

  render() {
    const { narrow } = this.props;

    const sortedCompanies = [...this.state.companies].sort((a, b) => {
      const nameA = a.name.toUpperCase();
      const nameB = b.name.toUpperCase();

      if (a.is_reliable_partner && !b.is_reliable_partner) return -1;
      if (!a.is_reliable_partner && b.is_reliable_partner) return 1;

      if (nameA < nameB) return -1;
      if (nameA > nameB) return 1;
      return 0;
    });

    let rows = sortedCompanies.map(company => {
      const govOrgId = getGovOrgId(company.country, company.gov_org_ids);

      const companyId = `${company.country}/${govOrgId}`;
      const selected = companyId === this.props.selectedItemId;

      return (
        <SearchListRowComponent
          key={companyId}
          narrow={narrow}
          selected={selected}
          onClick={() => this.rowClicked(company)}
          company_resource_id={companyId}
          gov_org_id={govOrgId}
          {...company}
        />
      );
    });

    if (!rows.length) {
      let message = '';
      let className = '';

      if (this.state.loaded) {
        message = 'messageEmpty';
      } else if (this.state.loading) {
        message = 'messageLoading';
      } else if (this.state.failed) {
        className = 'text-warning';
        message = 'messageFailed';
      } else {
        message = 'messageInitial';
      }

      rows = (
        <ListMessageRowComponent message={message} className={className} colSpan={this.colSpan()} />
      );
    }

    return (
      <InfiniteScroll
        pageStart={0}
        loadMore={() => {}}
        hasMore={false}
        loader={
          <div className="loader" key={0}>
            <table>
              <tbody>
                <ListMessageRowComponent message="messageLoading" colSpan={this.colSpan()} />
              </tbody>
            </table>
          </div>
        }
      >
        {this.state.tooManyResults && (
          <div className="alert alert-warning">
            <FormattedMessage
              id="search.tooManyResults"
              description="Message shown when there is too many results"
              defaultMessage="Too many results, please narrow your search."
            />
          </div>
        )}

        <div className="company-container">
          <table
            className={classNames(
              'table',
              'table--bol',
              'table-striped',
              'table-striped--bol',
              'table-hover',
              'company-list',
              'table-clickable-rows--bol',
              // Note that more than one of these
              // can be active at the same time:
              // - loaded means the initial load succeeded (eventually)
              // - failed means the last load failed
              // - loading means a load is in progress
              {
                loaded: this.state.loaded,
                failed: this.state.failed,
                loading: this.state.loading,
              }
            )}
          >
            <thead>
              <SearchListHeadComponent narrow={narrow} />
            </thead>
            <tbody>{rows}</tbody>
          </table>
        </div>
      </InfiniteScroll>
    );
  }
}

export default withRouter(SearchListComponent);
