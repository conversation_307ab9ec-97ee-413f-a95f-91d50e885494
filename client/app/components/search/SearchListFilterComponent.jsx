import React from 'react';
//import { routerShape, locationShape, browserHistory } from 'react-router';
import { injectIntl, defineMessages } from 'react-intl';
import qs from 'query-string';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { searchStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import companySearch from '../../actions/actionCreators/CompanySearch';
import ListFilterOptionsComponent from '../shared/ListFilterOptionsComponent';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';
import { withRouter } from '../../helpers/RouterComponentWrappers';

//This one is tricky

const messages = defineMessages({
  compSearchPlaceholder: {
    id: 'companies.filter.compSearchPlaceholder',
    description: 'Company place holder for search filter field',
    defaultMessage: 'Company name or number',
  },
  countryTitleFI: {
    id: 'companies.filter.countryTitle.fi',
    description: 'Country title text for search filter (FI)',
    defaultMessage: 'Finnish',
  },
  countryTitleSE: {
    id: 'companies.filter.countryTitle.se',
    description: 'Country title text for search filter (SE)',
    defaultMessage: 'Swedish',
  },
  countryTitleEE: {
    id: 'companies.filter.countryTitle.ee',
    description: 'Country title text for search filter (EE)',
    defaultMessage: 'Estonian',
  },
  countryTitleLV: {
    id: 'companies.filter.countryTitle.lv',
    description: 'Country title text for search filter (LV)',
    defaultMessage: 'Latvia',
  },
  countryTitleLT: {
    id: 'companies.filter.countryTitle.lt',
    description: 'Country title text for search filter (LT)',
    defaultMessage: 'Lithuanian',
  },
  countryTitlePL: {
    id: 'companies.filter.countryTitle.pl',
    description: 'Country title text for search filter (PL)',
    defaultMessage: 'Poland',
  },
});

class SearchListFilterComponent extends React.Component {
  static get propTypes() {
    return {
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired,
    };
  }

  constructor(props) {
    super(props);

    this.searchSubscription = new StoreSubscription(searchStore, this.searchChanged.bind(this));
    const storeState = searchStore.getState();
    this.state = this.mapStoreToState(storeState);
  }

  componentDidMount() {
    this.searchSubscription.activate();

    /*
    this.backListener = browserHistory.listen(location => {
      if (location.action === 'POP') {
        // get current pathanme
        const { hash } = location;
        // get previous pathname
        const { pathname } = this.props.router.location;
        if (hash && pathname && pathname.slice(0, pathname.lastIndexOf('/')) === '/search/parent') {
          // BOL-2858: if parent company has been open before
          // update search results with current pathname parameters
          let country = hash.slice(hash.lastIndexOf('/') + 1, hash.lastIndexOf('%2F'));
          const freeText = hash.slice(hash.lastIndexOf('%2F') + 3);
          country = getCountryCodeAlpha2(country);
          this.setState({
            ...this.state,
            filter: {
              country: country || 'FI',
              freeText: freeText || '',
            },
          });
          companySearch(this.state.filter);
        }
      }
    });
    */

    const params = qs.parse(this.props.router.location.search);
    // eslint-disable-next-line react/no-did-mount-set-state
    this.setState({
      ...this.state,
      filter: {
        country: params.country || 'FI',
        freeText: params.free_text || '',
      },
    });
  }

  componentWillUnmount() {
    this.searchSubscription.deactivate();
    this.backListener();
  }

  mapStoreToState(storeState) {
    return {
      loaded: storeState.loaded,
      loading: storeState.loading,
      failed: storeState.failed,
      companies: storeState.companies,
      tooManyResults: storeState.tooManyResults,
      loadMore: storeState.loadMore,
      filter: storeState.filter,
      isQueryTooShort: storeState.isQueryTooShort,
    };
  }

  searchChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  _onFilter(event) {
    event.preventDefault();

    const { filter } = this.state;
    const { country, freeText } = filter;
    if (freeText.length < 2) {
      this.setState({ ...this.state, filter, isQueryTooShort: true });
      return false;
    }

    companySearch(filter);

    const params = qs.stringify({
      country,
      free_text: freeText,
    });

    this.props.router.push({
      pathname: '/search',
      search: `?${params}`,
    });

    return false;
  }

  _onSearchChange = event => {
    const { filter } = this.state;
    const { country } = filter;
    const freeText = event.target.value;
    filter.freeText = freeText;

    const params = qs.stringify({
      country,
      free_text: freeText,
    });

    this.props.router.replace({
      pathname: this.props.router.location.pathname,
      search: `?${params}`,
    });

    this.setState({ ...this.state, filter });
  };

  _onCountryChange = event => {
    const { filter } = this.state;
    const { freeText } = filter;
    const country = event.target.value;
    filter.country = country;

    const params = qs.stringify({
      country,
      free_text: freeText,
    });

    this.props.router.replace({
      pathname: '/search',
      search: `?${params}`,
    });

    if (freeText.length < 2) {
      this.setState({
        ...this.state,
        filter,
        isQueryTooShort: true,
      });
      return false;
    }

    companySearch(filter);
    return false;
  };

  _onClear(event) {
    event.preventDefault();

    this.props.router.push({
      pathname: '/search',
      search: '',
    });

    companySearch(searchStore.getInitialFilter());
  }

  render() {
    const { formatMessage } = this.props.intl;
    const supportedCountries = ['FI', 'SE', 'EE', 'LV', 'LT', 'PL'];
    const { isQueryTooShort } = this.state;
    const { filter } = this.state;

    return (
      <div className="card noborder list-filter-with-options vertical-compact">
        <div className="card card-default mb-0 list-filter">
          <div className="card-block filter-block">
            <form id="search_filter_form" onSubmit={this._onFilter.bind(this)}>
              <div className="row">
                <div className="col-md-6">
                  <div>
                    <label className="form-control-label form-control-label-sm">
                      <FormattedMessage
                        id="projects.filter.searchLabel"
                        description="Label for search input"
                        defaultMessage="Search"
                      />
                    </label>
                    <div className="form-group">
                      <div className="input-group">
                        <input
                          name="compsearch"
                          className="form-control"
                          placeholder={formatMessage(messages.compSearchPlaceholder)}
                          onChange={this._onSearchChange}
                          value={filter.freeText}
                          type="search"
                        />
                        <button
                          id="search_filter_button"
                          role="button"
                          type="submit"
                          className="input-group-addon text-primary bg-white"
                        >
                          <span className="fa fa-search" aria-hidden="true" />
                        </button>
                      </div>
                      <small
                        className={classNames('form-text', 'text-danger', {
                          'd-none': !isQueryTooShort,
                        })}
                      >
                        <FormattedMessage
                          id="companies.filter.queryTooShort"
                          description="Error message when query is too short"
                          defaultMessage="Please enter at least two characters."
                        />
                      </small>
                    </div>
                  </div>
                </div>
                <div className="col-md-3" />
                <div className="col-md-3">
                  <div>
                    <label className="form-control-label form-control-label-sm">
                      <FormattedMessage
                        id="companies.filter.countryLabel"
                        description="Label for country filter"
                        defaultMessage="Country"
                      />
                    </label>
                    <select
                      name="company_country"
                      className="form-control"
                      onChange={this._onCountryChange.bind(this)}
                      value={filter.country}
                      role="button"
                    >
                      {supportedCountries.map(c => (
                        <option key={c} value={c}>
                          {formatMessage(messages[`countryTitle${c}`])}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              <ListFilterOptionsComponent callback={this._onClear.bind(this)} />
            </form>
          </div>
        </div>
      </div>
    );
  }
}

export default withRouter(injectIntl(SearchListFilterComponent));
