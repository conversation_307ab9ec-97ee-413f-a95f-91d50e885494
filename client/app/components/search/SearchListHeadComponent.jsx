import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { defineMessages, injectIntl } from 'react-intl';

import FormattedMessage from '../../components/i18n/FormattedMessage'
import { preventDefault } from '../../helpers/EventHandlers';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  reliablePartner: {
    id: 'search.rpColumn',
    description: 'Search RP table column heading',
    defaultMessage: 'Reliable Partner',
  },
});

class SearchListHeadComponent extends React.Component {
  static get propTypes() {
    return {
      narrow: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  static get defaultProps() {
    return {
      narrow: false,
    };
  }

  render() {
    const { narrow } = this.props;
    const { formatMessage } = this.props.intl;
    return (
      <tr>
        <th>
          <a onClick={preventDefault} className="th-label">
            <FormattedMessage
              id="search.nameColumn"
              description="Search name table column heading"
              defaultMessage="Company"
            />
          </a>
        </th>
        <th className={classNames({ hidden: narrow })}>
          <a onClick={preventDefault} className="th-label">
            <FormattedMessage
              id="search.countryColumn"
              description="Search country table column heading"
              defaultMessage="Country"
            />
          </a>
        </th>
        <th>
          {narrow ? (
            <abbr title={formatMessage(messages.reliablePartner)}>
              <FormattedMessage
                id="search.rpColumn.short"
                description="Search RP table column heading short version"
                defaultMessage="RP"
              />
            </abbr>
          ) : (
            <FormattedMessage
              id="search.rpColumn"
              description="Search RP table column heading"
              defaultMessage="Reliable Partner"
            />
          )}
        </th>
        <th className={classNames({ hidden: narrow })}>
          <FormattedMessage
            id="search.ralaColumn"
            description="Search RALA table column heading"
            defaultMessage="RALA"
          />
        </th>
      </tr>
    );
  }
}

export default injectIntl(SearchListHeadComponent);
