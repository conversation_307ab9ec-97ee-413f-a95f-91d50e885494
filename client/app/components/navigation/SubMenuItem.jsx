import PropTypes from 'prop-types';
import React from 'react';
import { Link } from 'react-router-dom';
import classNames from 'classnames';

const SubMenuItem = props => {
  const cssClasses = classNames('nav-item', {
    icon: !!props.icon,
    active: props.active,
  });

  return (
    <li id={props.id} className={cssClasses}>
      <Link to={props.to} className="nav-link">
        {props.icon && <i className={classNames('fa', 'fa-mr', `fa-${props.icon}`)} />}
        {props.text}
      </Link>
    </li>
  );
};

SubMenuItem.propTypes = {
  id: PropTypes.string,
  active: PropTypes.bool,
  to: PropTypes.string,
  text: PropTypes.any,
  icon: PropTypes.string,
};

export default SubMenuItem;
