import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl, defineMessages } from 'react-intl';

import SubMenuItem from './SubMenuItem';
import SubMenuHomeItem from './SubMenuHomeItem';
import { featureActive } from '../../helpers/FeatureFlags';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  home: {
    id: 'submenu.home',
    description: 'Submenu home link text',
    defaultMessage: 'Bolagsfakta',
  },
  companies: {
    id: 'submenu.companies',
    description: 'Submenu companies link text',
    defaultMessage: 'Companies',
  },
  searchHistory: {
    id: 'submenu.searchHistory',
    description: 'Submenu Search history link text',
    defaultMessage: 'Search history',
  },
  search: {
    id: 'submenu.search',
    description: 'Submenu search link text',
    defaultMessage: 'Search',
  },
  projects: {
    id: 'submenu.projects',
    description: 'Submenu projects link text',
    defaultMessage: 'Projects',
  },
  contacts: {
    id: 'submenu.contacts',
    description: 'Submenu contacts link text',
    defaultMessage: 'Contacts',
  },
  subscription: {
    id: 'submenu.subscription',
    description: 'Submenu subscription link text',
    defaultMessage: 'Subscription',
  },
  help: {
    id: 'submenu.help',
    description: 'Submenu help link text',
    defaultMessage: 'Help',
  },
});

class SubMenu extends React.Component {
  static get propTypes() {
    return {
      route: PropTypes.string.isRequired,
      visible: PropTypes.bool,
      intl: intlPropType.isRequired
    };
  }

  render() {
    const { formatMessage } = this.props.intl;
    const visibility = this.props.visible ? 'visible' : 'hidden';
    const { route } = this.props;

    return (
      <nav
        className="stv-secondary-nav navbar navbar-toggleable-sm fixed-top"
        style={{ visibility }}
      >
        <button
          id="submenu_collapse_button"
          className="navbar-toggler navbar-toggler-right"
          type="button"
          data-toggle="collapse"
          data-target="#secondary-navbar"
          aria-controls="secondary-navbar"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          &#9776;
        </button>
        <a className="navbar-brand" href="#">
          &nbsp;
        </a>
        <div className="collapse navbar-collapse justify-content-between" id="secondary-navbar">
          <ul className="nav navbar-nav">
            <SubMenuHomeItem to="/" id="home_submenu" />
            {featureActive('projects') && (
              <SubMenuItem
                to="/projects"
                text={formatMessage(messages.projects)}
                id="projects_submenu"
                active={route.startsWith('/projects')}
              />
            )}
            {!featureActive('search') && (
              <SubMenuItem
                to="/companies"
                text={formatMessage(messages.companies)}
                id="companies_submenu"
                active={route.startsWith('/companies')}
              />
            )}
            {featureActive('search') && (
              <SubMenuItem
                to="/search"
                text={formatMessage(messages.search)}
                id="search_submenu"
                active={route.startsWith('/search')}
              />
            )}
            {featureActive('search') && (
              <SubMenuItem
                to="/companies"
                text={formatMessage(messages.searchHistory)}
                id="companies_submenu"
                active={route.startsWith('/companies')}
              />
            )}
          </ul>
          <ul className="nav navbar-nav">
            {featureActive('archived_reports') && (
              <SubMenuItem
                to="/subscription"
                text={formatMessage(messages.subscription)}
                id="subscription_submenu"
                active={route === '/subscription'}
              />
            )}
            {!featureActive('use_stv_theme') && (
              <SubMenuItem
                to="/contacts"
                text={formatMessage(messages.contacts)}
                id="contacts_submenu"
                active={route === '/contacts'}
              />
            )}
            <SubMenuItem
              to="/help"
              icon="question-circle"
              text={formatMessage(messages.help)}
              id="help_submenu"
              active={route === '/help'}
            />
          </ul>
        </div>
      </nav>
    );
  }
}

export default injectIntl(SubMenu);
