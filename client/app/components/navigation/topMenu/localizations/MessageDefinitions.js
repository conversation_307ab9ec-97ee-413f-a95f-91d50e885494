import { defineMessages } from 'react-intl';

const messageDefinitions = defineMessages({
  sp: {
    id: 'home',
    description: 'Home item',
    defaultMessage: 'Home'
  },
  taxnumber: {
    header: {
      id: 'taxnumber.header',
      description: 'Taxnumber header item',
      defaultMessage: 'Services'
    },
    report: {
      id: 'taxnumber.report',
      description: 'Taxnumber report item',
      defaultMessage: 'Reports'
    },
    register: {
      id: 'taxnumber.register',
      description: 'Building site register item',
      defaultMessage: 'Building site register'
    }
  },
  tilaajavastuu: {
    report: {
      id: 'tilaajavastuu.report',
      description: 'Tilaajavastuu report item',
      defaultMessage: 'Reports'
    },
    supervisor: {
      id: 'tilaajavastuu.supervisor',
      description: 'Tilaajavastuu supervisor item',
      defaultMessage: 'Supervisor'
    }
  },
  adminTools: {
    header: {
      id: 'adminTools.header',
      description: 'Admin tools header item',
      defaultMessage: 'Admin tools'
    },
    qvarnAccessTools: {
      id: 'adminTools.qvarnAccessTools',
      description: 'Qvan access tools item',
      defaultMessage: 'Qvarn Access Tool'
    }
  },
  competency: {
    id: 'competency',
    description: 'Competency item',
    defaultMessage: 'Competency register'
  },
  resellerManagementTool: {
    id: 'resellerManagementTool',
    description: 'Reseller Management Tool',
    defaultMessage: 'Reseller Management Tool'
  },
  qat: {
    id: 'qat',
    description: 'QAT item',
    defaultMessage: 'Qvarn Access Tool'
  },
  changeLanguage: {
    id: 'changeLanguage',
    description: 'Change language item',
    defaultMessage: 'Change language'
  },
  userDetails: {
    id: 'userDetails',
    description: 'User details item',
    defaultMessage: 'Your details'
  },
  changePassword: {
    id: 'changePassword',
    description: 'Change password item',
    defaultMessage: 'Change password'
  },
  changeEmail: {
    id: 'changeEmail',
    description: 'Change email item',
    defaultMessage: 'Change email'
  },
  signIn: {
    id: 'signIn',
    description: 'Sign in item',
    defaultMessage: 'Sign in'
  },
  signOut: {
    id: 'signOut',
    description: 'Sign out item',
    defaultMessage: 'Sign out'
  },
  organizationInfo: {
    id: 'organizationInfo',
    description: 'Organization info item',
    defaultMessage: 'Organisation info'
  },
  users: {
    id: 'users',
    description: 'Users item',
    defaultMessage: 'User management'
  },
  usersOverview: {
    id: 'usersOverview',
    description: 'Alternative link locale to user management if person only has read-only rights',
    defaultMessage: 'Users'
  },
  cards: {
    id: 'cards',
    description: 'Card item',
    defaultMessage: 'Card management'
  },
  serviceManagement: {
    id: 'serviceManagement',
    description: 'Service management',
    defaultMessage: 'Service management'
  },
  switchRepresentatedOrg: {
    id: 'switchRepresentatedOrg',
    description: 'Represented organizations item',
    defaultMessage: 'Switch organization account'
  },
  goToRepresentatedOrg: {
    id: 'goToRepresentatedOrg',
    description: 'Represented organizations item',
    defaultMessage: 'Go to organization account'
  },
  registerNewOrganization: {
    id: 'registerNewOrganization',
    description: 'Register a new organisation item',
    defaultMessage: 'Register a new organisation'
  },
  noCompanySelected: {
    id: 'noCompanySelected',
    description: 'No company selected text',
    defaultMessage: 'No company selected'
  },
  representingAsAdmin: {
    id: 'representingAsAdmin',
    description: 'Representing as service portal administrator',
    defaultMessage: 'Representing as service portal administrator'
  },
  showAllOrganizations: {
    id: 'showAllOrganizations',
    description: 'Show all organizations',
    defaultMessage: 'Show all {orgCount}'
  },
  showAllOrganizationsAsAdmin: {
    id: 'showAllOrganizationsAsAdmin',
    description: 'Show all organizations as admin',
    defaultMessage: 'Search all {orgCount}'
  },
  searchUsersGlobally: {
    id: 'searchUsersGlobally',
    description: 'Search all users globally',
    defaultMessage: 'Search users globally'
  },
  showAdminFindUsers: {
    id: 'showAdminFindUsers',
    description: 'Find all users',
    defaultMessage: 'Search all users'
  },
  showCpbOrganizations: {
    id: 'showCpbOrganizations',
    description: 'Show all CPB organizations',
    defaultMessage: 'Companies list'
  },
  showCpbFindUsers: {
    id: 'showCpbFindUsers',
    description: 'Find CPB users without company',
    defaultMessage: 'Find users without company'
  },
  whitelistedSignatories: {
    id: 'whitelistedSignatories',
    description: 'Whitelisted signatories',
    defaultMessage: 'Whitelisted signatories'
  },
  whitelistedOrganisations: {
    id: 'whitelistedOrganisations',
    description: 'Whitelisted organisations',
    defaultMessage: 'whitelistedOrganisations'
  },
  companiesWithoutFtax: {
    id: 'companiesWithoutFtax',
    description: 'Companies without ftax link',
    defaultMessage: 'Companies without F-tax'
  },
  companyRegistryManualManagement: {
    id: 'companyRegistryManualManagement',
    description: 'Manual Company registration',
    defaultMessage: 'Manual Company registration',
  },
  manageAllScanners: {
    id: 'manageAllScanners',
    description: 'Manage all scanners',
    defaultMessage: 'Manage all scanners'
  },
  manageScanners: {
    id: 'manageScanners',
    description: 'Manage scanners',
    defaultMessage: 'Manage scanners'
  },
  csvReports: {
    id: 'csvReports',
    description: 'CSV reports',
    defaultMessage: 'CSV reports'
  },
  resendAccountActivationLink: {
    id: 'resendAccountActivationLink',
    description: 'Resend account activation link',
    defaultMessage: 'Resend account activation link'
  },
  myCards: {
    id: 'myCards',
    description: 'Card holder cards',
    defaultMessage: 'My cards'
  },
  statusPaymentOverdue: {
    id: 'statusPaymentOverdue',
    description: 'Tooltip text for companies with status payment_overdue',
    defaultMessage: 'Status: Payment overdue'
  },
  statusTerminated: {
    id: 'statusTerminated',
    description: 'Tooltip text for companies with status terminated',
    defaultMessage: 'Status: Terminated'
  },
  invoicesLink: {
    id: 'invoicesLink',
    description: 'Link to organisation invoices page',
    defaultMessage: 'Invoices'
  },
  customerService: {
    id: 'customerService',
    description: 'Link to the ID06 customer service',
    defaultMessage: 'Customer service'
  }
});

export default messageDefinitions;
