#global-services-banner-message {
    padding: 12px;

    p {
        font-size: 18px;
        margin-bottom: 0;
    }
    .closeBtn {
        float: right;
        font-size: 1rem * 1.5;
        font-weight: bold;
        line-height: 1;
        text-shadow: 0 1px 0 #fff;
        color: #fff;
        text-shadow: none;
        opacity: 1;
        padding: 0 5px;
    }
    button.closeBtn {
        cursor: pointer;
        background: transparent;
        border: 0;
        -webkit-appearance: none;

        @include hover-focus {
            color: #fff;
            text-decoration: none;
            cursor: pointer;
            opacity: .75;
        }
    }
}

.stv-primary-nav.navbar {
    border-bottom: 1px solid $brand-primary;
    background-color: #fff;
    padding: 0;
    &.other-org {
        background: #fffadc;
    }
    .icon-brand-color{
        color:$brand-primary;
    }
    .selected-language-icon-hidden {
        display: none;
    }
    .fa-grey {
        color: #999;
    }
    .navbar-toggler {
        height: 51px;
        top: 0;
    }
    .navbar-brand {
        padding: 10px 0 10px 15px;
        height: 100%;
        > span {
            display: block;
            height: $navbar-brand-img-height;
            width: $navbar-brand-img-width;
            background-image: $navbar-brand-img;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    }
    .navbar-collapse {
        min-width: 0; /* Flex hack */
    }
    &:not(.minimal) {
        .dropdown {
            .dropdown-toggle {
                // padding-right: 33px !important;
                &:after {
                    margin-left: 15px;
                }
            }
            .dropdown-menu {
                margin: 1px 0 0;
                max-width: 600px;
                overflow: hidden;
                position: absolute;
                box-shadow: 0 6px 12px rgba(0,0,0,.175);
                .dropdown-header {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .dropdown-item {
                    &.with-icon {
                        padding-left: 45px;
                        position: relative;
                    }
                    & .representing-as-admin {
                        display: block;
                        font-size: 11px;
                        line-height: normal;
                        color: #818a91;

                    }
                    > i.fa {
                        position: absolute;
                        top: 50%;
                        left: 17px;
                        transform: translateY(-50%);
                    }
                }
            }
        }
    }
    .navbar-nav {
        @media(min-width: $min-width-md) {
            align-items: center;
        }
        .nav-item {
            margin: 0;
            a {
                color: #000;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                &:hover,
                &:focus {
                    background: #efefef;
                }
            }
            > a.nav-link {
                padding: 14px 12px 13px 12px;
                &.with-icon {
                    padding-left: 45px;
                    position: relative;
                }
                > i.fa {
                    position: absolute;
                    top: 50%;
                    left: 17px;
                    transform: translateY(-50%);
                }
            }
        }
    }
    .navbar-nav:last-child {
        flex-wrap: wrap;
        min-width: 0;
        width: 100%;
        justify-content: flex-end;
        > li:first-child {
            max-width: 60%;
        }
        > li:last-child {
            max-width: 40%;
        }
    }
    &:not(.navbar-toggleable-xl):not(.minimal) {
        .navbar-nav:last-child {
            > li:first-child {
                @media(max-width: $max-width-md) {
                    max-width: 100%;
                }
            }
            > li:last-child {
                @media(max-width: $max-width-md) {
                    max-width: 100%;
                }
            }
        }
        .dropdown {
            .dropdown-menu {
                @media(max-width: 767px) {
                    box-shadow: none;
                    position: relative;
                    border: 0;
                    border-radius: 0;
                    margin: 0 0 12px;
                    padding: 2px 0 2px;
                    width: 100%;
                    max-width: none;
                    float: none;
                }
            }
        }
    }
}
