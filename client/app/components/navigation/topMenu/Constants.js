/* eslint-disable max-len */
const constants = {
  pageLinkRelativeUrlOrgIdPlaceholder: '[ORG_ID]',
  pageLinkRelativeUrlSyncIdPlaceholder: '[SYNC_ID]',
  pageLinkRelativeUrlLangPlaceholder: '[LANG]',
  pageLinkRelativeUrls: {
    sp: {
      home: '/#/',
      organizationHome: '/#/[ORG_ID]/home'
    },
    taxnumber: {
      report: '/api/company/redirect/[SYNC_ID]?lang=[LANG]',
      register: '/trek/api/tokenAuth/redirectToCompany/[SYNC_ID]?lang=[LANG]'
    },
    tilaajavastuu: {
      report: '',
      supervisor: '/tilaajavastuu.fi/valvoja_redirect.jsf',
      adminSettings: '/tilaajavastuu.fi/maintenance/editcompany.jsf?companyid=[SYNC_ID]'
    },
    competency: {
      home: '/orgs/[ORG_ID]/competences'
    },
    qat: {
      home: '/'
    },
    publicWebsite: {
      home: '/#'
    },
    qatAccessTools:{
      home:''
    },
    resellerManagementTool: {
      home:''
    }

  },
  domainNamesForEnvironments: {
    live: {
      sp: 'portal.tilaajavastuu.fi',
      taxnumber: 'www.veronumero.fi',
      tilaajavastuu: 'raportit.tilaajavastuu.fi',
      competency: 'taito.tilaajavastuu.fi',
      qat: 'qat.tilaajavastuu.fi',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.tilaajavastuu.fi'
    },
    vaultitBeta: {
      sp: 'portal.beta.tilaajavastuu.fi',
      taxnumber: 'v-www.alpha.veronumero.fi',
      tilaajavastuu: 'v-raportit.alpha.tilaajavastuu.fi',
      competency: 'taito.beta.tilaajavastuu.fi',
      qat: 'qat.beta.tilaajavastuu.fi',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.beta.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://v-www.alpha.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.beta.tilaajavastuu.fi'
    },
    vaultitAlpha: {
      sp: 'portal.alpha.tilaajavastuu.fi',
      taxnumber: 'devel-www.veronumero.fi',
      tilaajavastuu: 'devel-raportit.tilaajavastuu.fi',
      competency: 'taito.alpha.tilaajavastuu.fi',
      qat: 'qat.alpha.tilaajavastuu.fi',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.alpha.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.alpha.tilaajavastuu.fi'
    },
    gamma: {
      sp: 'gamma-portal.tilaajavastuu.fi',
      taxnumber: 'www.veronumero.fi',
      tilaajavastuu: 'raportit.tilaajavastuu.fi',
      competency: 'taito.gamma.tilaajavastuu.fi',
      qat: 'qat.gamma.tilaajavastuu.fi',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.alpha.tilaajavastuu.fi'
    },
    beta: {
      sp: 'beta-portal.tilaajavastuu.fi',
      taxnumber: 'beta.veronumero.fi',
      tilaajavastuu: 'raportit.tilaajavastuu.fi',
      competency: 'taito.beta.tilaajavastuu.fi',
      qat: 'qat.beta.tilaajavastuu.fi',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.beta.tilaajavastuu.fi'
    },
    alpha: {
      sp: 'alpha-portal.tilaajavastuu.fi',
      taxnumber: 'devel-www.veronumero.fi',
      tilaajavastuu: 'raportit.tilaajavastuu.fi',
      competency: 'taito.alpha.tilaajavastuu.fi',
      qat: 'qat.alpha.tilaajavastuu.fi',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.alpha.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.alpha.tilaajavastuu.fi'
    },
    robot: {
      sp: 'robot-portal.tilaajavastuu.fi',
      taxnumber: 'devel-www.veronumero.fi',
      tilaajavastuu: 'raportit.tilaajavastuu.fi',
      competency: 'robot-taito-1.tilaajavastuu.io',
      qat: 'robot-qat-1.tilaajavastuu.io',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.alpha.tilaajavastuu.fi'
    },
    devrobot: {
      sp: 'devrobot-portal.tilaajavastuu.fi',
      taxnumber: 'devel-www.veronumero.fi',
      tilaajavastuu: 'raportit.tilaajavastuu.fi',
      competency: 'dev-taito-1.tilaajavastuu.io',
      qat: 'dev-taito-1.tilaajavastuu.io',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.alpha.tilaajavastuu.fi'
    },
    dev: {
      sp: 'localhost:8080',
      taxnumber: 'devel-www.veronumero.fi',
      tilaajavastuu: 'raportit.tilaajavastuu.fi',
      competency: 'taitowebappclient:3333',
      qat: 'qatclient:3333',
      publicWebsite: 'tilaajavastuu.fi',
      qatAccessTools:'qat.alpha.tilaajavastuu.fi',
      buildingSiteRegistryRegistrationLink: 'https://www.veronumero.fi/kayttooikeudet/#tyomaarekisteri',
      resellerManagementTool:'reseller.alpha.tilaajavastuu.fi'
    }
  },
  bannerBgColors: {
    red: '#ed4c5e',
    pink: '#fce9ee',
    purple: '#ebe6ff',
    green: '#e8fdf4',
    yellow: '#fffadc',
    beige: '#e8efce',
  },
  bannerFgColors: {
    red: '#fff',
    pink: '#d80027',
    purple: '#666',
    green: '#666',
    yellow: '#666',
    beige: '#666',
  }
};

export default constants;
