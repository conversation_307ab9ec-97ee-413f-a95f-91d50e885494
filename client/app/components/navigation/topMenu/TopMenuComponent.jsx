import React from 'react';
import PropTypes from 'prop-types';
import { IntlProvider } from 'react-intl';
import StateHelpers from './helpers/StateHelpers.js';
import ActionHelpers from './helpers/ActionHelpers.js';
import TopMenu from './components/TopMenu.jsx';

class TopMenuComponent extends React.Component {
  overWritableParameters = [
    'excludedLanguageCodes',
    'selectedRepresentationIndex',
    'allRepresentations',
    'allRepresentationsShowExclamationMarks',
    'allRepresentationsShowExclamationMarksTooltipKeys',
    'representationsOpenedInAdminMode',
    'configuration',
    'selectedRepresentedOrganizationId',
    'selectedRepresentedSyncId',
    'servicesVisibilityInTopMenu',
    'servicesEnabilityInTopMenu',
    'companyInfo',
    'previouslyVisitedOrganizationData',
    'topMenuLinks',
  ];

  getParameterNotDefined(parameter) {
    return typeof parameter === 'undefined';
  }

  getOrOverWrite(parameterName, defaultStore) {
    let overWriteStore = this.props.overWriteParameters;
    if (this.getParameterNotDefined(overWriteStore)) {
      overWriteStore = {};
    }
    let defaultValue = defaultStore[parameterName];
    let overWriteValue = overWriteStore[parameterName];

    return this.getParameterNotDefined(overWriteValue) ? defaultValue : overWriteValue;
  }

  checkOverWriteParameters() {
    for (let _prop in this.props.overWriteParameters) {
      if (!this.overWritableParameters.includes(_prop)) {
        console.error(
          `Parameter "${_prop}" is not overwritable. You can overwrite only these parameters: ${
            this.overWritableParameters
          }`
        );
      }
    }
  }

  render() {
    this.checkOverWriteParameters();

    let topMenuParameters = this.props.topMenuParameters;
    if (topMenuParameters === null) {
      topMenuParameters = {
        menuItemsVisibility: {
          openUsualFindCompanyPage: true,
        },
        topMenuLinks: {},
        configuration: {
          style: {},
          backToWebsiteLink: {},
          serviceProviderTopMenu: {
            anonymousLanguageSelector: {},
          },
          languages: [],
        },
      };
    } else if (this.getParameterNotDefined(topMenuParameters)) {
      console.error(
        'TopMenuComponent',
        'topMenuParameters must be an object or null (cannot be undefined)'
      );
    }
    this.getOrOverWrite('excludedLanguageCodes', topMenuParameters, this.props.overWriteParameters);
    let stateHelpers = new StateHelpers();
    let actionHelpers = new ActionHelpers(this.props);

    let authInfo = stateHelpers.getAuthInfo(this.props);
    let localizations = stateHelpers.getLocalizations(this.props);
    let urls = stateHelpers.getUrls(this.props);
    let currentPortalKey = stateHelpers.getCurrentPortalKey();
    let constants = stateHelpers.getConstants();

    let visibilitySettings = {
      allOrganizationsLinkData: topMenuParameters.allOrganizationsLinkData,
      hideHomeMenuLink: topMenuParameters.hideHomeMenuLink,
    };

    let meniuLinksVisibility = Object.assign(
      visibilitySettings,
      topMenuParameters.menuItemsVisibility
    );

    let dynamicProps = {};
    for (let i = 0; i < this.overWritableParameters.length; i++) {
      let _prop = this.overWritableParameters[i];
      dynamicProps[_prop] = this.getOrOverWrite(_prop, topMenuParameters);
    }
    return (
      <IntlProvider {...localizations}>
        <TopMenu
          {...dynamicProps}
          visibilitySettings={meniuLinksVisibility}
          authInfo={authInfo}
          urls={urls} // complicated
          currentPortalKey={currentPortalKey}
          actionHelpers={actionHelpers}
          constants={constants}
          onTopMenuItemClicked={this.props.onTopMenuItemClicked}
          selectedLanguageCode={this.props.selectedLanguageCode}
          serviceBreakBannerData={this.props.serviceBreakBannerData}
          selectedLanguage={localizations.messagesLanguage}
          openUsualFindCompanyPage={topMenuParameters.menuItemsVisibility.openUsualFindCompanyPage}
        >
          {this.props.children}
        </TopMenu>
      </IntlProvider>
    );
  }
}

TopMenuComponent.propTypes = {
  selectedLanguageCode: PropTypes.string.isRequired,
  onLanguageChanged: PropTypes.func.isRequired,
  onSignInClicked: PropTypes.func.isRequired,
  onSignOutClicked: PropTypes.func.isRequired,
  onSelectedRepresentationChanged: PropTypes.func,
  onTopMenuItemClicked: PropTypes.func,
  serviceBreakBannerData: PropTypes.object,
  topMenuParameters: PropTypes.object,
  overWriteParameters: PropTypes.object,
  children: PropTypes.node,
};

export default TopMenuComponent;
