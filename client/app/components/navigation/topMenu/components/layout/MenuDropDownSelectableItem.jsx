import React from 'react';
import messageDefinitions from '../../localizations/MessageDefinitions.js';
import { injectIntl } from 'react-intl';
import PropTypes from 'prop-types';


class MenuDropDownSelectableItem extends React.Component {
  render() {
    if (this.props.isHidden) {
      return null;
    }

    const { formatMessage } = this.props.intl;

    let iconClass = 'fa fa-check fa-grey selected-language-icon-hidden';

    if (this.props.isSelected) {
      iconClass = 'fa fa-check fa-grey';
    }

    let iconComponent = (
      <i id={this.props.id + '_checked'} className={iconClass} aria-hidden="true"></i>
    );

    if (this.props.showExclamationMark) {
      iconClass = 'fa fa-exclamation-circle fa-lg text-warning';
      iconComponent = (
        <i
          id={this.props.id + '_icon'}
          className={iconClass}
          aria-hidden="true"
          data-toggle="tooltip"
          data-placement="left"
          title={formatMessage(messageDefinitions[this.props.exclamationMarkTooltipKey]) || ''}
        ></i>
      );
    }

    return (
      <a
        role="button"
        id={this.props.id}
        className="dropdown-item with-icon"
        href={this.props.url}
        onClick={this.props.onClick}
        title={this.props.text}
      >
        {iconComponent}
        <span id={this.props.id + '_text'}>
          {this.props.text}
          {this.props.markedAsAdmin &&
          this.props.markedAsAdmin.length &&
          this.props.markedAsAdmin.indexOf(this.props.index) > -1 ? (
                <span className="representing-as-admin">
                  {formatMessage(messageDefinitions.representingAsAdmin)}
                </span>
              ) : null}
        </span>
      </a>
    );
  }
}

MenuDropDownSelectableItem.propTypes = {
  id: PropTypes.string.isRequired,
  isHidden: PropTypes.bool,
  isSelected: PropTypes.bool,
  showExclamationMark: PropTypes.bool,
  exclamationMarkTooltipKey: PropTypes.string,
  url: PropTypes.string,
  onClick: PropTypes.func,
  text: PropTypes.string.isRequired,
  markedAsAdmin: PropTypes.arrayOf(PropTypes.number),
  index: PropTypes.number,
  intl: PropTypes.object.isRequired,
};

export default injectIntl(MenuDropDownSelectableItem);
