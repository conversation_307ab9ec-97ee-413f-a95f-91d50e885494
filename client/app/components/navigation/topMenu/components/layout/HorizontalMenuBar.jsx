import React from 'react';
import PropTypes from 'prop-types';
import Logo from './Logo.jsx';
import $ from 'jquery';

class HorizontalMenuBar extends React.Component {
  static propTypes = {
    changeBgColor: PropTypes.any, //can be a number or a boolean
    miniamlStyle: PropTypes.bool,
    hideLogo: PropTypes.bool,
    orgId: PropTypes.string,
    configuration: PropTypes.shape({
      style: PropTypes.shape({
        topMenuToggleable: PropTypes.bool.isRequired,
      }).isRequired,
      thirdLevelMenu: PropTypes.bool
    }).isRequired,
    children: PropTypes.node,
  };

  render() {
    let className = 'navbar stv-primary-nav';
    let logoClassName = 'navbar-brand';

    className += this.props.changeBgColor ? ' other-org' : '';
    className += this.props.miniamlStyle ? ' minimal' : '';
    className += !this.props.configuration.style.topMenuToggleable
      ? ' navbar-toggleable-xl'
      : ' navbar-toggleable-sm';
    logoClassName += this.props.configuration.thirdLevelMenu ? ' hidden-sm-down' : '';
    return (
      <nav className={className}>
        {!this.props.hideLogo ? (
          <Logo
            id="top_menu_logo"
            orgId={this.props.orgId}
            configuration={this.props.configuration}
            className={logoClassName}
          />
        ) : null}
        {this.props.configuration.thirdLevelMenu && !this.props.miniamlStyle ? (
          <button
            id="top_menu_collapse_button"
            className="navbar-toggler third-level-nav-toggler"
            type="button"
            onClick={() => $('.stv-side-nav .navbar-collapse').toggleClass('show')}
          >
            &#9776;
          </button>
        ) : null}
        {this.props.configuration.style.topMenuToggleable ? (
          <button
            id="top_menu_collapse_button"
            className="navbar-toggler navbar-toggler-right"
            type="button"
            data-toggle="collapse"
            data-target="#collapsingNavbar"
            aria-controls="collapsingNavbar"
            aria-expanded="false"
            aria-label="Toggle navigation"
          >
            &#9776;
          </button>
        ) : null}
        <div className="collapse navbar-collapse" id="collapsingNavbar">
          {this.props.children}
        </div>
      </nav>
    );
  }
}

export default HorizontalMenuBar;
