import React from 'react';
import PropTypes from 'prop-types';


class Logo extends React.Component {
  render() {
    let href;

    if (this.props.configuration && this.props.configuration.services) {
      href = this.props.configuration.topLogoUrl;

      if (this.props.topLogoUrlInOrgContext) {
        href = this.props.topLogoUrlInOrgContext;
      }

      return (
        <a
          role="button"
          className={this.props.className}
          id={this.props.id}
          href={href}
        >
          <span />
        </a>
      );
    }
    else {
      return (
        <span className={this.props.className} />
      );
    }
  }
}

Logo.propTypes = {
  className: PropTypes.string.isRequired,
  id: PropTypes.string,
  configuration: PropTypes.shape({
    services: PropTypes.any,
    topLogoUrl: PropTypes.string,
  }),
  topLogoUrlInOrgContext: PropTypes.string,
};

export default Logo;
