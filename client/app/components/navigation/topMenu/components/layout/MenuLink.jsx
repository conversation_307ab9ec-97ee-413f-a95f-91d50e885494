import React from 'react';
import PropTypes from 'prop-types';

class MenuLink extends React.Component {
  render() {
    let className = 'nav-item';

    if (this.props.className) {
      className = className + ' ' + this.props.className;
    }

    return (
      <li id={this.props.id} className={className}>
        <a
          role="button"
          className={this.props.iconClass ? 'nav-link with-icon' : 'nav-link'}
          href={this.props.url}
          onClick={this.props.onClick}
          title={this.props.text}
        >
          {this.props.iconClass && !this.props.mediaSrc ? (
            <i className={this.props.iconClass}></i>
          ) : null}
          {this.props.mediaSrc ? (
            <div className="media align-items-center">
              <img src={this.props.mediaSrc} className="avatar" alt="avatar" />
              <div className="media-body hidden-sm-down">
                <span className="username">{this.props.text}</span>
              </div>
            </div>
          ) : (
            <span className="username">{this.props.text}</span>
          )}
        </a>
      </li>
    );
  }
}

MenuLink.propTypes = {
  id: PropTypes.string.isRequired,
  className: PropTypes.string,
  iconClass: PropTypes.string,
  url: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  text: PropTypes.string.isRequired,
  mediaSrc: PropTypes.string,
};

export default MenuLink;
