import React from 'react';
import PropTypes from 'prop-types';

class MenuItemsGroup extends React.Component {
  render() {
    let mainClassName = 'navbar-nav';
    return (
      <ul id={this.props.id} className={mainClassName}>
        {this.props.children}
      </ul>
    );
  }
}

MenuItemsGroup.propTypes = {
  id: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
};

export default MenuItemsGroup;
