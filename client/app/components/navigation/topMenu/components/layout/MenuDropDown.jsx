import React from 'react';
import messageDefinitions from '../../localizations/MessageDefinitions.js';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';

class MenuDropDown extends React.Component {
  render() {
    const { formatMessage } = this.props.intl;
    let iconComponent = null;
    let pushMenuToRight = '';

    if (this.props.iconClass) {
      iconComponent = (
        <i id={this.props.id + '_icon'} className={this.props.iconClass} aria-hidden="true"></i>
      );
    } else if (this.props.showExclamationMark) {
      iconComponent = (
        <i
          id={this.props.id + '_icon'}
          className={'fa fa-exclamation-circle fa-lg text-warning'}
          data-toggle="tooltip"
          data-placement="left"
          title={formatMessage(messageDefinitions[this.props.exclamationMarkTooltipKey])}
          aria-hidden="true"
        />
      );
    }

    if (this.props.dropdownMenuRight) {
      pushMenuToRight = ' dropdown-menu-right';
    }

    let propsChildrenNotEmpty = false;
    for (var index = 0; index < this.props.children.length; index++) {
      if (this.props.children[index] != null) {
        propsChildrenNotEmpty = true;
        break;
      }
    }

    let linkClassName = 'nav-link dropdown-toggle';

    let menuDropdown = propsChildrenNotEmpty ? (
      <li className="nav-item dropdown">
        <a
          id={this.props.id}
          className={linkClassName + (iconComponent ? ' with-icon' : '')}
          data-toggle="dropdown"
          role="button"
          aria-haspopup="true"
          aria-expanded="false"
          title={this.props.text}
        >
          {iconComponent}
          <span id={this.props.id + '_text'}>{this.props.text}</span>
        </a>
        <div className={'dropdown-menu' + pushMenuToRight} aria-labelledby="Preview">
          {this.props.children}
        </div>
      </li>
    ) : null;

    return menuDropdown;
  }
}

MenuDropDown.propTypes = {
  id: PropTypes.string.isRequired,
  iconClass: PropTypes.string,
  showExclamationMark: PropTypes.bool,
  exclamationMarkTooltipKey: PropTypes.string,
  dropdownMenuRight: PropTypes.bool,
  text: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  intl: PropTypes.object.isRequired,
};

export default injectIntl(MenuDropDown);
