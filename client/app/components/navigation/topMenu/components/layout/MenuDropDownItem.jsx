import React from 'react';
import PropTypes from 'prop-types';

class MenuDropDownItem extends React.Component {
  render() {
    let url = this.props.url;
    let iconComponent = null;
    let className = '';

    if (this.props.iconClass) {
      iconComponent = (
        <i id={this.props.id + '_icon'} className={this.props.iconClass} aria-hidden="true"></i>
      );
    }

    if (this.props.className) {
      className = ' ' + this.props.className;
    }

    if (this.props.disabled) {
      url = null;
      className = className + ' disabled text-muted';
    }

    return (
      <a
        role="button"
        id={this.props.id}
        className={'dropdown-item' + (iconComponent ? ' with-icon' : '') + className}
        href={url}
        onClick={this.props.onClick}
        title={this.props.text}
      >
        {iconComponent}
        {this.props.text}
      </a>
    );
  }
}

MenuDropDownItem.propTypes = {
  id: PropTypes.string.isRequired,
  url: PropTypes.string,
  iconClass: PropTypes.string,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
  text: PropTypes.string.isRequired,
};

export default MenuDropDownItem;
