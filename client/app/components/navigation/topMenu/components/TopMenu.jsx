import React from 'react';
import { injectIntl } from 'react-intl';
import $ from 'jquery';

import messageDefinitions from '../localizations/MessageDefinitions.js';

import HorizontalMenuBar from './layout/HorizontalMenuBar.jsx';
import MenuDropDown from './layout/MenuDropDown.jsx';
import MenuDropDownItem from './layout/MenuDropDownItem.jsx';
import MenuDropDownSelectableItem from './layout/MenuDropDownSelectableItem.jsx';
import MenuDropDownGroupTitle from './layout/MenuDropDownGroupTitle.jsx';
import MenuDropDownSeparator from './layout/MenuDropDownSeparator.jsx';
import MenuItemsGroup from './layout/MenuItemsGroup.jsx';
import MenuLink from './layout/MenuLink.jsx';
import StateHelpers from '../helpers/StateHelpers.js';
import Markdown from 'react-markdown';
import constants from '../Constants.js';
import PropTypes from 'prop-types';

class TopMenu extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      originalBodyPadding: 0,
    };
  }

  componentDidMount() {
    if (this.props?.serviceBreakBannerData?.message) {
      this.initializeNotificationBanner();
    }

    this.inputPrevElementFocusHack();
  }

  componentDidUpdate() {
    this.initializeNotificationBanner();
    this.inputPrevElementFocusHack();
  }

  initializeNotificationBanner() {
    let defaultBodyPadding = parseInt($('body').css('paddingTop'));
    this.notificationBannerActions(defaultBodyPadding);

    window.addEventListener('resize', () => {
      this.notificationBannerActions(defaultBodyPadding);
    });
  }

  notificationBannerActions(defaultBodyPadding) {
    let body = $('body');
    let topMenuWrapper = $('.top-menu-wrapper');

    if (defaultBodyPadding && !this.state.originalBodyPadding) {
      this.setState({ originalBodyPadding: defaultBodyPadding });
    }

    let padding = this.state.originalBodyPadding;
    padding = parseInt(topMenuWrapper.outerHeight());
    body.css('paddingTop', `${padding}px`);
  }

  handleOnServiceBrakeNotificationClose() {
    this.notificationBannerActions();
    this.props.serviceBreakBannerData.handleOnClose();
  }

  inputPrevElementFocusHack() {
    // Hack for triggering prev element when input has focus
    if (this.props.configuration.style.usedTheme === 'theme-3') {
      $('.input-group .form-control').on('focus', function () {
        $(this).prev('.input-group-addon').addClass('input-focused');
      });

      $('.input-group .form-control').on('focusout', function () {
        $(this).prev('.input-group-addon').removeClass('input-focused');
      });

      $('.input-group .form-control').each(function () {
        if ($(this).is(':focus')) {
          if (!$(this).prev('.input-group-addon').hasClass('input-focused')) {
            $(this).prev('.input-group-addon').addClass('input-focused');
          }
        }
      });
    }
  }

  doHideLanguageSelection(languageCode) {
    let excludedLanguageCodes = this.props.excludedLanguageCodes;
    return excludedLanguageCodes && excludedLanguageCodes.indexOf(languageCode) > -1;
  }

  handleOnTopMenuItemClicked(itemName, event = null) {
    if (this.props.onTopMenuItemClicked) {
      if (event) {
        event.preventDefault();
      }
      this.props.onTopMenuItemClicked(itemName);
    }
  }

  ensureBodyPadding() {
    if (
      this.props.configuration.style.usedTheme === 'theme-1' ||
      this.props.configuration.style.usedTheme === 'theme-2'
    ) {
      $('body').removeClass('padding-incl-secondary-nav');
      if (this.props.visibilitySettings.secondLevelNavIsVIsible) {
        $('body').addClass('padding-incl-secondary-nav');
      }
    }

    if (this.props.configuration.style.usedTheme === 'theme-3') {
      $('body').removeClass('minimal-padding');
      if (this.props.visibilitySettings.showMinimalTopMenuStyle) {
        $('body').addClass('minimal-padding');
      }
    }
  }

  render() {
    this.inputPrevElementFocusHack();
    this.ensureBodyPadding();
    let stateHelpers = new StateHelpers();

    const { formatMessage } = this.props.intl;

    let representationsExist =
      this.props.allRepresentations && this.props.allRepresentations.length;

    let isLoggedIn = this.props.authInfo.user;
    let backToWebsiteLink = this.props.configuration.backToWebsiteLink;
    let backToWebsiteText = backToWebsiteLink.title
      ? backToWebsiteLink.title[this.props.selectedLanguage]
      : null;
    let backToWebsiteUrl = backToWebsiteLink.url;
    let topLogoUrlInOrgContext = null;

    if (
      this.props.configuration.topLogoUrlInOrgContext &&
      this.props.selectedRepresentedOrganizationId
    ) {
      topLogoUrlInOrgContext = stateHelpers.getFixedUrl(
        this.props.configuration.topLogoUrlInOrgContext,
        this.props
      );
    }

    let serviceBreakBanner = null;

    if (this.props.serviceBreakBannerData && this.props.serviceBreakBannerData.message) {
      // Default banner color is red
      const bannerBgColor =
        constants.bannerBgColors[this.props.serviceBreakBannerData.color] ||
        constants.bannerBgColors['red'];
      const bannerFgColor =
        constants.bannerFgColors[this.props.serviceBreakBannerData.color] ||
        constants.bannerFgColors['red'];
      serviceBreakBanner = (
        <div
          className="text-center"
          id="global-services-banner-message"
          style={{ background: bannerBgColor, color: bannerFgColor }}
        >
          {this.props.serviceBreakBannerData.handleOnClose ? (
            <button
              onClick={() => this.handleOnServiceBrakeNotificationClose()}
              type="button"
              className="closeBtn"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          ) : null}
          <div>
            <Markdown source={this.props.serviceBreakBannerData.message} />
          </div>
        </div>
      );
    }

    let backLink = this.props.visibilitySettings.showAnonymousUserBackLink ? (
      <MenuLink
        id="top_menu_back_to_public_link"
        text={backToWebsiteText}
        url={backToWebsiteUrl}
        className="back-to-home-btn"
      />
    ) : null;

    let anonymousLanguageSelector =
      this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector;
    let selectedLanguageText = anonymousLanguageSelector.text
      ? anonymousLanguageSelector.text[this.props.selectedLanguage]
      : null;

    let resendAccountActivationAfterManualIDLink = this.props.selectedRepresentedOrganizationId
      ? this.props.topMenuLinks.resendAccountActivationLinkInOrgContext
      : this.props.topMenuLinks.resendAccountActivationLink;

    let languageSelector = (
      <MenuDropDown
        id="top_menu_lang_dropdown"
        text={selectedLanguageText}
        dropdownMenuRight={anonymousLanguageSelector.position === 'right'}
      >
        {this.props.configuration.languages.map(language => {
          let items = [];
          items.push(
            <MenuDropDownSelectableItem
              key={'top_menu_lang_' + language}
              id={'top_menu_lang_' + language}
              text={
                this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector.text[
                  language
                ]
              }
              isSelected={this.props.selectedLanguage === language}
              onClick={this.props.actionHelpers.getLanguagesOnClickHandler(language)}
              isHidden={this.doHideLanguageSelection(language)}
            />
          );
          return items;
        })}
      </MenuDropDown>
    );

    if (isLoggedIn) {
      let userFirstNameLine = this.props.authInfo.user.firstName;
      let userLastNameLine = this.props.authInfo.user.lastName;

      let profileWithPhotoLink = this.props.configuration.serviceProviderTopMenu
        .showProfileWithPhotoLink ? (
            <MenuLink
              id="top_menu_profile_with_photo_link"
              text={userFirstNameLine + ' ' + userLastNameLine}
              url={this.props.topMenuLinks.userDetails}
              className="profile-with-photo-btn"
              mediaSrc={this.props.configuration.serviceProviderTopMenu.profilePhotoLink}
            />
          ) : null;

      let userInfoLine = this.props.authInfo.user.fullName + ' - ' + this.props.authInfo.user.email;

      let userProfileLinks = this.props.visibilitySettings.showSignedInUserProfileLinks ? (
        <div>
          <MenuDropDownSeparator id="top_menu_user_info_seperator" />

          <MenuDropDownItem
            onClick={() => this.handleOnTopMenuItemClicked('top_menu_user_info_details')}
            id="top_menu_user_info_details"
            text={formatMessage(messageDefinitions.userDetails)}
            url={this.props.topMenuLinks.userDetails}
          />

          {this.props.visibilitySettings.showChangeEmailLink ? (
            <MenuDropDownItem
              onClick={() => this.handleOnTopMenuItemClicked('top_menu_user_info_change_email')}
              id="top_menu_user_info_change_email"
              text={formatMessage(messageDefinitions.changeEmail)}
              url={this.props.topMenuLinks.changeEmail}
            />
          ) : null}

          {this.props.visibilitySettings.showResetPasswordLink ? (
            <MenuDropDownItem
              onClick={() => this.handleOnTopMenuItemClicked('top_menu_user_info_change_password')}
              id="top_menu_user_info_change_password"
              text={formatMessage(messageDefinitions.changePassword)}
              url={this.props.topMenuLinks.changePassword}
            />
          ) : null}
        </div>
      ) : null;

      let languageSelectorInDropdown = this.props.visibilitySettings
        .showSignedInUserLanguageSelector ? (
            <div>
              <MenuDropDownSeparator id="top_menu_user_change_language_seperator" />
              <MenuDropDownGroupTitle
                id="top_menu_user_change_language_header"
                text={formatMessage(messageDefinitions.changeLanguage)}
              />

              {this.props.configuration.languages.map(language => {
                let items = [];
                items.push(
                  <MenuDropDownSelectableItem
                    key={'top_menu_user_info_lang_' + language}
                    id={'top_menu_user_info_lang_' + language}
                    text={
                      this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector
                        .text[language]
                    }
                    isSelected={this.props.selectedLanguage === language}
                    onClick={this.props.actionHelpers.getLanguagesOnClickHandler(language)}
                    isHidden={this.doHideLanguageSelection(language)}
                  />
                );
                return items;
              })}
            </div>
          ) : null;

      let servicesMenu = null;
      let separatorId = '';
      let findOrgUrl = this.props.topMenuLinks.registerNewOrganization;

      if (
        (this.props.visibilitySettings.showSignedInUserEnviromentSelector === true ||
          typeof this.props.visibilitySettings.showSignedInUserEnviromentSelector ===
            'undefined') &&
        this.props.configuration.serviceProviderTopMenu.alwaysHideSignedInUserEnviromentSelector ===
          false
      ) {
        servicesMenu = (
          <MenuDropDown
            id="top_menu_enviroment_dropdown"
            text={
              this.props.configuration.services[this.props.configuration.currentService].title[
                this.props.selectedLanguage
              ]
            }
          >
            {this.props.configuration.servicesMenu.map((serviceGroup, groupIdx) => {
              let items = [];

              for (const service in serviceGroup.services) {
                if (this.props.servicesVisibilityInTopMenu[service]) {
                  let url = this.props.configuration.services[service].url;
                  let serviceHasException = false;

                  if (
                    this.props.configuration.services[service].relativePages &&
                    this.props.configuration.services[service].relativePages.organizationHome &&
                    this.props.selectedRepresentedOrganizationId
                  ) {
                    url +=
                      this.props.configuration.services[service].relativePages.organizationHome;
                  }

                  if (serviceGroup.services[service].url)
                    if (serviceGroup.services[service].url[0] === '/')
                      url += serviceGroup.services[service].url;
                    else url = serviceGroup.services[service].url;

                  if (
                    this.props.configuration.services[service] &&
                    this.props.configuration.services[service].exceptional_behaviour_in_top_menu
                  ) {
                    serviceHasException = true;
                  }

                  let text =
                    this.props.configuration.services[service].title[this.props.selectedLanguage];
                  if (serviceGroup.services[service].customTitleForTopMenu) {
                    text =
                      serviceGroup.services[service].customTitleForTopMenu[
                        this.props.selectedLanguage
                      ];
                  }

                  if (serviceHasException) {
                    items.push(
                      <MenuDropDownItem
                        key={serviceGroup.services[service].id}
                        id={serviceGroup.services[service].id}
                        text={text}
                        url={stateHelpers.getFixedUrl(url, this.props)}
                        onClick={e => this.handleOnTopMenuItemClicked(service, e)}
                        disabled={!this.props.servicesEnabilityInTopMenu[service]}
                      />
                    );
                  } else {
                    items.push(
                      <MenuDropDownItem
                        onClick={() =>
                          this.handleOnTopMenuItemClicked(serviceGroup.services[service].id)
                        }
                        key={serviceGroup.services[service].id}
                        id={serviceGroup.services[service].id}
                        text={text}
                        url={stateHelpers.getFixedUrl(url, this.props)}
                        disabled={!this.props.servicesEnabilityInTopMenu[service]}
                      />
                    );
                  }
                }
              }

              if (serviceGroup.title && items.length) {
                let nameForElementId = serviceGroup.title[this.props.selectedLanguage]
                  .replace(' ', '_')
                  .toLowerCase();
                let menuGroupId = 'menu_group_' + nameForElementId;
                separatorId = 'separator_of_' + nameForElementId;
                items.splice(
                  0,
                  0,
                  <MenuDropDownGroupTitle
                    key={menuGroupId}
                    text={serviceGroup.title[this.props.selectedLanguage]}
                    id={menuGroupId}
                  />
                );
              }

              if (groupIdx !== 0 && items.length) {
                items.splice(0, 0, <MenuDropDownSeparator key={separatorId} id={separatorId} />);
                separatorId = '';
              }

              return items;
            })}
          </MenuDropDown>
        );
      }

      let myCardsMenuOption = null;
      let customerServiceLink = null;
      let organizationMenu = null;
      if (
        this.props.visibilitySettings.showSignedInUserOrganizationMenu &&
        this.props.configuration.serviceProviderTopMenu.alwaysHideSignedInUserOrganizationMenu ===
          false
      ) {
        let signedInUserInfoAndManagementLinks = null;

        if (
          this.props.visibilitySettings.showSignedInUserOrganizationInfoLink ||
          this.props.visibilitySettings.showSignedInUserManageUsersLink ||
          this.props.visibilitySettings.showSignedInUserManageCardsLink ||
          this.props.visibilitySettings.showServiceManagementLink
        ) {
          signedInUserInfoAndManagementLinks = (
            <div>
              {this.props.visibilitySettings.showSignedInUserOrganizationInfoLink ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_org_info_org_info')}
                  id="top_menu_org_info_org_info"
                  text={formatMessage(messageDefinitions.organizationInfo)}
                  url={this.props.topMenuLinks.orgInfoLink}
                  iconClass="fa fa-list-alt icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.orgInvoicesLink ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_org_invoicing_link')}
                  id="top_menu_org_invoicing_link"
                  text={formatMessage(messageDefinitions.invoicesLink)}
                  url={this.props.topMenuLinks.invoicingWebLink}
                  iconClass="fa fa-file-text-o icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.showSignedInUserManageUsersLink ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_org_info_users')}
                  id="top_menu_org_info_users"
                  text={formatMessage(messageDefinitions.users)}
                  url={this.props.topMenuLinks.users}
                  iconClass="fa fa-user icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.showSignedInUserViewUsersLink ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_org_info_users')}
                  id="top_menu_org_info_users"
                  text={formatMessage(messageDefinitions.usersOverview)}
                  url={this.props.topMenuLinks.users}
                  iconClass="fa fa-user icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.showServiceManagementLink ? (
                <MenuDropDownItem
                  id="top_menu_service_management"
                  text={formatMessage(messageDefinitions.serviceManagement)}
                  url={this.props.topMenuLinks.serviceManagement}
                  iconClass="fa fa-check icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.showSignedInUserManageCardsLink ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_org_info_cards')}
                  id="top_menu_org_info_cards"
                  text={formatMessage(messageDefinitions.cards)}
                  url={this.props.topMenuLinks.companyCardsManagementLink}
                  iconClass="fa fa-credit-card icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.showCpbOrganizations ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_cpb_organizations')}
                  id="top_menu_cpb_organizations"
                  text={formatMessage(messageDefinitions.showCpbOrganizations)}
                  url={this.props.topMenuLinks.cpbCustomersLink}
                  iconClass="fa fa-book icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.showCpbFindUsers ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_cpb_find_users')}
                  id="top_menu_cpb_find_users"
                  text={formatMessage(messageDefinitions.showCpbFindUsers)}
                  url={this.props.topMenuLinks.findUsersCpb}
                  iconClass="fa fa-bars icon-brand-color"
                />
              ) : null}

              {this.props.visibilitySettings.showManageOrganisationScannersLink ? (
                <MenuDropDownItem
                  onClick={() =>
                    this.handleOnTopMenuItemClicked(
                      'top_menu_show_manage_organization_scanners_link'
                    )
                  }
                  id="top_menu_show_manage_organization_scanners_link"
                  text={formatMessage(messageDefinitions.manageScanners)}
                  url={this.props.topMenuLinks.manageOrgScannerLink}
                  iconClass="fa fa-barcode icon-brand-color"
                />
              ) : null}

              <MenuDropDownSeparator id="top_menu_org_info_seperator" />
            </div>
          );
        }

        let representations = null;
        let newOrganization = null;
        let showAllOrganizations = null;
        let searchUsersGlobally = null;
        let showGovCompanySignatoriesPage = null;
        let showWhitelistOrganisationsPage = null;
        let showCompaniesWithoutFTaxPage = null;
        let showCompanyRegistryManualManagementPage = null;
        let showResendAccountActivationLink = null;
        let showManageScannersPage = null;
        let showCsvReportsPage = null;
        let showAdminFindUsers = null;
        let currentRepresentationText = null;
        let representationsHeader = null;

        currentRepresentationText =
          this.props.selectedRepresentationIndex !== null
            ? this.props.allRepresentations[this.props.selectedRepresentationIndex]
            : formatMessage(messageDefinitions.noCompanySelected);

        let showCurrentRepresentationExclamationMark = false;
        let currentRepresentationExclamationMarkText = '';

        if (this.props.selectedRepresentationIndex !== null) {
          if (
            this.props.allRepresentationsShowExclamationMarks &&
            this.props.allRepresentationsShowExclamationMarks[
              this.props.selectedRepresentationIndex
            ]
          ) {
            showCurrentRepresentationExclamationMark = true;
          }
        }

        if (showCurrentRepresentationExclamationMark) {
          if (this.props.allRepresentationsShowExclamationMarksTooltipKeys) {
            currentRepresentationExclamationMarkText =
              this.props.allRepresentationsShowExclamationMarksTooltipKeys[
                this.props.selectedRepresentationIndex
              ];
          }
        }

        if (representationsExist) {
          let nowRepresentingTheOnlyRepresentation =
            this.props.allRepresentations.length === 1 &&
            this.props.selectedRepresentationIndex !== null;

          let shouldHideSingleItem =
            this.props.visibilitySettings.hideIfCurrentlyRepresentingOnlyRepresentation &&
            nowRepresentingTheOnlyRepresentation;

          if (!shouldHideSingleItem) {
            representations = this.props.allRepresentations.map((item, index) => {
              let showRepresentationExclamationMark = false;
              let representationExclamationMarkText = '';

              if (index !== null) {
                if (
                  this.props.allRepresentationsShowExclamationMarks &&
                  this.props.allRepresentationsShowExclamationMarks[index]
                ) {
                  showRepresentationExclamationMark = true;
                }
              }

              if (showRepresentationExclamationMark) {
                if (this.props.allRepresentationsShowExclamationMarksTooltipKeys) {
                  representationExclamationMarkText =
                    this.props.allRepresentationsShowExclamationMarksTooltipKeys[index];
                }
              }
              return (
                <MenuDropDownSelectableItem
                  id={'top_menu_represented_org_' + index}
                  index={index}
                  key={index}
                  text={item}
                  markedAsAdmin={this.props.representationsOpenedInAdminMode}
                  isSelected={index === this.props.selectedRepresentationIndex}
                  onClick={this.props.actionHelpers.getRepresentationClickHandler(index)}
                  showExclamationMark={showRepresentationExclamationMark}
                  exclamationMarkTooltipKey={representationExclamationMarkText}
                />
              );
            });
          }
        }

        representationsHeader =
          representations && representations.length ? (
            <MenuDropDownGroupTitle
              id="top_menu_represented_orgs_header"
              text={formatMessage(
                this.props.selectedRepresentationIndex !== null
                  ? messageDefinitions.switchRepresentatedOrg
                  : messageDefinitions.goToRepresentatedOrg
              )}
            />
          ) : null;

        newOrganization = this.props.visibilitySettings.hideRegisterNewOrganizationLink ? null : (
          <MenuDropDownItem
            onClick={() => this.handleOnTopMenuItemClicked('top_menu_org_info_new_organization')}
            id="top_menu_org_info_new_organization"
            text={formatMessage(messageDefinitions.registerNewOrganization)}
            url={findOrgUrl}
            iconClass="fa fa-plus icon-brand-color"
          />
        );

        var showAllValuesObject = {
          orgCount: null,
        };

        if (
          this.props.visibilitySettings.allOrganizationsLinkData &&
          this.props.visibilitySettings.allOrganizationsLinkData.count
        ) {
          showAllValuesObject.orgCount =
            '(' + this.props.visibilitySettings.allOrganizationsLinkData.count + ')';
        }

        showAllOrganizations =
          this.props.visibilitySettings.allOrganizationsLinkData &&
          this.props.visibilitySettings.allOrganizationsLinkData.showLink ? (
                <MenuDropDownItem
                  onClick={() => this.handleOnTopMenuItemClicked('top_menu_show_all_organizations')}
                  id="top_menu_show_all_organizations"
                  text={formatMessage(
                    this.props.visibilitySettings.allOrganizationsLinkData.isAdmin ||
                      this.props.visibilitySettings.allOrganizationsLinkData.isReadOnlyAdmin
                      ? messageDefinitions.showAllOrganizationsAsAdmin
                      : messageDefinitions.showAllOrganizations,
                    showAllValuesObject
                  )}
                  url={this.props.topMenuLinks.searchOrganisationsLink}
                  iconClass="fa fa-bars icon-brand-color"
                  className={representations ? 'mt-3' : ''}
                />
              ) : null;

        searchUsersGlobally = this.props.visibilitySettings.showUserGlobalSearch ? (
          <MenuDropDownItem
            id="top_menu_show_all_global_users"
            text={formatMessage(messageDefinitions.searchUsersGlobally)}
            url={this.props.topMenuLinks.searchUsersGlobally}
            iconClass="fa fa-users icon-brand-color"
            className={representations ? 'mt-3' : ''}
          />
        ) : null;

        showGovCompanySignatoriesPage =
          this.props.visibilitySettings.showWhitelistedSignatoriesLink &&
          this.props.openUsualFindCompanyPage == false ? (
                <MenuDropDownItem
                  onClick={() =>
                    this.handleOnTopMenuItemClicked('top_menu_show_gov_companies_signatories_list')
                  }
                  id="top_menu_show_gov_companies_signatories_list"
                  text={formatMessage(messageDefinitions.whitelistedSignatories)}
                  url={this.props.topMenuLinks.whitelistSignatoriesLink}
                  iconClass="fa fa-user-plus icon-brand-color"
                />
              ) : null;

        showAdminFindUsers = this.props.visibilitySettings.showAdminFindAllUsers ? (
          <MenuDropDownItem
            id="top_menu_find_users_without_company"
            text={formatMessage(messageDefinitions.showAdminFindUsers)}
            url={this.props.topMenuLinks.findUsers}
            iconClass="fa fa-search icon-brand-color"
          />
        ) : null;

        showWhitelistOrganisationsPage = this.props.visibilitySettings
          .showWhitelistOrganisationsLink ? (
              <MenuDropDownItem
                onClick={() =>
                  this.handleOnTopMenuItemClicked('top_menu_show_whitelist_organisations_list')
                }
                id="top_menu_show_whitelist_organisations_list"
                text={formatMessage(messageDefinitions.whitelistedOrganisations)}
                url={this.props.topMenuLinks.whitelistOrganisationsLink}
                iconClass="fa fa-list-alt icon-brand-color"
              />
            ) : null;

        showCompaniesWithoutFTaxPage = this.props.visibilitySettings
          .showCompaniesWithoutFTaxLink ? (
              <MenuDropDownItem
                onClick={() => 
                  this.handleOnTopMenuItemClicked('top_menu_show_companies_without_ftax')
                }
                id="top_menu_show_companies_without_ftax"
                text={formatMessage(messageDefinitions.companiesWithoutFtax)}
                url={this.props.topMenuLinks.companiesWithoutFtaxLink}
                iconClass="fa fa-list-alt icon-brand-color"
              />
            ) : null;

        showCompanyRegistryManualManagementPage =
          this.props.visibilitySettings.showManualCompaniesRegistryManagementLink &&
          this.props.openUsualFindCompanyPage == false ? (
                <MenuDropDownItem
                  onClick={() =>
                    this.handleOnTopMenuItemClicked(
                      'top_menu_show_company_registry_manual_management'
                    )
                  }
                  id="top_menu_show_company_registry_manual_management"
                  text={formatMessage(messageDefinitions.companyRegistryManualManagement)}
                  url={this.props.topMenuLinks.manualOrgEnrolmentLink}
                  iconClass="fa fa-edit icon-brand-color"
                />
              ) : null;

        showManageScannersPage = this.props.visibilitySettings.showManageScannersLink ? (
          <MenuDropDownItem
            onClick={() =>
              this.handleOnTopMenuItemClicked('top_menu_show_manage_all_scanners_link')
            }
            id="top_menu_show_manage_all_scanners_link"
            text={formatMessage(messageDefinitions.manageAllScanners)}
            url={this.props.topMenuLinks.scannersManagement}
            iconClass="fa fa-cube icon-brand-color"
          />
        ) : null;

        showCsvReportsPage = this.props.visibilitySettings.showCsvReportsLink ? (
          <MenuDropDownItem
            onClick={() => this.handleOnTopMenuItemClicked('top_menu_show_csv_reports_link')}
            id="top_menu_show_csv_reports_link"
            text={formatMessage(messageDefinitions.csvReports)}
            url={this.props.topMenuLinks.csvReports}
            iconClass="fa fa-list-alt icon-brand-color"
          />
        ) : null;

        showResendAccountActivationLink = this.props.visibilitySettings
          .showResendAccountActivationLink ? (
              <MenuDropDownItem
                onClick={() =>
                  this.handleOnTopMenuItemClicked('top_menu_show_resend_account_activation_link')
                }
                id="top_menu_show_resend_account_activation_link"
                text={formatMessage(messageDefinitions.resendAccountActivationLink)}
                url={resendAccountActivationAfterManualIDLink}
                iconClass="fa far fa-envelope icon-brand-color"
              />
            ) : null;

        myCardsMenuOption = (
          <li className="nav-item">
            <MenuDropDownItem
              onClick={() => this.handleOnTopMenuItemClicked('top_menu_my_cards')}
              id="top_menu_my_cards"
              text={formatMessage(messageDefinitions.myCards)}
              iconClass="fa fa-credit-card icon-brand-color"
              url={this.props.topMenuLinks.myCardsLink}
              className="nav-link"
            />
          </li>
        );

        customerServiceLink = (
          <li className="nav-item">
            <a
              id="id06-customer-service-link"
              href={this.props.topMenuLinks.customerServiceLink}
              target="_blank"
              rel="noreferrer"
            >
              {formatMessage(messageDefinitions.customerService)}
            </a>
          </li>
        );
        const selectedCompanyId = this.props.selectedRepresentedOrganizationId;
        const companyIndex = this.props.companyInfo.findIndex(
          company => company.organizationId == selectedCompanyId
        );
        let companyShortenedName = null;

        if (selectedCompanyId !== null && companyIndex > -1) {
          companyShortenedName = this.props.companyInfo[companyIndex].organizationShortName;
        }

        let orgNameToDisplay = companyShortenedName
          ? companyShortenedName
          : currentRepresentationText;

        organizationMenu = (
          <MenuDropDown
            id="top_menu_org_dropdown"
            text={orgNameToDisplay}
            dropdownMenuRight={true}
            showExclamationMark={showCurrentRepresentationExclamationMark}
            exclamationMarkTooltipKey={currentRepresentationExclamationMarkText}
          >
            {signedInUserInfoAndManagementLinks}
            {representationsHeader}
            {representations}
            {showAllOrganizations}
            {searchUsersGlobally}
            {showGovCompanySignatoriesPage}
            {showWhitelistOrganisationsPage}
            {showCompaniesWithoutFTaxPage}
            {showCompanyRegistryManualManagementPage}
            {showResendAccountActivationLink}
            {showManageScannersPage}
            {showCsvReportsPage}
            {showAdminFindUsers}
            {newOrganization && (representations || showAllOrganizations) ? (
              <MenuDropDownSeparator id="top_menu_org_info_new_organization_seperator" />
            ) : null}
            {newOrganization}
          </MenuDropDown>
        );
      }

      let changeBgColor =
        this.props.representationsOpenedInAdminMode &&
        this.props.representationsOpenedInAdminMode.length &&
        this.props.representationsOpenedInAdminMode.indexOf(
          this.props.selectedRepresentationIndex
        ) > -1;

      let menuToReturn = null;

      if (!this.props.visibilitySettings.showMinimalTopMenuStyle) {
        menuToReturn = (
          <HorizontalMenuBar
            orgId={this.props.selectedRepresentedOrganizationId}
            topLogoUrlInOrgContext={topLogoUrlInOrgContext}
            configuration={this.props.configuration}
            changeBgColor={changeBgColor}
          >
            <MenuItemsGroup id="top_menu_signed_in_left_group" alignLeft={true}>
              {servicesMenu}
              {this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector
                .position === 'left' &&
              this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector.alwaysShow
                ? languageSelector
                : null}
              {customerServiceLink}
            </MenuItemsGroup>
            <MenuItemsGroup id="top_menu_signed_in_right_group" alignRight={true}>
              {this.props.visibilitySettings.myCardsLinkIsVisible ? myCardsMenuOption : null}
              {organizationMenu}
              {this.props.configuration.serviceProviderTopMenu.showSignedInUserMenu ? (
                <MenuDropDown
                  id="top_menu_user_dropdown"
                  iconClass="fa fa-user"
                  text={userFirstNameLine}
                  dropdownMenuRight={true}
                >
                  <MenuDropDownGroupTitle id="top_menu_user_info_header" text={userInfoLine} />
                  {languageSelectorInDropdown}
                  {userProfileLinks}
                  <MenuDropDownSeparator id="top_menu_user_info_signout_seperator" />
                  <MenuDropDownItem
                    id="top_menu_user_info_signout"
                    text={formatMessage(messageDefinitions.signOut)}
                    onClick={this.props.actionHelpers.getOnSignOutClickHandler()}
                  />
                </MenuDropDown>
              ) : null}
              {this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector
                .position === 'right' &&
              this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector.alwaysShow
                ? languageSelector
                : null}
              {profileWithPhotoLink}
            </MenuItemsGroup>
          </HorizontalMenuBar>
        );
      } else {
        menuToReturn = (
          <HorizontalMenuBar
            configuration={this.props.configuration}
            changeBgColor={changeBgColor}
            hideLogo={true}
            miniamlStyle={true}
            topLogoUrlInOrgContext={topLogoUrlInOrgContext}
          >
            <MenuItemsGroup id="top_menu_signed_in_right_group" alignRight={true}>
              {languageSelector}
              <MenuLink
                id="top_menu_signin_link"
                text={formatMessage(messageDefinitions.signOut)}
                onClick={this.props.actionHelpers.getOnSignOutClickHandler()}
              />
            </MenuItemsGroup>
          </HorizontalMenuBar>
        );
      }
      return (
        <div className="top-menu-wrapper fixed-top">
          {serviceBreakBanner}
          {menuToReturn}
          {this.props.children}
        </div>
      );
    } else {
      let signInLink = this.props.visibilitySettings.showAnonymousUserSignInLink ? (
        <MenuLink
          id="top_menu_signin_link"
          text={formatMessage(messageDefinitions.signIn)}
          onClick={this.props.actionHelpers.getOnSignInClickHandler()}
        />
      ) : null;

      let menuToReturn = null;

      if (!this.props.visibilitySettings.showMinimalTopMenuStyle) {
        menuToReturn = (
          <HorizontalMenuBar
            orgId={this.props.selectedRepresentedOrganizationId}
            topLogoUrlInOrgContext={topLogoUrlInOrgContext}
            configuration={this.props.configuration}
            changeBgColor={false}
          >
            <MenuItemsGroup id="top_menu_signed_out_left_group" alignLeft={true}>
              {this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector
                .position === 'left' &&
              (this.props.visibilitySettings.showAnonymousUserLanguageSelector ||
                this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector
                  .alwaysShow)
                ? languageSelector
                : null}
              {backLink}
            </MenuItemsGroup>
            <MenuItemsGroup id="top_menu_signed_out_right_group" alignRight={true}>
              {signInLink}
              {this.props.configuration.serviceProviderTopMenu.anonymousLanguageSelector
                .position === 'right'
                ? languageSelector
                : null}
            </MenuItemsGroup>
          </HorizontalMenuBar>
        );
      } else {
        menuToReturn = (
          <HorizontalMenuBar
            orgId={this.props.selectedRepresentedOrganizationId}
            configuration={this.props.configuration}
            topLogoUrlInOrgContext={topLogoUrlInOrgContext}
            changeBgColor={false}
            hideLogo={true}
            miniamlStyle={true}
          >
            <MenuItemsGroup id="top_menu_signed_out_right_group" alignRight={true}>
              {languageSelector}
            </MenuItemsGroup>
          </HorizontalMenuBar>
        );
      }

      return (
        <div className="top-menu-wrapper fixed-top">
          {serviceBreakBanner}
          {menuToReturn}
          {this.props.children}
        </div>
      );
    }
  }
}

TopMenu.propTypes = {
  children: PropTypes.node,
  serviceBreakBannerData: PropTypes.shape({
    message: PropTypes.string,
    color: PropTypes.string,
    handleOnClose: PropTypes.func,
  }),
  selectedRepresentedOrganizationId: PropTypes.string,
  excludedLanguageCodes: PropTypes.arrayOf(PropTypes.string),
  visibilitySettings: PropTypes.object,
  configuration: PropTypes.object.isRequired,
  representationsOpenedInAdminMode: PropTypes.array,
  selectedRepresentationIndex: PropTypes.number,
  onTopMenuItemClicked: PropTypes.func,
  topMenuLinks: PropTypes.shape({
    myCardsLink: PropTypes.string,
    userDetails: PropTypes.string,
    changeEmail: PropTypes.string,
    changePassword: PropTypes.string,
    registerNewOrganization: PropTypes.string,
    orgInfoLink: PropTypes.string,
    invoicingWebLink: PropTypes.string,
    users: PropTypes.string,
    serviceManagement: PropTypes.string,
    companyCardsManagementLink: PropTypes.string,
    cpbCustomersLink: PropTypes.string,
    findUsersCpb: PropTypes.string,
    manageOrgScannerLink: PropTypes.string,
    searchOrganisationsLink: PropTypes.string,
    searchUsersGlobally: PropTypes.string,
    whitelistSignatoriesLink: PropTypes.string,
    findUsers: PropTypes.string,
    whitelistOrganisationsLink: PropTypes.string,
    companiesWithoutFtaxLink: PropTypes.string,
    manualOrgEnrolmentLink: PropTypes.string,
    scannersManagement: PropTypes.string,
    csvReports: PropTypes.string,
    resendAccountActivationLinkInOrgContext: PropTypes.string,
    resendAccountActivationLink: PropTypes.string,
    customerServiceLink: PropTypes.string,
  }),
  authInfo: PropTypes.shape({
    user: PropTypes.shape({
      firstName: PropTypes.string,
      lastName: PropTypes.string,
      fullName: PropTypes.string,
      email: PropTypes.string,
    }),
  }),
  intl: PropTypes.object.isRequired,
  actionHelpers: PropTypes.shape({
    getOnSignInClickHandler: PropTypes.func.isRequired,
    getOnSignOutClickHandler: PropTypes.func.isRequired,
    getLanguagesOnClickHandler: PropTypes.func.isRequired,
    getRepresentationClickHandler: PropTypes.func.isRequired,
  }),
  selectedLanguage: PropTypes.string.isRequired,
  allRepresentations: PropTypes.arrayOf(PropTypes.string),
  companyInfo: PropTypes.arrayOf(
    PropTypes.shape({
      organizationId: PropTypes.string.isRequired,
      organizationShortName: PropTypes.string,
    })
  ),
  allRepresentationsShowExclamationMarksTooltipKeys: PropTypes.arrayOf(PropTypes.string),
  servicesEnabilityInTopMenu: PropTypes.object,
  openUsualFindCompanyPage: PropTypes.bool,
  allRepresentationsShowExclamationMarks: PropTypes.arrayOf(PropTypes.bool),
  servicesVisibilityInTopMenu: PropTypes.object,
  // Add other prop types here
};

export default injectIntl(TopMenu);
