/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import { getReportURL, getFilename } from '../../helpers/Reports';
import Spinner from './Spinner';

export default class ReportRedirect extends React.Component {
  static get propTypes() {
    return {
      params: PropTypes.object,
    };
  }

  componentDidMount() {
    // report redirect endpoint:
    // <bol host>/#/report-redirect/<country>/<org id>/<lang>
    // usage sample:
    // http://localhost:8080/#/report-redirect/FIN/2327327-1/EN

    const { country } = this.props.params;
    const { orgId } = this.props.params;
    const { lang } = this.props.params;

    // do a redirect to BOL server report endpoint
    window.location = getReportURL(
      `${country}/${orgId}`,
      getFilename(country, orgId, country, null),
      lang,
      true
    );
  }

  render() {
    return (
      <div className="container">
        <br />
        <br />
        <Spinner />
      </div>
    );
  }
}
