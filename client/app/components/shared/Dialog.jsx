import PropTypes from 'prop-types';
import React from 'react';

import ContextBlock from './ContextBlock';
import StoreSubscription from '../../helpers/StoreSubscription';
import { dialogStore } from '../../stores/Stores';
import closeDialog from '../../actions/actionCreators/CloseDialog';
import confirmDialog from '../../actions/actionCreators/ConfirmDialog';

class Dialog extends React.Component {
  static get propTypes() {
    return {
      dialogType: PropTypes.any,
      children: PropTypes.node,
      placement: PropTypes.string,
    };
  }

  constructor(props) {
    super(props);
    this.dialogSub = new StoreSubscription(dialogStore, this.stateChanged.bind(this));
    this.state = this.mapStoreToState(dialogStore.getState());
  }

  componentDidMount() {
    this.dialogSub.activate();
  }

  componentWillUnmount() {
    this.dialogSub.deactivate();
  }

  mapStoreToState(storeState) {
    if (storeState.dialogType === this.props.dialogType) {
      return {
        isOpen: true,
        dialogParams: storeState.dialogParams,
        targetElement: storeState.targetElement,
      };
    }

    return {
      isOpen: false,
    };
  }
  stateChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  injectProps(element) {
    return React.cloneElement(element, {
      ...this.state.dialogParams,
      handleConfirm: confirmDialog,
    });
  }

  render() {
    if (this.state.isOpen) {
      return (
        <ContextBlock
          toggle={closeDialog}
          target={this.state.targetElement}
          isOpen
          placement={this.props.placement}
        >
          {React.Children.map(this.props.children, this.injectProps.bind(this))}
        </ContextBlock>
      );
    }

    return null;
  }
}

export default Dialog;
