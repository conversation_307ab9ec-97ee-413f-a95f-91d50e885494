import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import FormattedMessage from '../../components/i18n/FormattedMessage'

class DetailsModalComponent extends React.Component {
  static get propTypes() {
    return {
      title: PropTypes.node.isRequired,
      editAction: PropTypes.object,
      onDetailsClose: PropTypes.func.isRequired,
      extraClassNames: PropTypes.object,
      extraHeaderClassNames: PropTypes.object,
      extraBodyClassNames: PropTypes.object,
      children: PropTypes.node,
    };
  }

  constructor(props) {
    super(props);
    this.onKeyUp = this.onKeyUp.bind(this);
  }

  componentDidMount() {
    document.addEventListener('keyup', this.onKeyUp);
  }

  componentWillUnmount() {
    document.removeEventListener('keyup', this.onKeyUp);
  }

  onKeyUp(event) {
    if (event.keyCode === 27) {
      if (this.modalsVisible()) {
        $('.modal:visible').modal('hide');
      } else {
        event.preventDefault();
        this.closeDetails();
      }
    }
  }

  closeDetails() {
    this.props.onDetailsClose();
  }

  contextBlocksVisible() {
    // Modals may open context blocks.
    return $('.context-block:visible').length > 0;
  }

  modalsVisible() {
    // We're not using class="modal" here, just the inner parts, so the
    // presence of visible elements with class="modal" indicates some other
    // Bootstrap modals are currently active.
    return $('.modal:visible').length > 0;
  }

  renderModalHeader() {
    const cssClasses = classNames('modal-header', this.props.extraHeaderClassNames);
    return (
      <div className={cssClasses}>
        <div className="left-content">
          <h4 className="modal-title pointer-events-none">{this.props.title}</h4>
        </div>
        <div className="right-content d-flex justify-content-end">
          {this.props.editAction}
          <div
            id="close_view_button_title"
            className="ml-4"
            role="button"
            data-dismiss="modal"
            onClick={this.closeDetails.bind(this)}
            aria-label="Close"
          >
            <i className="fa fa-close fa-pr--bol" />
            <FormattedMessage
              id="details.closeDetails"
              description="Close details pane button label"
              defaultMessage="Close view"
            />
          </div>
        </div>
      </div>
    );
  }

  renderModalBody() {
    const cssClasses = classNames('modal-body', this.props.extraBodyClassNames);
    return <div className={cssClasses}>{this.props.children}</div>;
  }

  render() {
    const cssClasses = classNames('modal-content details-details', this.props.extraClassNames);

    return (
      <div className={cssClasses}>
        {this.renderModalHeader()}
        {this.renderModalBody()}
      </div>
    );
  }
}

export default DetailsModalComponent;
