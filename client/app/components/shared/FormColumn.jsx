import React from 'react';
import PropTypes from 'prop-types';

class FormColumn extends React.Component {
  static get propTypes() {
    return {
      width: PropTypes.string,
      widthXs: PropTypes.string,
      widthSm: PropTypes.string,
      widthMd: PropTypes.string,
      widthLg: PropTypes.string,
      mobileHeader: PropTypes.bool,
      visible: PropTypes.bool,
      className: PropTypes.string,
      onClick: PropTypes.func,
      offSet: PropTypes.string,
      xsOffSet: PropTypes.string,
      smOffSet: PropTypes.string,
      mdOffSet: PropTypes.string,
      lgOffSet: PropTypes.string,
      children: PropTypes.node,
      id: PropTypes.string,
    };
  }

  static get defaultProps() {
    return {
      visible: true,
    };
  }

  render() {
    if (!this.props.visible) {
      return null;
    }

    // default widths
    let ColWidth = 12;
    let ColWidthXs = 12;
    let ColWidthSm = 12;
    let ColWidthMd = 12;
    let ColWidthLg = 12;
    let offsetOfElement = '';

    if (this.props.width) {
      ColWidth = this.props.width;
      ColWidthXs = 12;
      ColWidthSm = 12;
      ColWidthMd = ColWidth;
      ColWidthLg = ColWidth;
    }

    if (this.props.widthXs) {
      ColWidthXs = this.props.widthXs;
    }

    if (this.props.widthSm) {
      ColWidthSm = this.props.widthSm;
    }

    if (this.props.widthMd) {
      ColWidthMd = this.props.widthMd;
    }

    if (this.props.widthLg) {
      ColWidthLg = this.props.widthLg;
    }

    if (this.props.offSet) {
      offsetOfElement +=
        ' offset-xs-' +
        0 +
        ' offset-sm-' +
        this.props.offSet +
        ' offset-md-' +
        this.props.offSet +
        ' offset-lg-' +
        this.props.offSet;
    }

    if (this.props.xsOffSet) {
      offsetOfElement = ' offset-xs-' + this.props.xsOffSet;
    }

    if (this.props.smOffSet) {
      offsetOfElement += ' offset-sm-' + this.props.smOffSet;
    }

    if (this.props.mdOffSet) {
      offsetOfElement += ' offset-md-' + this.props.mdOffSet;
    }

    if (this.props.lgOffSet) {
      offsetOfElement += ' offset-lg-' + this.props.lgOffSet;
    }

    // final widths
    let className =
      ' col-' +
      ColWidthXs +
      ' col-sm-' +
      ColWidthSm +
      ' col-md-' +
      ColWidthMd +
      ' col-lg-' +
      ColWidthLg +
      offsetOfElement;

    if (this.props.mobileHeader) {
      className = className + ' form-row-header-mobile hidden-md-up';
    }

    if (this.props.className) {
      className = className + ' ' + this.props.className;
    }

    return (
      <div id={this.props.id} className={className} onClick={this.props.onClick}>
        {this.props.children}
      </div>
    );
  }
}

export default FormColumn;
