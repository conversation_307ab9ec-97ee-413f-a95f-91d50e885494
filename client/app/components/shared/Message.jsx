import React from 'react';
import PropTypes from 'prop-types';

class Message extends React.Component {
  static get propTypes() {
    return {
      className: PropTypes.string,
      defaultIcon: PropTypes.bool,
      type: PropTypes.oneOf(['default', 'danger', 'warning', 'success', 'info']),
      children: PropTypes.node.isRequired,
      id: PropTypes.string,
    };
  }

  render() {
    let defaultIcon = typeof this.props.defaultIcon !== 'undefined' ? this.props.defaultIcon : true;
    let className = '';

    if (this.props.className) {
      className = ' ' + this.props.className;
    }

    return (
      <div id={this.props.id} className={'alert alert-' + this.props.type + className} role="alert">
        {this.props.type === 'success' && defaultIcon ? (
          <i className="fa fa-check fa-mr  align-middle" aria-hidden="true"></i>
        ) : (
          ''
        )}
        {this.props.type === 'warning' && defaultIcon ? (
          <i className="fa fa-info-circle fa-mr  align-middle" aria-hidden="true"></i>
        ) : (
          ''
        )}
        {this.props.type === 'danger' && defaultIcon ? (
          <i className="fa fa-exclamation-circle fa-mr  align-middle" aria-hidden="true"></i>
        ) : (
          ''
        )}
        {this.props.type === 'info' && defaultIcon ? (
          <i className="fa fa-info-circle fa-mr  align-middle" aria-hidden="true"></i>
        ) : (
          ''
        )}
        {this.props.children}
      </div>
    );
  }
}

export default Message;
