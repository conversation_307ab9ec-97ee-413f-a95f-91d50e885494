import React from 'react';
import PropTypes from 'prop-types';
import Input<PERSON>abel from './InputLabel.jsx';

class TextInput extends React.Component {
  static get propTypes() {
    return {
      // Input props
      id: PropTypes.string,
      inputClassName: PropTypes.string,
      autoComplete: PropTypes.bool,
      field: PropTypes.object,
      placeholder: PropTypes.string,
      autoFocus: PropTypes.bool,
      disabled: PropTypes.bool,
      inputGroup: PropTypes.bool,
      addonPossition: PropTypes.oneOf(['left', 'right']),
      type: PropTypes.oneOf(['text', 'password']),
      static: PropTypes.bool,
      readOnlyValue: PropTypes.string,
      onPaste: PropTypes.func,
      // Label props
      labelClassName: PropTypes.string,
      messageId: PropTypes.string,
      defaultMessage: PropTypes.string,
      required: PropTypes.bool,
      bold: PropTypes.bool,
      inline: PropTypes.bool,
      children: PropTypes.node,
    };
  }

  render() {
    let isStatic = this.props.static ? true : false;
    let inputClassName = 'form-control';
    let labelClassName = '';
    let labelAnimationClass = '';
    let readOnlyValue = this.props.readOnlyValue;
    let addonPossition = this.props.addonPossition ? this.props.addonPossition : 'right';

    let value = '';
    let handleOnChange = () => {};
    let handleOnEnterKeyDownToSubmitForm = () => {};
    let handleOnBlur = () => {};

    if (this.props.inputClassName) {
      inputClassName = inputClassName + ' ' + this.props.inputClassName;
    }

    if (!readOnlyValue) {
      if (this.props.field) {
        value = this.props.field.value || '';
        handleOnChange = this.props.field.handleOnChange || handleOnChange;
        handleOnEnterKeyDownToSubmitForm =
          this.props.field.handleOnEnterKeyDownToSubmitForm || handleOnEnterKeyDownToSubmitForm;
        handleOnBlur = this.props.field.handleOnBlur || handleOnBlur;
      }
    } else {
      value = readOnlyValue;
    }

    let textInput = null;

    if (isStatic) {
      textInput = <p className="form-control-static">{value}</p>;
    } else {
      textInput = (
        <input
          id={this.props.id}
          type={this.props.type ? this.props.type : 'text'}
          className={inputClassName}
          autoComplete={this.props.autoComplete ? 'on' : 'off'}
          value={value}
          onChange={handleOnChange}
          onKeyDown={handleOnEnterKeyDownToSubmitForm}
          onBlur={handleOnBlur}
          placeholder={this.props.placeholder}
          autoFocus={this.props.autoFocus ? true : false}
          disabled={this.props.disabled ? true : false}
          onPaste={this.props.onPaste ? this.props.onPaste : () => {}}
        />
      );
    }

    if (this.props.labelClassName) {
      labelClassName = labelClassName + ' ' + this.props.labelClassName;
    }

    if (this.props.field && !this.props.field.value && !readOnlyValue) {
      labelClassName = labelClassName + labelAnimationClass;
    }

    let textInputLabel = this.props.messageId ? (
      <InputLabel
        htmlFor={this.props.id}
        messageId={this.props.messageId}
        defaultMessage={this.props.defaultMessage}
        required={this.props.required}
        inline={this.props.inline}
        bold={this.props.bold}
        className={labelClassName}
      />
    ) : null;

    let labelPossitionTop = textInputLabel;

    let addonPossitionLeft = null;
    let addonPossitionRight = null;

    if (this.props.children && this.props.inputGroup) {
      if (addonPossition == 'left') {
        addonPossitionLeft = this.props.children;
      } else {
        addonPossitionRight = this.props.children;
      }
    }

    return this.props.children && this.props.inputGroup ? (
      <div>
        {labelPossitionTop}
        <div className="input-group">
          {addonPossitionLeft}
          {textInput}
          {addonPossitionRight}
        </div>
      </div>
    ) : (
      <div>
        {labelPossitionTop}
        {textInput}
      </div>
    );
  }
}

export default TextInput;
