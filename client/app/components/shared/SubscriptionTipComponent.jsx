/* eslint-disable max-len */
import React from 'react';
import PropTypes from 'prop-types';
import FormattedMessage from '../../components/i18n/FormattedMessage'

import { subscriptionStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
/* eslint-enable max-len */

class SubscriptionTipComponent extends React.Component {
  static get propTypes() {
    return {
      searchMode: PropTypes.bool,
    };
  }

  constructor(props) {
    super(props);
    this.subscriptionSubscription = new StoreSubscription(
      subscriptionStore,
      this.subscriptionChanged.bind(this)
    );
    const storeState = subscriptionStore.getState();
    this.state = this.mapStoreToState(storeState);
  }

  componentDidMount() {
    this.subscriptionSubscription.activate();
  }

  componentWillUnmount() {
    this.subscriptionSubscription.deactivate();
  }

  mapStoreToState(storeState) {
    return {
      subscription: storeState.subscription,
    };
  }

  subscriptionChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  renderSearchTip() {
    return (
      <div>
        <FormattedMessage
          id="search.tip.subscribed"
          description="Search tip box message when subscribed"
          defaultMessage="Previously searched companies shown in Search history"
        >
          {message => (
            <span dangerouslySetInnerHTML={{ __html: message }} />
          )}
        </FormattedMessage>
      </div>
    );
  }

  renderCompaniesTip(subscribed) {
    return (
      <div>
        {subscribed ? (
          <FormattedMessage
            id="companies.tip.subscribed"
            description="Companies tip box message when subscribed"
            defaultMessage="Your searched reports are archived automatically."
          >
            {message => (
              <span dangerouslySetInnerHTML={{ __html: message }} />
            )}
          </FormattedMessage>
        ) : (
          <FormattedMessage
            id="companies.tip.noSubscription"
            description="Companies tip box message when no subscription"
            defaultMessage="Upgrade the service and archive searched reports"
          >
            {message => (
              <span dangerouslySetInnerHTML={{ __html: message }} />
            )}
          </FormattedMessage>
        )}
      </div>
    );
  }

  render() {
    const { subscription } = this.state;
    const subscribed = subscription && subscription.active;
    if (!subscribed && this.props.searchMode) {
      return null;
    }

    return (
      <div id="search_info_message" className="alert alert-info mt-5 mb-5">
        {this.props.searchMode ? this.renderSearchTip() : this.renderCompaniesTip(subscribed)}
      </div>
    );
  }
}

export default SubscriptionTipComponent;
