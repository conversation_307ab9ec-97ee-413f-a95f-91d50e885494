/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { getReportURL } from '../../helpers/Reports';
import { featureActive } from '../../helpers/FeatureFlags';
import ReportLanguageSwitchComponent from '../companies/ReportLanguageSwitchComponent';
import companyReportsSelect from '../../actions/actionCreators/CompanyReportsSelect';
import { intlPropType } from '../i18n/IntlPropTypes';
/* eslint-enable max-len */

class ViewLatestReportButtonComponent extends React.Component {
  static get defaultProps() {
    return {
      enableLangSelection: true,
    };
  }

  static get propTypes() {
    return {
      title: PropTypes.string,
      govOrgId: PropTypes.string,
      country: PropTypes.string,
      filename: PropTypes.string,
      enableLangSelection: PropTypes.bool,
      companyId: PropTypes.string,
      intl: intlPropType.isRequired,
    };
  }

  onReportOpen() {
    if (window.location.href.indexOf('#/companies') !== -1) {
      // reload archives list when user viewed report from search history company details
      setTimeout(() => companyReportsSelect(this.props.companyId), 6500);
    }
  }

  renderViewLatestReportButton(title, country, govOrgId, filename) {
    if (this.props.enableLangSelection && featureActive('web_reports')) {
      if (country !== 'EST') {
        return this.renderWebReportButton(title, country, govOrgId, filename);
      }
      if (country === 'EST' && featureActive('web_reports_ee')) {
        return this.renderWebReportButton(title, country, govOrgId, filename);
      }
    }
    return this.renderPdfReportButton(country, govOrgId, filename);
  }

  renderWebReportButton(formatMessage, country, govOrgId, filename) {
    return (
      <ReportLanguageSwitchComponent
        id="company_report_link"
        title={this.props.title}
        companyId={`${country}/${govOrgId}`}
        filename={filename}
        selectedLanguageCallback={this.onReportOpen.bind(this)}
      />
    );
  }

  renderPdfReportButton(country, govOrgId, filename) {
    return (
      <a
        className="btn btn-primary"
        href={getReportURL(`${country}/${govOrgId}`, filename, this.props.intl.locale)}
        id="company_report_link"
        target="_blank"
        rel="noopener noreferrer"
        onClick={this.onReportOpen.bind(this)}
      >
        <i
          id="company_details_view.report_pdf_icon"
          className="fa fa-file-pdf-o fa-pr--bol"
          aria-hidden="true"
        />
        <FormattedMessage
          id="companyDetails.companyLatestReportLink"
          description="Label company latest report link"
          defaultMessage="View latest report"
        />
      </a>
    );
  }

  render() {
    return this.renderViewLatestReportButton(
      this.props.title,
      this.props.country,
      this.props.govOrgId,
      this.props.filename,
      this.props.enableLangSelection
    );
  }
}

export default injectIntl(ViewLatestReportButtonComponent);
