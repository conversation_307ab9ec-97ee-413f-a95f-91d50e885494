import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { Popper } from 'react-popper';

class ContextBlock extends React.Component {
  static get defaultProps() {
    return {
      twoColumn: false,
      placement: 'auto-end',
      modifiers: {
        flip: {
          enabled: false,
        },
        preventOverflow: {
          enabled: true,
          escapeWithReference: false,
        },
        offset: {
          enabled: false,
          offset: '0px, 0px',
        },
      },
    };
  }

  static get propTypes() {
    return {
      isOpen: PropTypes.bool.isRequired,
      target: PropTypes.object.isRequired,
      title: PropTypes.node,
      toggle: PropTypes.func,
      twoColumn: PropTypes.bool,
      children: PropTypes.node,
      placement: PropTypes.string,
      modifiers: PropTypes.object,
      outerClasses: PropTypes.string,
      increasedWidth: PropTypes.bool,
    };
  }

  handleToggle() {}

  handleClose() {
    if (this.props.toggle) {
      this.props.toggle();
    }
  }

  render() {
    if (!this.props.isOpen) {
      return null;
    }
    const outerClasses = classNames(
      {
        'context-block': true,
        'no-border': true,
        'double-width': this.props.twoColumn,
        'increased-width': this.props.increasedWidth,
      },
      this.props.outerClasses
    );

    let { title, children } = this.props;
    if (!title) {
      children = React.Children.toArray(this.props.children);
      title = children.shift();
    }

    return (
      <Popper
        placement={this.props.placement}
        modifiers={this.props.modifiers}
        referenceElement={this.props.target}
        isOpen={this.props.isOpen}
        toggle={this.handleToggle.bind(this)}
        positionFixed
        eventsEnabled
      >
        {({ ref, style, placement }) => (
          <div className={outerClasses} ref={ref} style={style} data-placement={placement}>
            <div className="border">
              <div className="heading d-flex justify-content-between">
                <div className="mr-5 pointer-events-none">{title}</div>
                <div>
                  <a onClick={this.handleClose.bind(this)} role="button">
                    <i className="fa fa-close" />
                  </a>
                </div>
              </div>
              <div className="content">{children}</div>
            </div>
          </div>
        )}
      </Popper>
    );
  }
}

export default ContextBlock;
