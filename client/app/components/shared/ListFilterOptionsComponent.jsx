import PropTypes from 'prop-types';
import React from 'react';

import { defineMessages, injectIntl } from 'react-intl';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  clearFilter: {
    id: 'companies.filter.clearFilter',
    description: "'Clear filter' text",
    defaultMessage: 'Clear filter',
  },
});

class ListFilterOptionsComponent extends React.Component {

  render() {
    const { formatMessage } = this.props.intl;
    return (
      <div className="filter-options">
        <div className="clear-filter">
          <a
            href='#'
            type="button"
            className="pull-right mt-2"
            id="clear-filter-link"
            onClick={this.props.callback}
          >
            <i className="fa fa-trash-o fa-pr--bol" />
            {formatMessage(messages.clearFilter)}
          </a>
        </div>
      </div>
    );
  }
}

ListFilterOptionsComponent.propTypes = {
  callback: PropTypes.func.isRequired,
  intl: intlPropType.isRequired
};

export default injectIntl(ListFilterOptionsComponent);
