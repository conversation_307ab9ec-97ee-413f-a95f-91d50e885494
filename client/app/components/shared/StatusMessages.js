import { defineMessages } from 'react-intl';

const StatusMessages = defineMessages({
  ok: {
    id: 'statusLabel.ok',
    description: 'Company status: OK',
    defaultMessage: 'OK',
  },
  attention: {
    id: 'statusLabel.attention',
    description: 'Company status: Attention',
    defaultMessage: 'Attention',
  },
  incomplete: {
    id: 'statusLabel.incomplete',
    description: 'Company status: Incomplete',
    defaultMessage: 'Incomplete',
  },
  investigate: {
    id: 'statusLabel.investigate',
    description: 'Company status: Investigate',
    defaultMessage: 'Investigate',
  },
  stop: {
    id: 'statusLabel.stop',
    description: 'Company status: Stop',
    defaultMessage: 'Stop',
  },
  notOk: {
    id: 'companies.filter.statusNotOK',
    description: 'Company status: Not OK',
    defaultMessage: 'Not OK',
  },
  any: {
    id: 'companies.filter.statusAny',
    description: 'Company status: Any',
    defaultMessage: 'Any status',
  },
});

export default StatusMessages;
