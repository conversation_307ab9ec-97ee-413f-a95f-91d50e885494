import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';

const WizardGuideStep = props => (
  <div
    className={classNames({
      'wizard-guide__step--completed': props.completed,
      'wizard-guide__step--active': props.active,
      'wizard-guide__step': true,
      'pointer-events-none': true,
    })}
  >
    {props.completed ? <i className="material-icons wizard-guide__step-icon">done</i> : null}
    <span className="wizard-guide__step-number">{props.num}</span>
    <span className="wizard-guide__step-title">{props.title}</span>
  </div>
);

WizardGuideStep.propTypes = {
  active: PropTypes.bool,
  completed: PropTypes.bool,
  num: PropTypes.node,
  title: PropTypes.node,
};

class WizardGuideComponent extends React.Component {
  // eslint-disable-line react/no-multi-comp

  static get propTypes() {
    return {
      steps: PropTypes.arrayOf(PropTypes.object).isRequired,
      activeStep: PropTypes.string.isRequired,
    };
  }

  render() {
    const { steps, activeStep } = this.props;
    const active = steps.find(elem => elem.step === activeStep);
    const completedSteps = steps.slice(0, steps.findIndex(elem => elem.step === activeStep));
    const futureSteps = steps.slice(steps.findIndex(elem => elem.step === activeStep) + 1);
    const showCompleted = active.num === 3;
    return (
      <div className="wizard-guide d-flex">
        {completedSteps.map(step => (
          <WizardGuideStep
            key={step.step}
            title={step.title}
            num={step.num}
            active={false}
            completed
          />
        ))}
        <WizardGuideStep
          key={active.step}
          title={active.title}
          num={active.num}
          completed={showCompleted}
          active
        />
        {futureSteps.map(step => (
          <WizardGuideStep
            key={step.step}
            title={step.title}
            num={step.num}
            completed={false}
            active={false}
          />
        ))}
      </div>
    );
  }
}

export default WizardGuideComponent;
