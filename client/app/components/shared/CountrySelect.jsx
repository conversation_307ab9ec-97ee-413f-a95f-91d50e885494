import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';

import { getCountryShortlist, getSortedCountryList } from '../../helpers/Countries';

class CountrySelect extends React.Component {

  static get propTypes() {
    return {
      className: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      defaultValue: PropTypes.string,
      id: PropTypes.string,
      reference: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
    };
  }

  static get defaultProps() {
    return {
      defaultValue: '',
    };
  }

  renderCountryOptions(countries) {
    return countries.map(country => (
      <option key={country.code} value={country.code}>
        {country.name}
      </option>
    ));
  }

  render() {
    const { formatMessage } = this.props.intl;
    const countryShortlist = this.renderCountryOptions(getCountryShortlist(formatMessage));

    const allCountries = this.renderCountryOptions(getSortedCountryList(formatMessage));
    return (
      <select
        className={this.props.className}
        id={this.props.id}
        name={this.props.name}
        ref={this.props.reference}
        defaultValue={this.props.defaultValue}
        role="button"
      >
        <option value="" />
        {countryShortlist}
        <option value="">---------</option>
        {allCountries}
      </select>
    );
  }
}

export default injectIntl(CountrySelect);
