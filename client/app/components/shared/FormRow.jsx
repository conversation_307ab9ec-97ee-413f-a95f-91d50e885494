import React from 'react';
import PropTypes from 'prop-types';

class FormRow extends React.Component {
  static get propTypes() {
    return {
      className: PropTypes.string,
      header: PropTypes.bool,
      id: PropTypes.string,
      role: PropTypes.string,
      onClick: PropTypes.func,
      visible: PropTypes.bool,
      children: PropTypes.node,
    };
  }

  static get defaultProps() {
    return {
      visible: true,
    };
  }

  render() {
    if (this.props.visible == false) {
      return null;
    }

    let cssClass = 'row';

    if (this.props.className) {
      cssClass = 'row ' + this.props.className;
    }

    if (this.props.header) {
      cssClass = cssClass + ' form-row-header hidden-sm-down';
    }

    return (
      <div
        id={this.props.id}
        className={cssClass}
        role={this.props.role}
        onClick={this.props.onClick}
      >
        {this.props.children}
      </div>
    );
  }
}

export default FormRow;
