import React from 'react';
import PropTypes from 'prop-types';
import { FormattedMessage, IntlProvider } from 'react-intl';
import localizationHelpers from '../../helpers/localizationHelpers.js';

class InputLabel extends React.Component {
  static get propTypes() {
    return {
      inline: PropTypes.bool,
      className: PropTypes.string,
      bold: PropTypes.bool,
      messageId: PropTypes.string,
      defaultMessage: PropTypes.string,
      required: PropTypes.bool,
      htmlFor: PropTypes.string,
      type: PropTypes.string,
      children: PropTypes.node,
    };
  }

  render() {
    let localizations = localizationHelpers.getLocalizationsForUIComponents();

    let cssClass = 'form-control-label';

    if (this.props.inline) {
      cssClass = 'col-form-label';
    }

    if (this.props.type === 'custom-checkbox') {
      cssClass = 'custom-control custom-checkbox';
    }

    if (this.props.type === 'custom-radio') {
      cssClass = 'custom-control custom-radio';
    }

    if (this.props.className) {
      cssClass = cssClass + ' ' + this.props.className;
    }

    let content = null;

    if (this.props.children) {
      content = this.props.children;
    } else {
      if (this.props.bold) {
        content = (
          <strong>
            <FormattedMessage
              id={this.props.messageId}
              defaultMessage={this.props.defaultMessage}
            />
          </strong>
        );
      } else {
        content = (
          <FormattedMessage id={this.props.messageId} defaultMessage={this.props.defaultMessage} />
        );
      }
    }

    if (this.props.required) {
      cssClass = cssClass + ' required';
    }

    return (
      <IntlProvider {...localizations}>
        <label id={this.props.htmlFor + '_label'} className={cssClass} htmlFor={this.props.htmlFor}>
          {content}
        </label>
      </IntlProvider>
    );
  }
}

export default InputLabel;
