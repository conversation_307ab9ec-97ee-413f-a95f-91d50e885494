import React from 'react';
import classNames from 'classnames';
import { injectIntl, defineMessages } from 'react-intl';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  visitorIcon: {
    id: 'companyReportMissingIconLabel',
    description: 'Visitor node missing report icon label',
    defaultMessage: 'Company report is not available',
  },
});

class VisitorIconComponent extends React.Component {
  static get propTypes() {
    return {
      intl: intlPropType.isRequired,
    };
  }
  render() {
    const { formatMessage } = this.props.intl;

    return (
      <i
        className={classNames('fa', 'fa-file-text-o', 'text-muted-more', 'fa-px--bol')}
        title={formatMessage(messages.visitorIcon)}
      />
    );
  }
}

export default injectIntl(VisitorIconComponent);
