import PropTypes from 'prop-types';
import React from 'react';
import getClassNames from 'classnames';

const DetailsListItem = ({
  classNames,
  printMode = false,
  clickable= false,
  id= '',
  children: _children
}) => {
  const children = React.Children.toArray(_children);

  if (!printMode && children.length < 2) {
    return null;
  }

  return (
    <div id={id} className={clickable ? 'row' : 'row pointer-events-none'}>
      <div
        className={getClassNames(
          'no-col-gutter-right',
          ['col-6', printMode],
          ['col-md-6', !printMode],
          'col-xs-12',
          'text-muted'
        )}
      >
        {children[0]}
      </div>
      <div
        className={getClassNames(
          ['col-6', printMode],
          ['col-md-6', !printMode],
          'col-xs-12',
          'details-list--value'
        )}
      >
        <span className={classNames}>{children[1]}</span>
      </div>
    </div>
  );
};

DetailsListItem.propTypes = {
  classNames: PropTypes.string,
  children: PropTypes.node,
  printMode: PropTypes.bool,
  clickable: PropTypes.bool,
  id: PropTypes.string,
};


export default DetailsListItem;
