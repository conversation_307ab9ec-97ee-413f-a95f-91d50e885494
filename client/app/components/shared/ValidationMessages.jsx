import React from 'react';
import PropTypes from 'prop-types';
import { FormattedMessage, IntlProvider } from 'react-intl';
import localizationHelpers from '../../helpers/localizationHelpers.js';

const defaultMessageIds = {
  unspecified: 'errors.validation.field.error',
  required: 'errors.validation.field.required',
  match: 'errors.validation.field.match',
  mismatch: 'errors.validation.field.mismatch',
  minLength: 'errors.validation.field.minLength',
  maxLength: 'errors.validation.field.maxLength',
  alphanumeric: 'errors.validation.field.alphanumeric',
  alphanumericWithAdditionalSymbols: 'errors.validation.field.alphanumericWithAdditionalSymbols',
  exactNumberOfDigits: 'errors.validation.field.exactNumberOfDigits',
  fTaxOrganizationNumber: 'errors.validation.field.fTaxOrganizationNumber',
  finnishOrganizationCode: 'errors.validation.field.finnishOrganizationCode',
  swedishOrganizationCode: 'errors.validation.field.swedishOrganizationCode',
  lithuanianOrganizationCode: 'errors.validation.field.lithuanianOrganizationCode',
  email: 'errors.validation.field.email',
  blackListedEmailAddress: 'errors.validation.field.blacklistedEmailAddress',
  password: 'errors.validation.field.password',
  passwordHasSpaces: 'errors.validation.field.password.hasSpaces',
  passwordExceedsConsecutiveCharactersLimit:
    'errors.validation.field.password.exceedsConsecutiveCharactersLimit',
  passwordAtLeastOneNumberRequired:
    'errors.validation.field.password.passwordAtLeastOneNumberRequired',
  passwordAtLeastOneLowerAndUpperCaseLetterRequired:
    'errors.validation.field.password.passwordAtLeastOneLowerAndUpperCaseLetterRequired',
  passwordValidationMinimumLength:
    'errors.validation.field.password.passwordValidationMinimumLength',
  billingAddress: 'errors.validation.field.billingAddress',
  atLeastOneAlphanumeric: 'errors.validation.field.atLeastOneAlphanumeric',
  componentCustomValidation: 'errors.validation.field.componentCustomValidation',
  organizationApplicationState: 'errors.validation.field.organizationApplicationState',
  incorrectEmailsInString: 'errors.validation.field.incorrectEmailsInString',
  maxNumberOfEmailsIsImported: 'errors.validation.field.maxNumberOfEmailsIsImported',
  incorrectCountryInPersonNumberSelection: 'errors.validation.field.personNumberCountry',
  finnishSocialSecurityNumber: 'errors.validation.field.finnishSocialSecurityNumber',
  ediValue: 'errors.validation.field.ediNumber',
  swedishPersonalNumber: 'errors.validation.field.swedishPersonalNumber',
  scannerIdCodeIsNotValid: 'errors.validation.field.scannerIdCodeIsNotValid',
  dateFormat: 'errors.validation.field.dateFormat',
  dateOnlyUntilToday: 'errors.validation.field.dateOnlyUntilToday',
  expiracyDateInvalid: 'errors.validation.field.expiracyDateInvalid',
  nationalityCode: 'errors.validation.field.nationalityCode',
  coordinationNumber: 'errors.validation.field.coordinationNumber',
  finnishPersonalNumber: 'errors.validation.field.finnishPersonalNumber',
  swedishFTaxNumber: 'errors.validation.field.swedishFTaxNumber',
  lithuanianPersonalNumber: 'errors.validation.field.lithuanianPersonalNumber',
  polishPersonalNumber: 'errors.validation.field.polishPersonalNumber',
  estonianPersonalNumber: 'errors.validation.field.estonianPersonalNumber',
  latvianPersonalNumber: 'errors.validation.field.latvianPersonalNumber',
  bulgarianPersonalNumber: 'errors.validation.field.bulgarianPersonalNumber',
  czechPersonalNumber: 'errors.validation.field.czechPersonalNumber',
  danishPersonalNumber: 'errors.validation.field.danishPersonalNumber',
  maltaPersonalNumber: 'errors.validation.field.maltaPersonalNumber',
  netherlandsPersonalNumber: 'errors.validation.field.netherlandsPersonalNumber',
  norwegianPersonalNumber: 'errors.validation.field.norwegianPersonalNumber',
  romanianPersonalNumber: 'errors.validation.field.romanianPersonalNumber',
  slovakPersonalNumber: 'errors.validation.field.slovakPersonalNumber',
  generalRegexpPersonalNumber: 'errors.validation.field.generalRegexpPersonalNumber',
  lmaNumberFormat: 'errors.validation.field.lmaNumberFormat',
  faroeIslandsOrganizationNumber: 'errors.validation.field.faroeIslandsOrganizationNumber',
  polishOrganizationNumber: 'errors.validation.field.polishOrganizationNumber',
  estonianOrganizationNumber: 'errors.validation.field.estonianOrganizationNumber',
  latvianOrganizationNumber: 'errors.validation.field.latvianOrganizationNumber',
  bulgarianOrganizationNumber: 'errors.validation.field.bulgarianOrganizationNumber',
  croatianOrganizationNumber: 'errors.validation.field.croatianOrganizationNumber',
  czechianOrganizationNumber: 'errors.validation.field.czechianOrganizationNumber',
  danishOrganizationNumber: 'errors.validation.field.danishOrganizationNumber',
  netherlandsOrganizationNumber: 'errors.validation.field.netherlandsOrganizationNumber',
  norwegianOrganizationNumber: 'errors.validation.field.norwegianOrganizationNumber',
  romanianOrganizationNumber: 'errors.validation.field.romanianOrganizationNumber',
  slovakOrganizationNumber: 'errors.validation.field.slovakOrganizationNumber',
  spanishOrganizationNumber: 'errors.validation.field.spanishOrganizationNumber',
};

class ValidationMessages extends React.Component {
  static get propTypes() {
    return {
      field: PropTypes.object,
      addErrorClass: PropTypes.bool,
      children: PropTypes.node,
    };
  }

  render() {
    let localizations = localizationHelpers.getLocalizationsForUIComponents();

    let className = null;
    let errorMessages = null;

    if (this.props.addErrorClass) {
      className = 'has-danger';
      errorMessages = [];
    }

    if (this.props.field.errors.hasErrors) {
      className = 'has-danger';
      errorMessages = [];

      this.props.field.errors.list.forEach((error, index) => {
        let messageId = defaultMessageIds.unspecified;

        if (this.props[error.key]) {
          messageId = this.props[error.key];
        } else if (defaultMessageIds[error.key]) {
          messageId = defaultMessageIds[error.key];
        }

        let errorMessage = (
          <div key={index} className="form-control-feedback">
            <FormattedMessage
              id={messageId}
              defaultMessage="Validation error"
              values={error.msgFormattingParams}
            />
          </div>
        );

        errorMessages.push(errorMessage);
      });
    }

    return (
      <IntlProvider {...localizations}>
        <div className={className}>
          {this.props.children}
          {errorMessages}
        </div>
      </IntlProvider>
    );
  }
}

export default ValidationMessages;
