import PropTypes from 'prop-types';
import React from 'react';
import { defineMessages, injectIntl } from 'react-intl';

import { stopPropagation } from '../../helpers/EventHandlers';
import { getReportURL } from '../../helpers/Reports';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  openCompnyReportIconLabel: {
    id: 'openCompnyReportIconLabel',
    description: 'Label for company report icon',
    defaultMessage: 'Open company report',
  },
  companyReportMissingIconLabel: {
    id: 'companyReportMissingIconLabel',
    description: 'Label for company report icon if report is missing',
    defaultMessage: 'Company report is not available',
  },
});

class ReportIconComponent extends React.Component {
  static get propTypes() {
    return {
      report_available: PropTypes.bool,
      company_resource_id: PropTypes.string,
      filename: PropTypes.string,
      hasCombinedReport: PropTypes.bool,
      intl: intlPropType.isRequired,
    };
  }

  static get defaultProps() {
    return {
      hasCombinedReport: false,
    };
  }

  handleOnClick(event) {
    if (event) {
      stopPropagation(event);
    }
  }

  renderIcon(textClass) {
    return <span className={`fa fa-file-text-o ${textClass}`} />;
  }

  render() {
    const { formatMessage } = this.props.intl;

    let textClass = '';
    let title = null;
    let reportElement = '';
    if (this.props.report_available) {
      textClass = 'text-muted';
      title = formatMessage(messages.openCompnyReportIconLabel);
      reportElement = (
        <a
          title={title}
          aria-label={title}
          href={getReportURL(
            this.props.company_resource_id,
            this.props.filename,
            this.props.intl.locale,
            false,
            this.props.hasCombinedReport
          )}
          target="_blank"
          rel="noopener noreferrer"
          onClick={this.handleOnClick.bind(this)}
        >
          {this.renderIcon(textClass)}
        </a>
      );
    } else if (!this.props.report_available) {
      textClass = 'text-muted-more';
      title = formatMessage(messages.companyReportMissingIconLabel);
      reportElement = (
        <span title={title} aria-label={title}>
          {this.renderIcon(textClass)}
        </span>
      );
    }
    return reportElement;
  }
}

export default injectIntl(ReportIconComponent);
