import { defineMessages } from 'react-intl';

const LoadingMessages = defineMessages({
  messageEmpty: {
    id: 'loading.empty',
    description: 'Empty result message',
    defaultMessage: 'No items found.',
  },
  messageLoading: {
    id: 'loading.loading',
    description: 'Loading message',
    defaultMessage: 'Loading...',
  },
  messageFailed: {
    id: 'loading.failed',
    description: 'Failed to load message',
    defaultMessage: 'Loading failed.',
  },
  messageInitial: {
    id: 'loading.initial',
    description: 'Initial message when user has not done anything',
    defaultMessage: 'Please enter a query',
  },
});

export default LoadingMessages;
