import PropTypes from 'prop-types';
import React from 'react';
import { Dropdown, DropdownToggle, DropdownMenu } from 'reactstrap';
import classNames from 'classnames';
import { stopPropagation } from '../../../helpers/EventHandlers';

class ContextualMenuComponent extends React.Component {
  static get propTypes() {
    return {
      id: PropTypes.string,
      classNames: PropTypes.string,
      toggler: PropTypes.node,
      toggleCallback: PropTypes.func,
      children: PropTypes.node,
      disabled: PropTypes.bool,
    };
  }

  constructor(props) {
    super(props);
    this.state = {
      isMenuExpanded: false,
    };
  }

  toggleDropdown() {
    if (this.props.disabled) {
      return;
    }
    const menuWillBeExpanded = !this.state.isMenuExpanded;
    this.setState({
      isMenuExpanded: !this.state.isMenuExpanded,
    });
    if (this.props.toggleCallback) {
      this.props.toggleCallback(menuWillBeExpanded);
    }
  }

  someChildrenVisible() {
    return React.Children.toArray(this.props.children).some(c => c.props.visible);
  }

  renderToggler() {
    if (!this.props.toggler) {
      return <i className="fa fa-caret-down" aria-hidden="true" />;
    }
    return this.props.toggler;
  }

  render() {
    if (!this.someChildrenVisible()) return null;

    return (
      <Dropdown
        id={this.props.id}
        className={classNames(['context-menu-handle open', this.props.classNames])}
        toggle={this.toggleDropdown.bind(this)}
        isOpen={this.state.isMenuExpanded}
      >
        <DropdownToggle tag="div" onClick={stopPropagation}>
          {this.renderToggler()}
        </DropdownToggle>
        <DropdownMenu>{this.props.children}</DropdownMenu>
      </Dropdown>
    );
  }
}

export default ContextualMenuComponent;
