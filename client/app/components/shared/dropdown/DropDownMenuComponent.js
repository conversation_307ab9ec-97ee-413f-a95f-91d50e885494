/* eslint-disable max-len */
import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';

import ContextualMenuComponent from './ContextualMenuComponent';
import ContextualMenuItem from './ContextualMenuItem';
import { intlPropType } from '../../i18n/IntlPropTypes';

class DropdownMenuComponent extends React.Component {
  static get propTypes() {
    return {
      id: PropTypes.string,
      options: PropTypes.array.isRequired,
      callback: PropTypes.func,
      callbackParams: PropTypes.array,
      toggler: PropTypes.node,
      toggleCallback: PropTypes.func,
      disabled: PropTypes.bool,
      className: PropTypes.string,
      intl: intlPropType.isRequired,
    };
  }

  menuItemClicked(item) {
    if (this.props.callback && !this.props.disabled) {
      return this.props.callback(item, ...(this.props.callbackParams || []));
    }
    return false;
  }

  render() {
    const { formatMessage } = this.props.intl;
    const menuItems = this.props.options.map(item => (
      <ContextualMenuItem
        key={item.value}
        onClick={() => this.menuItemClicked(item.value)}
        visible
        className={this.props.className + item.value}
      >
        {formatMessage(item.title)}
        <br />
        <span className="sub-label text-muted">{formatMessage(item.description)}</span>
      </ContextualMenuItem>
    ));

    return (
      <ContextualMenuComponent
        id={this.props.id}
        classNames="wide-menu role-menu"
        toggler={this.props.toggler}
        toggleCallback={this.props.toggleCallback}
        disabled={this.props.disabled}
      >
        {menuItems}
      </ContextualMenuComponent>
    );
  }
}

export default injectIntl(DropdownMenuComponent);
