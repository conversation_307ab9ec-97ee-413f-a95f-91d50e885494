/* eslint-disable no-lonely-if */
// React-to-print code was taken from
// https://github.com/gregnb/react-to-print/blob/v2.2.2/src/index.tsx
// and converted to ReactToPrint.js with specific fixes made for BOL-2398

import PropTypes from 'prop-types';
import React from 'react';
import { findDOMNode } from 'react-dom';

class ReactToPrint extends React.Component {
  static get propTypes() {
    return {
      trigger: PropTypes.func.isRequired,
      content: PropTypes.func.isRequired,
      copyStyles: PropTypes.bool,
      onBeforeGetContent: PropTypes.func,
      onBeforePrint: PropTypes.func,
      onAfterPrint: PropTypes.func,
      onPrintError: PropTypes.func,
      pageStyle: PropTypes.string,
      bodyClass: PropTypes.string,
      removeAfterPrint: PropTypes.bool,
      onHandleClick: PropTypes.func,
    };
  }

  static defaultProps = {
    copyStyles: true,
    removeAfterPrint: false,
  };

  constructor() {
    super();
    this.triggerRef = null;
    this.linkTotal = 0;
    this.linksLoaded = null;
    this.linksErrored = null;
    this.removeTimeout = null;
  }

  setRef = ref => {
    this.triggerRef = ref;
  };

  startPrint = (target, onAfterPrint) => {
    const { removeAfterPrint } = this.props;

    setTimeout(() => {
      target.contentWindow.focus();
      target.contentWindow.print();
      if (onAfterPrint) {
        onAfterPrint();
      }
      if (removeAfterPrint) {
        // The user may have removed the iframe in `onAfterPrint`
        if (document.getElementById('printWindow')) {
          document.body.removeChild(document.getElementById('printWindow'));
        }
      }
    }, 500);
  };

  triggerPrint = target => {
    const { onAfterPrint, onBeforePrint, onPrintError } = this.props;

    if (onBeforePrint) {
      const onBeforePrintOutput = onBeforePrint();
      if (onBeforePrintOutput && typeof onBeforePrintOutput.then === 'function') {
        onBeforePrintOutput
          .then(() => {
            this.startPrint(target, onAfterPrint);
          })
          .catch(error => {
            if (onPrintError) {
              onPrintError('onBeforePrint', error);
            }
          });
      } else {
        this.startPrint(target, onAfterPrint);
      }
    } else {
      this.startPrint(target, onAfterPrint);
    }
  };

  handleClick = () => {
    const { onBeforeGetContent, onPrintError, onHandleClick } = this.props;

    if (onHandleClick) {
      onHandleClick();
    }

    setTimeout(() => {
      if (onBeforeGetContent) {
        const onBeforeGetContentOutput = onBeforeGetContent();
        if (onBeforeGetContentOutput && typeof onBeforeGetContentOutput.then === 'function') {
          onBeforeGetContentOutput.then(this.handlePrint).catch(error => {
            if (onPrintError) {
              onPrintError('onBeforeGetContent', error);
            }
          });
        } else {
          this.handlePrint();
        }
      } else {
        this.handlePrint();
      }
    }, 0);
  };

  handlePrint = () => {
    const { bodyClass = '', content, copyStyles = true, pageStyle } = this.props;

    const contentEl = content();

    if (contentEl === undefined) {
      console.error(
        `Refs are not available for stateless components. 
        For "react-to-print" to work only Class based components can be printed`
      );
      return;
    }

    // Clear the previous print content if found
    const printEl = document.querySelector('#react-to-print');
    if (document.body.contains(printEl)) {
      printEl.parentNode.removeChild(printEl);
    }

    const printWindow = document.createElement('iframe');
    printWindow.id = 'react-to-print';
    printWindow.style.position = 'absolute';
    printWindow.style.top = '-1000px';
    printWindow.style.left = '-1000px';

    // eslint-disable-next-line react/no-find-dom-node
    const contentNodes = findDOMNode(contentEl);
    const linkNodes = document.querySelectorAll("link[rel='stylesheet']");

    this.linkTotal = linkNodes.length || 0;
    this.linksLoaded = [];
    this.linksErrored = [];

    const markLoaded = (linkNode, loaded) => {
      if (loaded) {
        this.linksLoaded.push(linkNode);
      } else {
        console.error(
          `"react-to-print" was unable to load a link. It may be invalid. 
          "react-to-print" will continue attempting to print the page. The link the errored was:`,
          linkNode
        );
        this.linksErrored.push(linkNode);
      }

      // We may have errors, but attempt to print anyways - maybe they are trivial and the user will
      // be ok ignoring them
      if (this.linksLoaded.length + this.linksErrored.length === this.linkTotal) {
        this.triggerPrint(printWindow);
      }
    };

    printWindow.onload = () => {
      // Fix for BOL-2398:
      // Some browsers (at least IE, Edge) tend to execute onload event repeatedly.
      // This leads to memory issues or intermittent print dialog open failures
      // Let's clear the handler to ensure it would get called once.
      printWindow.onload = null;

      const domDoc = printWindow.contentDocument || printWindow.contentWindow.document;
      const srcCanvasEls = contentNodes.querySelectorAll('canvas');

      domDoc.open();
      domDoc.write(contentNodes.outerHTML);
      domDoc.close();
      domDoc.body.setAttribute('id', 'bol-body');
      domDoc.documentElement.setAttribute('id', 'bol-html');

      /* remove date/time from top */
      const defaultPageStyle =
        pageStyle === undefined
          ? `@page { size: auto;  margin: 0mm; } 
          @media print { body { -webkit-print-color-adjust: exact; } }`
          : pageStyle;

      const styleEl = domDoc.createElement('style');
      styleEl.appendChild(domDoc.createTextNode(defaultPageStyle));
      domDoc.head.appendChild(styleEl);

      if (bodyClass.length) {
        domDoc.body.classList.add(bodyClass);
      }

      const canvasEls = domDoc.querySelectorAll('canvas');
      for (let i = 0, canvasElsLen = canvasEls.length; i < canvasElsLen; ++i) {
        const node = canvasEls[i];
        node.getContext('2d').drawImage(srcCanvasEls[i], 0, 0);
      }

      if (copyStyles !== false) {
        const headEls = document.querySelectorAll("style, link[rel='stylesheet']");

        for (let i = 0, headElsLen = headEls.length; i < headElsLen; ++i) {
          const node = headEls[i];
          if (node.tagName === 'STYLE') {
            const newHeadEl = domDoc.createElement(node.tagName);
            const { sheet } = node;

            if (sheet) {
              let styleCSS = '';
              // NOTE: for-of is not supported by IE
              for (let j = 0, cssLen = sheet.cssRules.length; j < cssLen; ++j) {
                if (typeof sheet.cssRules[j].cssText === 'string') {
                  styleCSS += `${sheet.cssRules[j].cssText}\r\n`;
                }
              }
              newHeadEl.setAttribute('id', `react-to-print-${i}`);
              newHeadEl.appendChild(domDoc.createTextNode(styleCSS));
              domDoc.head.appendChild(newHeadEl);
            }
          } else {
            // Many browsers will do all sorts of weird things if they encounter an
            // empty `href` tag (which is invalid HTML). Some will attempt to load the
            // current page. Some will attempt to load the page"s parent directory.
            // These problems can cause `react-to-print` to stop  without any error
            // being thrown. To avoid such problems we simply do not attempt to load
            // these links.
            if (node.hasAttribute('href') && !!node.getAttribute('href')) {
              const newHeadEl = domDoc.createElement(node.tagName);

              // node.attributes has NamedNodeMap type that is not an Array and can be
              // iterated only via direct [i] access
              for (let j = 0, attrLen = node.attributes.length; j < attrLen; ++j) {
                const attr = node.attributes[j];
                newHeadEl.setAttribute(attr.nodeName, attr.nodeValue);
              }

              newHeadEl.onload = markLoaded.bind(null, newHeadEl, true);
              newHeadEl.onerror = markLoaded.bind(null, newHeadEl, false);
              domDoc.head.appendChild(newHeadEl);
            } else {
              console.warn(
                `"react-to-print" encountered a <link> tag with an empty "href" attribute. 
                In addition to being invalid HTML, this can cause problems in many browsers, 
                and so the <link> was not loaded. The <link> is:`,
                node
              ); // tslint:disable-line max-line-length no-console
              // `true` because we"ve already shown a warning for this
              markLoaded(node, true);
            }
          }
        }
      }

      if (this.linkTotal === 0 || copyStyles === false) {
        this.triggerPrint(printWindow);
      }
    };

    if (document.getElementById('printWindow')) {
      document.body.removeChild(document.getElementById('printWindow'));
    }
    document.body.appendChild(printWindow);
  };

  render() {
    const { trigger } = this.props;

    return React.cloneElement(trigger(), {
      onClick: (e)=> { this.handleClick(); e.preventDefault()},
      ref: this.setRef,
    });
  }
}

export default ReactToPrint;
