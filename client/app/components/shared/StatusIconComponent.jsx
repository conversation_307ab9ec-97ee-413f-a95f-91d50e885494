import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { injectIntl } from 'react-intl';
import StatusMessages from '../shared/StatusMessages';
import { featureActive } from '../../helpers/FeatureFlags';
import { intlPropType } from '../i18n/IntlPropTypes';

const StatusIcons = {
  attention: 'info_outline',
  incomplete: 'help',
  investigate: 'report_problem',
  ok: 'done',
  stop: 'pan_tool',
};

class StatusIconComponent extends React.Component {
  static get propTypes() {
    return {
      status: PropTypes.oneOf(['stop', 'investigate', 'incomplete', 'attention', 'ok']),
      additionalClasses: PropTypes.array,
      intl: intlPropType.isRequired,
    };
  }
  render() {
    const { formatMessage } = this.props.intl;
    const { status, additionalClasses } = this.props;

    if (!status) return null;

    if (featureActive('use_stv_theme')) {
      return (
        <i
          className={classNames(`icon-status-${status}-stv`, additionalClasses)}
          title={formatMessage(StatusMessages[status])}
        />
      );
    }

    return (
      <i
        className={classNames(
          'material-icons',
          'text-large',
          `status-${status}`,
          additionalClasses
        )}
        title={formatMessage(StatusMessages[status])}
      >
        {StatusIcons[status]}
      </i>
    );
  }
}

export default injectIntl(StatusIconComponent);
