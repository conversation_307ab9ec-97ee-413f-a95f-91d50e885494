import React from 'react';
import PropTypes from 'prop-types';

class FormGroup extends React.Component {
  static get propTypes() {
    return {
      className: PropTypes.string,
      row: PropTypes.bool,
      id: PropTypes.string,
      role: PropTypes.string,
      onClick: PropTypes.func,
      children: PropTypes.node,
    };
  }

  render() {
    let cssClass = 'form-group';

    if (this.props.row) {
      cssClass += ' row';
    }

    if (this.props.className) {
      cssClass += ' ' + this.props.className;
    }

    return (
      <div
        id={this.props.id}
        className={cssClass}
        role={this.props.role}
        onClick={this.props.onClick}
      >
        {this.props.children}
      </div>
    );
  }
}

export default FormGroup;
