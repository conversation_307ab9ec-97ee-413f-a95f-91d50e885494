import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';

import WizardGuideComponent from './WizardGuideComponent';

class WizardComponent extends React.Component {
  static get propTypes() {
    return {
      activeStep: PropTypes.string.isRequired,
      title: PropTypes.node.isRequired,
      handleClose: PropTypes.func.isRequired,
      children: PropTypes.node,
    };
  }

  constructor(props) {
    super(props);
    this.onKeyUp = this.onKeyUp.bind(this);
  }

  componentDidMount() {
    document.addEventListener('keyup', this.onKeyUp);
  }

  componentWillUnmount() {
    document.removeEventListener('keyup', this.onKeyUp);
  }

  onKeyUp(event) {
    if (event.keyCode === 27) {
      event.preventDefault();
      this.props.handleClose();
    }
  }

  steps() {
    return React.Children.map(this.props.children, (child, index) => ({
      num: index + 1,
      step: child.props.step,
      title: child.props.title,
      component: child,
    }));
  }

  render() {
    const steps = this.steps();
    const { activeStep, title } = this.props;
    const activeStepComponent = steps.find(elem => elem.step === activeStep);
    return (
      <div>
        <div
          id="wizard-modal"
          className="modal wizard-modal"
          tabIndex="-1"
          data-focus="false"
          aria-hidden="true"
          style={{ display: 'block' }}
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <div className="container">
                  <div className="row">
                    <div
                      className={classNames(
                        'col-3',
                        'd-flex',
                        'align-content-center',
                        'justify-content-start',
                        'no-col-gutter-left'
                      )}
                    >
                      <h3 className="modal-title pointer-events-none">{title}</h3>
                    </div>
                    <div
                      className={classNames(
                        'col-6',
                        'steps',
                        'd-flex',
                        'align-content-center',
                        'justify-content-center'
                      )}
                    >
                      <WizardGuideComponent steps={steps} activeStep={activeStep} />
                    </div>
                    <div
                      className={classNames(
                        'col-3',
                        'd-flex',
                        'align-content-center',
                        'justify-content-end',
                        'no-col-gutter-right'
                      )}
                    >
                      <button
                        id="close_wizard_title_button"
                        role="button"
                        type="button"
                        className="close"
                        data-dismiss="modal"
                        aria-label="Close"
                        onClick={this.props.handleClose}
                      >
                        <div aria-hidden="true" className="bulk-import-close-button">
                          ×
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-body">{activeStepComponent.component}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default WizardComponent;
