import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';

import LoadingMessages from './LoadingMessages';
import Spinner from '../shared/Spinner';
import { intlPropType } from '../i18n/IntlPropTypes';

class ListMessageRowComponent extends React.Component {
  render() {
    const { formatMessage } = this.props.intl;
    if (this.props.message === 'messageLoading') {
      return (
        <tr>
          <td colSpan={this.props.colSpan} className="pointer-events-none">
            <Spinner />
          </td>
        </tr>
      );
    }
    return (
      <tr>
        <td
          colSpan={this.props.colSpan}
          className={this.props.className || 'text-muted pointer-events-none'}
        >
          {formatMessage(LoadingMessages[this.props.message])}
        </td>
      </tr>
    );
  }
}
ListMessageRowComponent.propTypes = {
  colSpan: PropTypes.number,
  className: PropTypes.string,
  message: PropTypes.node.isRequired,
  intl: intlPropType.isRequired
};

ListMessageRowComponent.defaultProps = {
  className: 'text-muted',
};

export default injectIntl(ListMessageRowComponent);
