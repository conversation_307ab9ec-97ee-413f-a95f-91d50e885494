import React from 'react';
import classNames from 'classnames';
import { injectIntl, defineMessages } from 'react-intl';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  idCardIcon: {
    id: 'idCardIconLabel',
    description: 'Visitor node ID icon label',
    defaultMessage: 'Entrance Period',
  },
});

class IdCardIconComponent extends React.Component {
  static get propTypes() {
    return {
      intl: intlPropType.isRequired
    };
  }
  render() {
    const { formatMessage } = this.props.intl;

    return (
      <i className={classNames('fa', 'fa-id-card')} title={formatMessage(messages.idCardIcon)} />
    );
  }
}

export default injectIntl(IdCardIconComponent);
