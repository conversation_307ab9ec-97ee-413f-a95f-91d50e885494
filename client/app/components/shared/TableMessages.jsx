import { defineMessages } from 'react-intl';

const TableMessages = defineMessages({
  no_data_to_display: {
    id: 'companyDetails.table.noDataToDisplay',
    description: 'Message for empty table',
    defaultMessage: 'There is no data to display',
  },
  status: {
    id: 'relatedProjects.statusLabel',
    description: 'Label for Status column in related projects',
    defaultMessage: 'Status',
  },
  preannouncement: {
    id: 'relatedProjects.preannouncement',
    description: 'Label for Preannouncement column in related projects',
    defaultMessage: 'Preannouncement',
  },
  project: {
    id: 'relatedProjects.projectLabel',
    description: 'Label for Project column in related projects',
    defaultMessage: 'Project',
  },
  startDate: {
    id: 'relatedProjects.startDateLabel',
    description: 'Label for Start date column in related projects',
    defaultMessage: 'Start date',
  },
  endDate: {
    id: 'relatedProjects.endDateLabel',
    description: 'Label for End date column in related projects',
    defaultMessage: 'End date',
  },
  organization: {
    id: 'relatedSuppliers.supplierLabel',
    description: 'Label for Supplier column in related suppliers',
    defaultMessage: 'Supplier',
  },
  report: {
    id: 'relatedSuppliers.reportLabel',
    description: 'Label for Report column in related suppliers',
    defaultMessage: 'Report',
  },
  buyer: {
    id: 'relatedSuppliers.buyerLabel',
    description: 'Label for Buyer column in related suppliers',
    defaultMessage: 'Buyer',
  },
  contacts: {
    id: 'relatedSuppliers.contactPerson',
    description: 'Label for Contact Person in project supplier list',
    defaultMessage: 'Contact Person',
  },
  comments: {
    id: 'relatedSuppliers.commentsLabel',
    description: 'Label for comments in project suppliers list',
    defaultMessage: 'Comments',
  },
  role: {
    id: 'projectUsers.roleLabel',
    description: 'Label for role in project users.',
    defaultMessage: 'Role',
  },
  userInfo: {
    id: 'projectUsers.userInfoLabel',
    description: 'Label for user information in project users',
    defaultMessage: 'User information',
  },
  company: {
    id: 'projectUsers.companyLabel',
    description: 'Label for company information in project users',
    defaultMessage: 'Company',
  },
  notify: {
    id: 'projectUsers.notifyLabel',
    description: 'Label for notify in project users',
    defaultMessage: 'Notify',
  },
  actions: {
    id: 'projectUsers.actionsLabel',
    description: 'Label for actions in project users',
    defaultMessage: 'Actions',
  },
});

export default TableMessages;
