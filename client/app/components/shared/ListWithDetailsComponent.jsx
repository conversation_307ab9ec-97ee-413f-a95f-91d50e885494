import React from 'react';
import PropTypes from 'prop-types';
import Sticky from 'react-stickynode';

const DetailsDiv = ({children}) => <div className="col-sm-12 col-md-8 col-lg-9">
  <Sticky
    enabled
    top={120}
    bottomBoundary=".main"
  >
    <div>{children}</div>
  </Sticky>
</div>;

DetailsDiv.propTypes = {
  children: PropTypes.node,
};

const List = ({ children }) => <>{children}</>;
const Details = ({ children }) => <>{children}</>;

List.propTypes = {
  children: PropTypes.node,
};
Details.propTypes = {
  children: PropTypes.node,
};

const ListWithDetailsComponent = ({ children }) => {
  const childrenArray = React.Children.toArray(children);

  const listChild = childrenArray
    .find(child => child.type === List);
  const detailsChild = childrenArray
    .find(child => child.type === Details);

  const hasDetailsChildren = detailsChild
    && React.Children.count(detailsChild.props.children) > 0;

  const showFullList = !detailsChild || !hasDetailsChildren;

  const narrowClass = 'hidden-xs hidden-sm col-md-4 col-lg-3';
  const wideClass = 'col-12';

  return (
    <div className="card noborder">
      <div className="row">
        <div className={showFullList ? wideClass : narrowClass}>
          {listChild}
        </div>
        {!showFullList && <DetailsDiv>{detailsChild}</DetailsDiv>}
      </div>
    </div>
  );
};

ListWithDetailsComponent.propTypes = {
  children: PropTypes.node,
};

export { ListWithDetailsComponent, List, Details };
