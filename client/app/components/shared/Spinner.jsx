import PropTypes from 'prop-types';
import React from 'react';

class Spinner extends React.Component {
  static get propTypes() {
    return {
      id: PropTypes.string,
      className: PropTypes.string,
    };
  }
  static get defaultProps() {
    return {
      className: 'text-center w-100',
      id: '',
    };
  }

  render() {
    return (
      <div className={this.props.className} id={this.props.id}>
        <i className="fa fa-spin fa-spinner fa-lg text-primary loader" />
      </div>
    );
  }
}

export default Spinner;
