import PropTypes from 'prop-types';
import React from 'react';
import { injectIntl } from 'react-intl';

class SupplierRolesSelect extends React.Component {
  static get propTypes() {
    return {
      className: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      defaultValue: PropTypes.string,
      id: PropTypes.string,
      roleOptions: PropTypes.array,
      reference: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
    };
  }

  static get defaultProps() {
    return {
      defaultValue: '',
      roleOptions: [],
    };
  }

  renderSupplierRoleOptions(roles, formatMessage) {
    return roles.map(role => (
      <option key={role.value} value={role.value}>
        {formatMessage(role.title)}
      </option>
    ));
  }

  render() {
    const { formatMessage } = this.props.intl;
    const supplierRoleOptions = this.renderSupplierRoleOptions(
      this.props.roleOptions,
      formatMessage
    );

    return (
      <select
        className={this.props.className}
        id={this.props.id}
        name={this.props.name}
        ref={this.props.reference}
        defaultValue={this.props.defaultValue}
        disabled={this.props.roleOptions.length < 2}
        role="button"
      >
        {supplierRoleOptions}
      </select>
    );
  }
}

export default injectIntl(SupplierRolesSelect);
