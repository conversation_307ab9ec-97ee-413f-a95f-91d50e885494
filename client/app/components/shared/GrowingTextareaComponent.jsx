import React, { useState, forwardRef, useEffect } from 'react';
import PropTypes from 'prop-types';

const GrowingTextareaComponent = forwardRef((props, ref) => {
  // Growing textarea component behaves like a textarea, but with some CSS magic
  // it grows with the input
  const [input, setInput] = useState(props.value ?? '')
  const [rows, setRows] = useState(props.value?.length ? 4 : 1)

  useEffect(() => {
    if (props.value != null) {
      setInput(props.value + '\n' ?? '');
      setRows(props.value?.length ? 4 : 1);
    }
  }, [props.value])

  const onInput = (e) => {
    props.onInput?.(e);
    setInput(e.target.value + '\n')
  }
  const onFocus = (e) => {
    props.onFocus?.(e)
    setRows(4)
  }
  const onBlur = (e) => {
    props.onBlur?.(e)
    if (input.trim().length === 0) {
      setRows(1)
    }
  }

  return <div className="grow-container">
    <textarea
      {...props}
      ref={ref}
      className={(props.className ?? '') + ' form-control'} 
      rows={rows}
      onFocus={onFocus}
      onBlur={onBlur}
      onInput={onInput}
      role="textbox"
    ></textarea>
    <div className="grow-box form-control">{input}</div>
  </div>
});

GrowingTextareaComponent.propTypes = {
  onInput: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  className: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  name: PropTypes.string,
  id: PropTypes.string,
  disabled: PropTypes.bool,
};

export default GrowingTextareaComponent;
