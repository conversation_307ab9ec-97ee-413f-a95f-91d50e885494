import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';

class SearchableField extends React.Component {
  static get propTypes() {
    return {
      id: PropTypes.string,
      name: PropTypes.string,
      className: PropTypes.string.isRequired,
      required: PropTypes.bool,
      label: PropTypes.string,
      placeholder: PropTypes.string,
      input_value: PropTypes.string,
      onChange: PropTypes.func,
      renderErrors: PropTypes.oneOfType([PropTypes.string, PropTypes.func, PropTypes.element]),
      renderExtra: PropTypes.oneOfType([PropTypes.string, PropTypes.func, PropTypes.element]),
      reference: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
      children: PropTypes.node,
      disabled: PropTypes.bool,
    };
  }

  static get defaultProps() {
    return {
      id: 'search_id',
      name: 'search_id',
      label: '',
      placeholder: '',
      required: false,
      disabled: false,
    };
  }

  render() {
    let required = null;
    if (this.props.required) {
      required = <span className="text--red">*&nbsp;</span>;
    }

    return (
      <div className={this.props.className}>
        <label className="form-control-label form-control-label-sm" htmlFor="search_id">
          {required}
          {this.props.label}
        </label>
        <div className="input-group">
          <input
            className={classNames('form-control', 'form-control-sm', {
              'border-right': this.props.disabled,
            })}
            type="search"
            id={this.props.id}
            name={this.props.name}
            ref={this.props.reference}
            placeholder={this.props.placeholder}
            value={this.props.input_value}
            onChange={this.props.onChange}
            disabled={this.props.disabled}
          />
          {!this.props.disabled && this.props.children}
        </div>
        {this.props.renderErrors}
        {this.props.renderExtra}
      </div>
    );
  }
}

export default SearchableField;
