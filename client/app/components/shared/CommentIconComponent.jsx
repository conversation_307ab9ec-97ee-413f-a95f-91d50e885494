import PropTypes from 'prop-types';
import React from 'react';
import { defineMessages, injectIntl } from 'react-intl';
import { intlPropType } from '../i18n/IntlPropTypes';

const messages = defineMessages({
  unreadAriaLabel: {
    id: 'relatedSuppliers.unreadCommentAriaLabel',
    description: 'Label for unread comment icon',
    defaultMessage: 'Unread comments',
  },
  readAriaLabel: {
    id: 'relatedSuppliers.readCommentAriaLabel',
    description: 'Label for read comment icon',
    defaultMessage: 'Read comments',
  },
});

class CommentIconComponent extends React.Component {
  static get propTypes() {
    return {
      has_comment: PropTypes.bool,
      has_unread_comment: PropTypes.bool,
      intl: intlPropType.isRequired,
    };
  }

  static get defaultProps() {
    return {
      has_comment: false,
      has_unread_comment: false,
    };
  }

  render() {
    const { formatMessage } = this.props.intl;
    const { has_comment, has_unread_comment } = this.props;

    if (!has_comment) {
      return null;
    }

    const iconClass = has_unread_comment ? 'fa-comments' : 'fa-comments-o';
    const style = has_unread_comment ? { color: '#32A1D1' } : { color: '#999999' };
    const ariaLabel = has_unread_comment ? messages.unreadAriaLabel : messages.readAriaLabel;

    return (
      <i style={style} aria-label={formatMessage(ariaLabel)} className={`fa ${iconClass}`}></i>
    );
  }
}

export default injectIntl(CommentIconComponent);
