import PropTypes from 'prop-types';
import React from 'react';
import { Tab<PERSON>ontent, TabPane, Nav, NavItem, NavLink, Row, Col } from 'reactstrap';

import classNames from 'classnames';

class TabsComponent extends React.Component {
  static get propTypes() {
    return {
      activeTab: PropTypes.number,
      rowClass: PropTypes.string,
      children: PropTypes.node,
    };
  }

  constructor(props) {
    super(props);
    this.state = { activeTab: this.getTab(props.activeTab) };
    this.toggle = this.toggle.bind(this);
  }

  componentDidUpdate(prevProps) {
    const newIndex = this.getOriginalIndex(this.props.activeTab, this.props.children);
    const oldIndex = this.getOriginalIndex(prevProps.activeTab, prevProps.children);
    if (oldIndex !== newIndex) {
      this.toggle(this.props.activeTab);
    }
  }

  getOriginalIndex(tab, children) {
    let n = 0;
    return children.findIndex(t => t && t.props.enabled && n++ === tab);
  }

  getTab(defaultTab) {
    if (!this.props.children) return null;

    const hasTabs = React.Children.toArray(this.props.children).some(t => t && t.props.enabled);

    if (!hasTabs) return null;
    return defaultTab || 0;
  }

  toggle(tab) {
    if (this.state.activeTab === tab) return;

    this.setState({
      activeTab: tab,
    });
  }

  renderTab = (tab, index) => (
    <NavItem key={index}>
      <NavLink
        className={classNames(
          {
            active: this.state.activeTab === index,
          },
          tab.props.navClass
        )}
        onClick={() => {
          this.toggle(index);
        }}
        href="#"
      >
        {tab.props.title}
      </NavLink>
    </NavItem>
  );

  renderPane = (tab, index) => (
    <TabPane key={index} tabId={index}>
      <Row>
        <Col xl="12">{tab}</Col>
      </Row>
    </TabPane>
  );

  render() {
    const children = React.Children.toArray(this.props.children).filter(t => t && t.props.enabled);

    if (!children.length) return null;

    return (
      <div className="tabs-view">
        <Nav tabs className={this.props.rowClass || null}>
          {children.map(this.renderTab)}
        </Nav>

        <TabContent
          activeTab={this.state.activeTab}
          className={classNames('u-border', this.props.rowClass || '')}
        >
          {children.map(this.renderPane)}
        </TabContent>
      </div>
    );
  }
}

export default TabsComponent;
