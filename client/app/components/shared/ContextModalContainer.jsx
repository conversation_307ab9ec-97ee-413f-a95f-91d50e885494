import React from 'react';
import { contextModalContainerStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';

class ContextModalContainer extends React.Component {
  constructor(props) {
    super(props);
    this.contextModalSub = new StoreSubscription(
      contextModalContainerStore,
      this.stateChanged.bind(this)
    );
    this.state = this.mapStoreToState(contextModalContainerStore.getState());
  }

  componentDidMount() {
    this.contextModalSub.activate();
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.state.isOpen !== prevState.isOpen) {
      if (this.state.isOpen) {
        document.body.classList.add('modal-open');
      } else {
        document.body.classList.remove('modal-open');
      }
    }
  }

  componentWillUnmount() {
    this.contextModalSub.deactivate();
    document.body.classList.remove('modal-open');
  }

  mapStoreToState(storeState) {
    return {
      isOpen: storeState.isOpen,
      childComponent: storeState.childComponent,
    };
  }

  stateChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  render() {
    const { childComponent, isOpen } = this.state;
    if (isOpen) {
      return (
        <div
          id="context-modal-container"
          className="modal context-container"
          role="dialog"
          aria-labelledby="modal-container-label"
          aria-hidden="false"
          data-backdrop="false"
          tabIndex="-1"
          data-focus="false"
        >
          {childComponent}
        </div>
      );
    }
    return null;
  }
}

export default ContextModalContainer;
