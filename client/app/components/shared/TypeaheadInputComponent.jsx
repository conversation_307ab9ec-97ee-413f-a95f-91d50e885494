import PropTypes from 'prop-types';
import React from 'react';
import { Typeahead } from 'react-bootstrap-typeahead';
import Highlight from 'react-highlighter';

class TypeaheadInputComponent extends React.Component {
  static get propTypes() {
    return {
      // Input field name
      name: PropTypes.string.isRequired,
      // Select option change callback
      onChange: PropTypes.func.isRequired,
      // Text to display when there ar no options in list
      emptyLabel: PropTypes.string.isRequired,
      // Invoked when the input value is cleared
      onInputClear: PropTypes.func.isRequired,
      // Disable input
      disabled: PropTypes.bool,
      // Select options
      // array[{id: 1, label: 'demo label', 'email': '<EMAIL>'}]
      options: PropTypes.array,
      // Selected option
      selected: PropTypes.array,
      // Multiple selections
      multiple: PropTypes.bool,
      // Placeholder
      placeholder: PropTypes.string,
      // Max results to display
      maxResults: PropTypes.number,
      // Custom user class
      className: PropTypes.string,
    };
  }

  static get getDefaultProps() {
    return {
      disabled: false,
      multiple: false,
      options: [],
      selected: [],
      placeholder: '',
      maxResults: 1000,
      className: '',
    };
  }

  clear() {
    this.typeaheadInput.getInstance().clear();
    this.props.onInputClear();
  }

  renderMenuItemChildren(option, props) {
    return (
      <div
        id={`stv-typeahead-option-${option.id}`}
        className={option.email ? 'contains-email' : null}
      >
        {option.preElement}
        <Highlight search={props.text}>{option.label}</Highlight>
        {option.email ? (
          <span>
            <Highlight search={props.text}>{option.email}</Highlight>
          </span>
        ) : null}
        {option.additional_text ? <br /> : null}

        {option.additional_text ? (
          <span>
            <Highlight search={props.text}>{option.additional_text}</Highlight>
          </span>
        ) : null}
        {option.postElement}
      </div>
    );
  }

  render() {
    let className = 'stv-typeahead';

    if (this.props.className && this.props.className.length) {
      className += ` ${this.props.className}`;
    }

    return (
      <div id={this.props.name} className={className}>
        <Typeahead
          id={`typeahead-${this.props.name}`}
          multiple={this.props.multiple}
          ref={typeaheadInput => {
            this.typeaheadInput = typeaheadInput;
          }}
          disabled={this.props.disabled}
          onChange={this.props.onChange}
          options={this.props.options}
          selected={this.props.selected}
          emptyLabel={this.props.emptyLabel}
          maxResults={this.props.maxResults}
          placeholder={this.props.placeholder}
          filterBy={['email']}
          renderMenuItemChildren={(option, props) => this.renderMenuItemChildren(option, props)}
        />
        <button
          type="button"
          role="button"
          id={`${this.props.name}-close`}
          className="clear-stv-typeahead"
          onClick={() => {
            this.clear();
          }}
        >
          <i className="fa fa-times" aria-hidden="true" />
        </button>
      </div>
    );
  }
}

export default TypeaheadInputComponent;
