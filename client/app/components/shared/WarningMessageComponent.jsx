/* eslint-disable max-len */
import React from 'react';
import FormattedHTMLMessage from '../i18n/FormattedHtmlMessage'

import { subscriptionStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
/* eslint-enable max-len */

class WarningMessageComponent extends React.Component {
  constructor(props) {
    super(props);
    this.subscriptionSubscription = new StoreSubscription(
      subscriptionStore,
      this.subscriptionChanged.bind(this)
    );
    const storeState = subscriptionStore.getState();
    this.state = this.mapStoreToState(storeState);
  }

  componentDidMount() {
    this.subscriptionSubscription.activate();
  }

  componentWillUnmount() {
    this.subscriptionSubscription.deactivate();
  }

  mapStoreToState(storeState) {
    return {
      subscription: storeState.subscription,
    };
  }

  subscriptionChanged(storeState) {
    this.setState(this.mapStoreToState(storeState));
  }

  render() {
    const { subscription } = this.state;
    const subscribed = subscription && subscription.active;
    if (subscribed) {
      return null;
    }

    return (
      <div id="search_warning_message" className="alert alert-warning mt-5 mb-5">
        <div className="row">
          <div className="col-sm-9 mb-4 align-self-center">
            <FormattedHTMLMessage
              id="search.message.noSubscription"
              description="Message shown when not subscribed"
              defaultMessage="You are currently using the free version"
            />
          </div>
          <div className="col-sm-3 pl-6">
            <div className="row">
              <a className="btn btn-primary" href="/#/subscription">
                <FormattedHTMLMessage
                  id="search.message.subscribeButton"
                  description="Link to subscribtion"
                  defaultMessage="Subscribe to Report PRO"
                />
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default WarningMessageComponent;
