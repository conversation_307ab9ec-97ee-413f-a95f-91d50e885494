import { defineMessages } from 'react-intl';

const StatusMessages = defineMessages({
  created: {
    id: 'paStatusLabel.created',
    description: 'Preannouncement status: Register',
    defaultMessage: 'Register',
  },
  PACreated: {
    id: 'paStatusLabel.pa_created',
    description: 'Preannouncement status: Created',
    defaultMessage: 'Created',
  },
  registered: {
    id: 'paStatusLabel.registered',
    description: 'Preannouncement status: Review',
    defaultMessage: 'Review',
  },
  confirmed: {
    id: 'paStatusLabel.confirmed',
    description: 'Preannouncement status: Confirmed',
    defaultMessage: 'Confirmed',
  },
  rejected: {
    id: 'paStatusLabel.rejected',
    description: 'Preannouncement status: Rejected',
    defaultMessage: 'Rejected',
  },
  waitingForRegistration: {
    id: 'paStatusLabel.waitingForRegistration',
    description: 'Preannouncement status: Waiting for registration',
    defaultMessage: 'Waiting for registration',
  },
  inReview: {
    id: 'paStatusLabel.inReview',
    description: 'Preannouncement status: In review',
    defaultMessage: 'In review',
  },
});

export default StatusMessages;
