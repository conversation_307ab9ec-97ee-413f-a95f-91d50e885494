import React from 'react';
import PropTypes from 'prop-types';

const CSAccountActivateStep = props => (
  <div className="content-block">
    <div className="number">{props.stepNumber}</div>
    <div className="content-block__left">
      <img className="img-icon" data-testid="step-icon" src={props.icon} alt="" />
    </div>
    <div className="content-block__right">
      <h4 className="content-item--heading">{props.title}</h4>
      <div className="spacer" />
      <p className="text--medium">{props.description}</p>
      <div>{props.children}</div>
    </div>
  </div>
);

CSAccountActivateStep.propTypes = {
  stepNumber: PropTypes.string.isRequired,
  icon: PropTypes.string,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  children: PropTypes.node,
};

export default CSAccountActivateStep;
