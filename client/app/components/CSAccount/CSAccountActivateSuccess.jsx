import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import { useNavigate } from 'react-router-dom';
import Message from '../../components/shared/Message';
import CSAccountButton from './CSAccountButton';

export const CSAccountActivateSuccess = () => {
  const navigate = useNavigate();
  const handleRedirectToBol = () => {
    // redirect to BOL main page
    // TODO: We redirect to `/projects` because redirect to `/` renders BasePage
    // without any children, aka a blank page with tabs. Remove this once proper
    //  redirects for the pages in CS registration flow are implmented.
    navigate('/projects', {replace: true});
  };
  return (
    <div>
      <Message id="cs_activation_success_message" type="success">
        <FormattedMessage
          id="CSAccountActivate.success"
          description="CS account activation success message"
          defaultMessage="CS account activation success"
        />
      </Message>
      <CSAccountButton id="redirect_to_bol_button" onClick={handleRedirectToBol}>
        <FormattedMessage
          id="CSAccountActivate.goToBolService"
          description="Go to BOL service button text"
          defaultMessage="Go to BOL service"
        />
      </CSAccountButton>
    </div>
  );
};

export default injectIntl(CSAccountActivateSuccess);
