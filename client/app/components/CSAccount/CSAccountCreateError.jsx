import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import LocalizedText from '../i18n/LocalizedText';

const CSAccountCreateError = ({
  autoaccountErrorMsgFromCS = null,
  showAccountAlreadyExistsError = false,
  showAccountPendingExistsError = false,
  showAutoaccountError = false,
  showForeignCompanyError = false }) => (
  <div
    id="create_cs_account_error"
    className={classNames(
      {
        card:
          showForeignCompanyError ||
          showAutoaccountError ||
          showAccountAlreadyExistsError,
      },
      'error',
      'noborder',
      'text-danger',
      'mt-4'
    )}
  >
    <div className="m-4">
      {showAccountAlreadyExistsError && (
        // show active cs account exists error
        <FormattedMessage
          id="createCSAccount.errorAccountAlreadyExists"
          description="Creditsafe account already exists error message"
          defaultMessage={
            'There is already an active Creditsafe account for this organisation.' +
            ' Go to Home and then back to ID06 Bolagsdeklaration to continue.'
          }
        />
      )}
      {showAccountPendingExistsError && (
        // show pending cs account exists error
        <FormattedMessage
          id="createCSAccount.errorAccountPendingExists"
          description="Pending creditsafe account error message"
          defaultMessage={
            'There is pending Creditsafe account for this organisation.' +
            ' Go to Home and then back to ID06 Bolagsdeklaration to continue.'
          }
        />
      )}
      {showForeignCompanyError && (
        // show f-tax error
        <FormattedMessage
          id="createCSAccount.errorForeignCompany"
          description="Creditsafe account creation error message"
          defaultMessage={'Error message CS foreign company {emailLink}'}
          values={{
            emailLink: (
              <a className="text-danger" href="mailto:<EMAIL>">
                <EMAIL>
              </a>
            ),
          }}
        />
      )}
      {showAutoaccountError &&
        !showAccountAlreadyExistsError &&
        !showAccountPendingExistsError &&
        !showForeignCompanyError && (
        /* eslint-disable */
        // show general autoaccount error if no other type of error is present
        <div>
          <FormattedMessage
            id="createCSAccount.errorAutoaccount"
            description="Creditsafe autoaccount error message"
            defaultMessage="Error message CS autoaccount {emailLink}"
            values={{
              emailLink: (
                <a className="text-danger" href="mailto:<EMAIL>">
                    <EMAIL>
                </a>
              ),
            }}
          />
          {autoaccountErrorMsgFromCS && (
            // show error message received from creditsafe
            <div className="mt-4">
              <FormattedMessage
                id="createCSAccount.errorMessageFromCS"
                description="error message from creditsafe"
                defaultMessage="Error message from creditsafe:"
              />
              <span> </span>
              <LocalizedText translations={autoaccountErrorMsgFromCS} />
            </div>
          )}
        </div>
      )}
    </div>
  </div>
);

CSAccountCreateError.propTypes = {
  autoaccountErrorMsgFromCS: PropTypes.object,
  showAccountAlreadyExistsError: PropTypes.bool,
  showAccountPendingExistsError: PropTypes.bool,
  showAutoaccountError: PropTypes.bool,
  showForeignCompanyError: PropTypes.bool,
};

export default injectIntl(CSAccountCreateError);
