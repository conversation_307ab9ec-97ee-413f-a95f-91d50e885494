import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import CSAccountCreateError from './CSAccountCreateError';
import CSAccountButton from './CSAccountButton';
import CSAccountPageHeader from './CSAccountPageHeader';
import { authStore, autoaccountStore } from '../../stores/Stores';
import StoreSubscription from '../../helpers/StoreSubscription';
import createAutoaccount from '../../actions/actionCreators/AutoaccountCreate';
import CSAccountMessages from './CSAccountMessages';
import { withRouter } from '../../helpers/RouterComponentWrappers';
import { intlPropType } from '../i18n/IntlPropTypes';
import { routerPropType } from '../../helpers/RouterPropTypes';

class CSAccountCreatePage extends React.Component {
  static get propTypes() {
    return {
      intl: intlPropType.isRequired,
      router: routerPropType.isRequired,
    };
  }

  constructor(props) {
    super(props);
    this.authStoreSub = new StoreSubscription(authStore, this.storesChanged.bind(this));
    this.autoaccountSub = new StoreSubscription(autoaccountStore, this.storesChanged.bind(this));
    this.state = this.mapStoreToState(authStore.getState(), autoaccountStore.getState());
  }

  componentDidMount() {
    this.authStoreSub.activate();
    this.autoaccountSub.activate();
  }

  componentWillUnmount() {
    this.authStoreSub.deactivate();
    this.autoaccountSub.deactivate();
  }

  onSubmit() {
    createAutoaccount(this.props.router);
  }

  getPhone() {
    const { userProfile } = this.state.authInfo;
    return userProfile && userProfile.phone;
  }

  getEmail() {
    const { userProfile } = this.state.authInfo;
    return userProfile && userProfile.email;
  }

  getAccountAlreadyExistsError() {
    return (
      this.state.errors &&
      this.state.errors.active_exists_error &&
      this.state.errors.active_exists_error[0]
    );
  }

  getPendingAccountExistsError() {
    return (
      this.state.errors &&
      this.state.errors.pending_exists_error &&
      this.state.errors.pending_exists_error[0]
    );
  }

  getErrors() {
    return this.state.errors;
  }

  getCreditsafeError() {
    return this.state.errors && this.state.errors.cs_error && this.state.errors.cs_error[0];
  }

  getForeignCompanyError() {
    return this.state.errors && this.state.errors.f_tax_error && this.state.errors.f_tax_error[0];
  }

  isCreateDisabled() {
    return this.state.create_in_progress === true;
  }

  mapStoreToState(authStoreState, autoaccountStoreState) {
    const authInfo = authStoreState.auth_info;
    return {
      authInfo,
      errors: autoaccountStoreState.errors,
      create_in_progress: autoaccountStoreState.create_in_progress,
    };
  }

  storesChanged() {
    this.setState(this.mapStoreToState(authStore.getState(), autoaccountStore.getState()));
  }

  render() {
    const { formatMessage } = this.props.intl;
    return (
      <div id="create_cs_account_page" className="container" data-testid="create_cs_account_page">
        <CSAccountPageHeader title={formatMessage(CSAccountMessages.headerCreateMainTitle)} />
        <p>
          <FormattedMessage
            id="createCSAccount.bodyMainText"
            description="Create Creditsafe account main text"
            defaultMessage="Create Creditsafe account main text"
          />
        </p>
        <div className="pt-5">
          <CSAccountButton
            id="create_autoaccount_button"
            onClick={this.onSubmit.bind(this)}
            disabled={this.isCreateDisabled()}
          >
            <FormattedMessage
              id="createCSAccount.createCSAccountButton"
              description="Creditsafe account button"
              defaultMessage="Create Creditsafe account"
            />
          </CSAccountButton>
        </div>
        <CSAccountCreateError
          showAccountAlreadyExistsError={!!this.getAccountAlreadyExistsError()}
          showAccountPendingExistsError={!!this.getPendingAccountExistsError()}
          showAutoaccountError={!!this.getErrors()}
          autoaccountErrorMsgFromCS={this.getCreditsafeError()}
          showForeignCompanyError={!!this.getForeignCompanyError()}
        />
      </div>
    );
  }
}

export default injectIntl(withRouter(CSAccountCreatePage));
