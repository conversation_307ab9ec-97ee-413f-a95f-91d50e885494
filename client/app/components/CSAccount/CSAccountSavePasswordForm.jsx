import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';
import ValidationMessages from '../shared/ValidationMessages';
import requiredValidator from '../shared/RequiredValidator';
import TextInput from '../shared/TextInput';
import FormRow from '../shared/FormRow';
import FormColumn from '../shared/FormColumn';
import FormGroup from '../shared/FormGroup';
import { useCSAccountContext } from './CSAccountContext';
import { handleOnChangePasswordField, handleSaveButtonClick } from './CSAccountActivatePageReducer';
import CSAccountButton from './CSAccountButton';

const CSAccountSavePasswordForm = props => {
  const { formatMessage } = props.intl;
  const { state, dispatch } = useCSAccountContext();
  const passwordField = {
    defaultValue: '',
    validators: [requiredValidator],
    isSensitiveInformation: true,
    errors: { hasErrors: state.hasValidationErrors, list: [] },
    value: state.password,
    handleOnChange: e => handleOnChangePasswordField(e.target.value, state, dispatch),
    validateOnAnyFieldChange: true,
  };
  const handleFormSubmit = event => {
    event.preventDefault();
    handleSaveButtonClick(state, dispatch);
  };
  return (
    <div>
      <form aria-label="save password form" onSubmit={handleFormSubmit}>
        <FormRow>
          <FormColumn width="7">
            <FormGroup>
              <ValidationMessages field={passwordField}>
                <span>
                  <label className="form-control-label inline" id={props.messages.passwordField.id}>
                    {formatMessage(props.messages.passwordField)}
                  </label>
                </span>
                <TextInput
                  id="save_cs_password_input"
                  field={passwordField}
                  autoFocus={false}
                  placeholder={formatMessage(props.messages.passwordPlaceholder)}
                  type="password"
                />
                {state.hasValidationErrors && (
                  <div className="err-msg">
                    {formatMessage(props.messages.validationFieldRequired)}
                  </div>
                )}
              </ValidationMessages>
            </FormGroup>
          </FormColumn>
          <FormColumn width="5">
            <FormGroup>
              <label className="form-control-label d-block hidden-sm-down">&nbsp;</label>
              <CSAccountButton
                id="save_cs_password_submit"
                onClick={() => handleSaveButtonClick(state, dispatch)}
                disabled={state.passwordSaveStarted}
              >
                {formatMessage(props.messages.button)}
              </CSAccountButton>
            </FormGroup>
          </FormColumn>
        </FormRow>
      </form>
      <div>
        {state.passwordSaveFailed && (
          <div className="card error noborder text-danger mt-4">
            <p className="m-4">
              {state.errors.active_exists_error ? (
                formatMessage(props.messages.activeCSexistsError)
              ) : (
                formatMessage(props.messages.csAccountInvalidPassword)
              )}
            </p>
          </div>
        )}
        <p>
          <a
            href={formatMessage(props.messages.forgotPasswordLink)}
            target="_blank"
            rel="noopener noreferrer"
          >
            {formatMessage(props.messages.forgotPasswordTitle)}
          </a>
        </p>
        <p>
          {formatMessage(
            props.messages.csAccountActivateNeedHelp,
            {email: (<a href="mailto:<EMAIL>"><EMAIL></a>)}
          )}
        </p>
      </div>
    </div>
  );
};

CSAccountSavePasswordForm.propTypes = {
  messages: PropTypes.object,
  intl: PropTypes.object,
};

export default injectIntl(CSAccountSavePasswordForm);
