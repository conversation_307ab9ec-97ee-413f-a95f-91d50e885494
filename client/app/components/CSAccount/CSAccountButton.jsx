import React from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';

const CSAccountButton = props => (
  <button
    id={props.id}
    className="btn btn-sm btn-primary th-button"
    role="button"
    type="submit"
    onClick={props.onClick}
    disabled={props.disabled}
  >
    <div className="d-flex align-items-center">
      {props.children}
      <i className="ml-4 fa fa-chevron-right" />
    </div>
  </button>
);

CSAccountButton.propTypes = {
  id: PropTypes.string,
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  children: PropTypes.node,
};

export default injectIntl(CSAccountButton);
