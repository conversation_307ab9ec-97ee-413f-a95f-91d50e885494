import React, { useReducer } from 'react';
import PropTypes from 'prop-types';
import { injectIntl } from 'react-intl';
import { useLocation } from 'react-router-dom';
import FormattedMessage from '../../components/i18n/FormattedMessage'
import CSAccountPageHeader from './CSAccountPageHeader';
import CSAccountActivateSuccess from './CSAccountActivateSuccess';
import CSAccountSavePasswordForm from './CSAccountSavePasswordForm';
import CSAccountActivateStep from './CSAccountActivateStep';
import iconKeyboard from '../../../static/img/icon-keyboard.png';
import iconMonitor from '../../../static/img/icon-monitor.png';
import iconPassword from '../../../static/img/icon-password.png';
import { CSAccountMessages } from './CSAccountMessages';
import { CSAccountActivatePageReducer, initialState } from './CSAccountActivatePageReducer';
import CSAccountContext from './CSAccountContext';

export const CSAccountActivatePage = props => {
  const { formatMessage } = props.intl;
  const location = useLocation();
  const initialPasswordSaveSucceeded = location.state?.passwordSaveSucceeded || false;
  const [state, dispatch] = useReducer(CSAccountActivatePageReducer, {
    ...initialState,
    passwordSaveSucceeded: initialPasswordSaveSucceeded,
  });

  return (
    <CSAccountContext.Provider value={{ state, dispatch }}>
      <div id="activate_cs_account_page" className="container">
        {!state.passwordSaveSucceeded && (
          <div>
            <CSAccountPageHeader title={formatMessage(CSAccountMessages.headerMainTitle)} />
            <p>
              <FormattedMessage
                id="CSAccountActivate.description"
                description="Activate Creditsafe account page description"
                defaultMessage="Activate Creditsafe account page description"
                values={{
                  link: (
                    <a href="http://www.creditsafe.se/">
                      {formatMessage(CSAccountMessages.creditsafe)}
                    </a>
                  ),
                }}
              />
            </p>
            <div className="save-cs-password info-blocks">
              <CSAccountActivateStep
                stepNumber="1"
                icon={iconMonitor}
                title={formatMessage(CSAccountMessages.activationStep1Header)}
                description={formatMessage(CSAccountMessages.activationStep1Description)}
              />
              <CSAccountActivateStep
                stepNumber="2"
                icon={iconPassword}
                title={formatMessage(CSAccountMessages.activationStep2Header)}
                description={formatMessage(CSAccountMessages.activationStep2Description)}
              />
              <CSAccountActivateStep
                stepNumber="3"
                icon={iconKeyboard}
                title={formatMessage(CSAccountMessages.activationStep3Header)}
                description={formatMessage(CSAccountMessages.activationStep3Description)}
              >
                <CSAccountSavePasswordForm messages={CSAccountMessages} />
              </CSAccountActivateStep>
              <div className="clearBoth">
                <FormattedMessage
                  id="CSAccountActivate.legalNote"
                  defaultMessage="Legal note at the bottom"
                />
              </div>
            </div>
          </div>
        )}
        {state.passwordSaveSucceeded && (
          <div>
            <CSAccountPageHeader title={formatMessage(CSAccountMessages.headerSuccessTitle)} />
            <CSAccountActivateSuccess />
          </div>
        )}
      </div>
    </CSAccountContext.Provider>
  );
};

CSAccountActivatePage.propTypes = {
  intl: PropTypes.object,
};

export default injectIntl(CSAccountActivatePage);
