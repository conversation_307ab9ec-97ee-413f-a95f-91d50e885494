import getAPIClient from '../../helpers/ApiClient';
import ActionTypes from '../../actions/ActionTypes';

const initialSaveState = {
  hasValidationErrors: false,
  passwordSaveStarted: false,
  passwordSaveSucceeded: false,
  passwordSaveFailed: false,
  errors: {},
};

const initialPassword = {
  password: '',
};

export const initialState = {
  ...initialSaveState,
  ...initialPassword,
};

export const CSAccountActivatePageReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.CSACCOUNT_CHANGED_INPUT_PASSWORD: {
      return {
        ...state,
        password: action.password,
      };
    }
    case ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED: {
      return {
        ...state,
        ...initialSaveState,
        passwordSaveStarted: true,
        errors: {},
      };
    }
    case ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED: {
      return {
        ...state,
        ...initialSaveState,
        passwordSaveSucceeded: true,
        errors: {},
      };
    }
    case ActionTypes.CSACCOUNT_PASSWORD_SAVE_VALIDATION_FAILED: {
      return {
        ...state,
        ...initialSaveState,
        hasValidationErrors: true,
        errors: action.payload,
      };
    }
    case ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED: {
      return {
        ...state,
        ...initialSaveState,
        passwordSaveFailed: true,
        errors: action.payload,
      };
    }
    default:
      throw Error(`Unknown action: ${action.type}`);
  }
};

export const handleSaveButtonClick = async (state, dispatch) => {
  const handleOnEnd = (err, res) => {
    if (err) {
      dispatch({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED, payload: { error: err } });
    } else if (res.data && res.data.ok) {
      dispatch({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED });
    } else if (res.data && res.data.errors) {
      if (res.data.errors.password) {
        dispatch({
          type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_VALIDATION_FAILED,
          payload: res.data.errors,
        });
      } else {
        dispatch({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED, payload: res.data.errors });
      }
    } else {
      dispatch({
        type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED,
        payload: { error: 'Unknown error' },
      });
    }
  };

  dispatch({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED });

  try {
    const apiClient = getAPIClient();
    const res = await apiClient.post(
      '/api/autoaccount/activate',
      true,
      { password: state.password }
    );
    handleOnEnd(null, res);
  } catch (error) {
    handleOnEnd(error, null);
  }
};

export const handleOnChangePasswordField = (value, state, dispatch) => {
  dispatch({
    type: ActionTypes.CSACCOUNT_CHANGED_INPUT_PASSWORD,
    password: value || '',
  });
};
