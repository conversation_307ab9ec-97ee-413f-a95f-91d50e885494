import { defineMessages } from 'react-intl';

/* eslint-disable max-len */
export const CSAccountMessages = defineMessages({
  headerCreateMainTitle: {
    id: 'createCSAccount.headerMainTitle',
    description: 'Create Creditsafe account header',
    defaultMessage: 'Create Creditsafe account',
  },
  headerMainTitle: {
    id: 'CSAccountActivate.headerMainTitle',
    description: 'CSAccountActivate.headerMainTitle',
    defaultMessage: 'CSAccountActivate header main title',
  },
  headerSuccessTitle: {
    id: 'CSAccountActivate.headerSuccessTitle',
    description: 'CSAccountActivate.headerSuccessTitle',
    defaultMessage: 'CSAccountActivate header success title',
  },
  button: {
    id: 'CSAccountActivate.button',
    description: 'Save CreditSafe password button text',
    defaultMessage: 'Save',
  },
  passwordPlaceholder: {
    id: 'CSAccountActivate.password.placeholder',
    description: 'CS password placeholder',
    defaultMessage: 'Enter your Creditsafe password',
  },
  forgotPasswordTitle: {
    id: 'CSAccountActivate.forgotPassword.title',
    description: 'Forgot password link text',
    defaultMessage: 'Forgot Creditsafe password',
  },
  forgotPasswordLink: {
    id: 'CSAccountActivate.forgotPassword.link',
    description: 'creditsafe password reset link',
    defaultMessage: 'https://login.creditsafe.com/password-reset?lang=en-GB',
  },
  creditsafe: {
    id: 'CSAccountActivate.creditsafeLinkTitle',
    description: 'creditsafe website link title in text',
    defaultMessage: 'www.creditsafe.com',
  },
  activationStep1Header: {
    id: 'CSAccountActivate.step1.header',
    description: 'creditsafe account activation step',
    defaultMessage: 'Receive activation mail from Creditsafe',
  },
  activationStep2Header: {
    id: 'CSAccountActivate.step2.header',
    description: 'creditsafe account activation step',
    defaultMessage: 'Activate Creditsafe account',
  },
  activationStep3Header: {
    id: 'CSAccountActivate.step3.header',
    description: 'creditsafe account activation step',
    defaultMessage: 'Connect your Creditsafe-account to ID06 Bolagsdeklaration',
  },
  activationStep1Description: {
    id: 'CSAccountActivate.step1.description',
    description: 'creditsafe account activation step',
    defaultMessage: 'Receive activation mail from Creditsafe',
  },
  activationStep2Description: {
    id: 'CSAccountActivate.step2.description',
    description: 'creditsafe account activation step',
    defaultMessage: 'Activate Creditsafe account',
  },
  activationStep3Description: {
    id: 'CSAccountActivate.step3.description',
    description: 'creditsafe account activation step',
    defaultMessage: 'Connect your Creditsafe-account to ID06 Bolagsdeklaration',
  },
  passwordField: {
    id: 'CSAccountActivate.passwordField',
    description: 'creditsafe account password input field',
    defaultMessage: 'Creditsafe password',
  },
  validationFieldRequired: {
    id: 'CSAccountActivate.validation.field.required',
    description: 'required input field error msg',
    defaultMessage: 'Required field',
  },
  activeCSexistsError: {
    id: 'CSAccountActivate.active_exists_error',
    description: 'error message when active cs already exists',
    defaultMessage: 'There is already an active Creditsafe account for this organisation. Go to Home and then back to ID06 Bolagsdeklaration to continue.',
  },
  csAccountInvalidPassword: {
    id: 'CSAccountActivate.invalid',
    description: 'error message when entered password is not valid',
    defaultMessage: 'The password you have entered is not valid.',
  },
  csAccountActivateNeedHelp: {
    id: 'CSAccountActivate.needHelp',
    description: 'help text about cs account activation process',
    defaultMessage: 'Need help? Contact {email}',
  },
});
/* eslint-enable max-len */
export default CSAccountMessages;
