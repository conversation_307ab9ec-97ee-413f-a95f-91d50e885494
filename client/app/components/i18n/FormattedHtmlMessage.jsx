import FormattedMessage from '../../components/i18n/FormattedMessage'
import React from 'react';
import PropTypes from 'prop-types';

const FormattedHtmlMessage = ({ id, description, defaultMessage }) =>
  <FormattedMessage id={id} description={description} defaultMessage={defaultMessage}>
    {message => (
      <span dangerouslySetInnerHTML={{ __html: message }} />
    )}
  </FormattedMessage>;

FormattedHtmlMessage.propTypes = {
  id: PropTypes.any.isRequired,
  description: PropTypes.string.isRequired,
  defaultMessage: PropTypes.string.isRequired,
};

export default FormattedHtmlMessage;
