import React from 'react';
import PropTypes from 'prop-types';
import { IntlContext } from 'react-intl';

class LocalizedText extends React.Component {
  static propTypes = {
    translations: PropTypes.object,
  };

  static defaultProps = {
    translations: {}, // fallback if nothing received
  };

  static contextType = IntlContext;

  render() {
    const { locale } = this.context;
    const { translations } = this.props;

    // Protect from null or undefined translations
    const translatedText = translations?.[locale] ?? translations?.['sv'] ??
      translations?.['en'] ?? `[missing locale: ${locale}]`;

    return <span>{translatedText}</span>;
  }
}

export default LocalizedText;
