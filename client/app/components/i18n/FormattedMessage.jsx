import React from 'react';
import { FormattedMessage } from 'react-intl';
import PropTypes from 'prop-types';

const FormattedMessageWrapper = ({ tag: Tag = 'span', ...props }) => {
  return (
    <Tag>
      <FormattedMessage {...props} />
    </Tag>
  );
};

FormattedMessageWrapper.propTypes = {
  tag: PropTypes.oneOfType([PropTypes.string, PropTypes.elementType]),
  id: PropTypes.string.isRequired,
  defaultMessage: PropTypes.string,
  values: PropTypes.object,
};

export default FormattedMessageWrapper;
