import PropTypes from 'prop-types';
import React from 'react';

import AddSingleSupplierView from './projects/AddSingleSupplierView';
import ProjectListFilterComponent from './projects/ProjectListFilterComponent';
import ProjectListComponent from './projects/ProjectListComponent';
import ProjectTreePopupComponent from './projects/ProjectTreePopupComponent';
import BulkSupplierImportWizard from './projects/BulkSupplierImportWizard';
import PreannouncementView from './projects/PreannouncementView';
import ContextModalContainer from './shared/ContextModalContainer';
import { Outlet } from 'react-router-dom';
import { withRouter } from '../helpers/RouterComponentWrappers';
import {
  Details,
  List,
  ListWithDetailsComponent,
} from './shared/ListWithDetailsComponent';

class ProjectsPageComponent extends React.Component {
  static get propTypes() {
    return {
      children: PropTypes.node,
      params: PropTypes.object,
      outletContent: PropTypes.object,
    };
  }

  render() {
    const { outletContent } = this.props;
    const detailsView = this.props.children ? React.Children.only(this.props.children) : null;
    return (
      <div id="projects_page" className="container page">
        <ProjectListFilterComponent narrow={!!detailsView} />
        <ListWithDetailsComponent>
          <List>
            <ProjectListComponent narrow={!!outletContent}
              selectedItemId={this.props.params.itemId}/>
          </List>
          {outletContent && <Details>
            <Outlet/>
          </Details>}
        </ListWithDetailsComponent>
        {/*
                `ProjectTreePopupComponent` and `BulkSupplierImportWizard` are
                placed here rather than inside `ProjectDetailsViewComponent`
                because `Sticky` component (within `ListWithDetailsComponent`)
                does not allow to position popups with respect to a browser
                window (`position: fixed`). Contents of `Sticky` behave as if
                browser window was `Sticky` node.
                */}
        <ProjectTreePopupComponent />
        <PreannouncementView />
        <BulkSupplierImportWizard />
        <AddSingleSupplierView />
        <ContextModalContainer />
      </div>
    );
  }
}

export default withRouter(ProjectsPageComponent);
