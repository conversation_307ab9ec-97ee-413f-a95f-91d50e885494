import React from 'react';
import { injectIntl } from 'react-intl';
import FormattedMessage from '../components/i18n/FormattedMessage'
import SupportEmail from './customerSupport/SupportEmail';

const ContactsPageComponent = injectIntl((/* props */) => (
  <div id="contacts_page" className="container page">
    <h3 className="text-primary pt-5 pointer-events-none">
      <FormattedMessage
        id="customerSupport.contactUsTitle"
        description="Contact us title"
        defaultMessage="CONTACT US"
      />
    </h3>

    <p>
      <i className="fa fa-envelope-o fa-pr--bol" aria-hidden="true" />
      <span id="customerSupport.supportEmail">
        <SupportEmail />
      </span>
      <br />
      <i className="fa fa-phone fa-pr--bol" aria-hidden="true" />
      <span id="customerSupport.supportPhone">010-480 92 00</span>
    </p>

    <p>
      <FormattedMessage
        id="customerSupport.whatIsBolagsdeklaration"
        description="Project description"
        defaultMessage={
          'Företagsdeklaration is a company screening service provided by {id06Link}.'
        }
        values={{ id06Link: <a href="http://www.id06.se/">ID06</a> }}
      />
    </p>
  </div>
));

export default ContactsPageComponent;
