import PropTypes from 'prop-types';
import React from 'react';
import FormattedMessage from '../components/i18n/FormattedMessage'

import SubscriptionTipComponent from './shared/SubscriptionTipComponent';
import CompanyListFilterComponent from './companies/CompanyListFilterComponent';
import CompanyListComponent from './companies/CompanyListComponent';

import { featureActive } from '../helpers/FeatureFlags';
import {
  Details,
  List,
  ListWithDetailsComponent,
} from './shared/ListWithDetailsComponent';
import { withRouter } from '../helpers/RouterComponentWrappers';
import { Outlet } from 'react-router-dom';

class CompaniesPageComponent extends React.Component {
  render() {
    const { outletContent } = this.props;
    return (
      <div id="companies_page" className="container companies-page-view page">
        {featureActive('use_stv_theme') && (
          <div>
            <h3 className="font-weight-bold">
              <FormattedMessage
                id="companies.pageheader"
                description="Page header for Companies page"
                defaultMessage="Search for a company in your company listing"
              />
            </h3>

            <hr />
          </div>
        )}

        <CompanyListFilterComponent />
        {featureActive('use_stv_theme') && <SubscriptionTipComponent />}
        <ListWithDetailsComponent>
          <List>
            <CompanyListComponent narrow={!!this.props.params.itemId}
              selectedItemId={this.props.params.itemId}/>
          </List>
          {outletContent && <Details>
            <Outlet/>
          </Details>}
        </ListWithDetailsComponent>
      </div>
    );
  }
}

CompaniesPageComponent.propTypes = {
  children: PropTypes.node,
  outletContent: PropTypes.object,
  params: PropTypes.object,
};

export default withRouter(CompaniesPageComponent);
