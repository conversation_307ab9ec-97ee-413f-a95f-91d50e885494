/* eslint-env jest */

import ActionTypes from '../../app/actions/ActionTypes';
import dispatcher from '../../app/Dispatcher';
import getAPIClient from '../../app/helpers/ApiClient';
import addSubcontractorsCompanyFind from '../../app/actions/actionCreators/AddSubcontractorsCompanyFind';
import { getCountryCodeAlpha3 } from '../../app/helpers/Subsidiaries';
import {
  ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON,
  ERROR_TYPE_SUBCONTRACTOR_ADD_COMPANY_NOT_FOUND_IN_REGISTERS,
  ERROR_TYPE_SUBCONTRACTOR_ADD_SAME_AS_MAIN_CONTRACTOR,
} from '../../app/Constants';
import searchFieldNotFound from '../../app/actions/actionCreators/SearchFieldNotFound';
import addSubcontractorsCompanyNotFound from '../../app/actions/actionCreators/AddSubcontractorsCompanyNotFound';

// Mock dependencies
jest.mock('../../app/Dispatcher');
jest.mock('../../app/helpers/ApiClient', () => jest.fn());
jest.mock('../../app/actions/actionCreators/SearchFieldNotFound', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AddSubcontractorsCompanyNotFound', () => jest.fn());

describe('addSubcontractorsCompanyFind', () => {
  let apiClient;

  beforeEach(() => {
    apiClient = {
      get: jest.fn(),
      post: jest.fn(),
    };
    getAPIClient.mockReturnValue(apiClient);
    dispatcher.dispatch.mockClear();
    searchFieldNotFound.mockClear();
    addSubcontractorsCompanyNotFound.mockClear();
  });

  it('dispatches SEARCH_FIELD_SEARCH_IN_PROGRESS and SEARCH_FIELD_FOUND on successful find', async () => {
    const projectId = '1';
    const govOrgId = '123';
    const countryCode = 'LT';
    const key = 'testKey';
    const results = [{ id: 'company1' }];

    apiClient.get.mockResolvedValue({ data: { results, ok: true } });

    await addSubcontractorsCompanyFind(projectId, govOrgId, countryCode, key);

    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS,
      query: govOrgId,
      country: countryCode,
      key,
    });
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_FOUND,
      results,
      key,
    });
  });

  it('handles no results found add same as main contractor', async () => {
    const projectId = '1';
    const govOrgId = '123';
    const countryCode = 'LT';
    const key = 'testKey';
    const errors = { error: 'same as main'};
  
    apiClient.get.mockResolvedValue({ data: { results: [], errors: errors, ok: false } });
  
    await addSubcontractorsCompanyFind(projectId, govOrgId, countryCode, key);
  
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS,
      query: govOrgId,
      country: countryCode,
      key,
    });
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS_STOP,
      key: 'add_subcontractor_company_search',
    });
    expect(addSubcontractorsCompanyNotFound).toHaveBeenCalledWith(errors, ERROR_TYPE_SUBCONTRACTOR_ADD_SAME_AS_MAIN_CONTRACTOR);
    expect(searchFieldNotFound).toHaveBeenCalledWith(key);
  });

  it('handles no results found private person', async () => {
    const projectId = '1';
    const govOrgId = '123';
    const countryCode = 'LT';
    const key = 'testKey';
  
    apiClient.get.mockResolvedValue({ data: { results: [], ok: false } });
  
    await addSubcontractorsCompanyFind(projectId, govOrgId, countryCode, key);
  
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS,
      query: govOrgId,
      country: countryCode,
      key,
    });
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS_STOP,
      key: 'add_subcontractor_company_search',
    });
    expect(addSubcontractorsCompanyNotFound).toHaveBeenCalledWith(undefined, ERROR_TYPE_SUBCONTRACTOR_ADD_PRIVATE_PERSON);
    expect(searchFieldNotFound).toHaveBeenCalledWith(key);
  });

  it('handles HTTP error and dispatches appropriate actions', async () => {
    const projectId = '1';
    const govOrgId = '123';
    const countryCode = 'LT';
    const key = 'testKey';
    const error = new Error('HTTP error');

    apiClient.get.mockRejectedValue(error);

    await addSubcontractorsCompanyFind(projectId, govOrgId, countryCode, key);

    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS,
      query: govOrgId,
      country: countryCode,
      key,
    });
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS_STOP,
      key: 'add_subcontractor_company_search',
    });
    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.ADD_SUBCONTRACTOR_SAVE_FAILURE,
      errors: { general: error.toString() },
    });
  });

  it('handles import company not found in registers', async () => {
    const projectId = '1';
    const govOrgId = '123';
    const countryCode = 'LT';
    const key = 'testKey';

    apiClient.get.mockResolvedValue({ data: { results: [], ok: true } });
    apiClient.post.mockResolvedValue({ data: { ok: false } });

    await addSubcontractorsCompanyFind(projectId, govOrgId, countryCode, key);

    expect(dispatcher.dispatch).toHaveBeenCalledWith({
      type: ActionTypes.SEARCH_FIELD_SEARCH_IN_PROGRESS,
      query: govOrgId,
      country: countryCode,
      key,
    });
    expect(addSubcontractorsCompanyNotFound).toHaveBeenCalledWith(undefined, ERROR_TYPE_SUBCONTRACTOR_ADD_COMPANY_NOT_FOUND_IN_REGISTERS);
    expect(searchFieldNotFound).toHaveBeenCalledWith(key);
  });

  it('should call getAPIClient with the correct params', async () => {
    const mockGet = jest.fn().mockResolvedValue({
      data: { results: [], ok: true }
    });
    getAPIClient.mockReturnValue({ get: mockGet });
    const mockPost = jest.fn().mockResolvedValue({
      data: { ok: true, company: { id: 'testCompanyId' } }
    });
    getAPIClient.mockReturnValue({ get: mockGet, post: mockPost });

    const projectId = 'testProjectId';
    const govOrgId = 'testGovOrgId';
    const countryCode = 'SE';
    const key = 'testKey';
    const isProjectClient = false;

    await addSubcontractorsCompanyFind(projectId, govOrgId, countryCode, key, isProjectClient);

    expect(mockGet).toHaveBeenCalledWith('/api/find-company', true, {
      params: {
        query: encodeURI(govOrgId),
        country: countryCode,
        is_project_client: isProjectClient,
      }
    });
    expect(mockPost).toHaveBeenCalledWith(
      `/api/project/${projectId}/import-company`,
      true,
      { gov_org_id: govOrgId, country: getCountryCodeAlpha3(countryCode) }
    );
  });
});
