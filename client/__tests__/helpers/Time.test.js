/* eslint-env mocha */

import { expect } from 'chai';
import { formatTime } from '../../app/helpers/Time';

describe('Time Helper', () => {
  const mockEnIntl = {
    formatMessage: ({ id }) => {
      const messages = {
        'time.now': 'just now'
      };
      return messages[id];
    },
    locale: 'en-US'
  };

  const mockSwedishIntl = {
    formatMessage: ({ id }) => {
      const messages = {
        'time.now': 'just nu'
      };
      return messages[id];
    },
    locale: 'sv-SE'
  };

  // Helper to create a date relative to now
  const getRelativeDate = (secondsAgo) => {
    const date = new Date();
    date.setSeconds(date.getSeconds() - secondsAgo);
    return date;
  };

  describe('Timezones', () => {
    it('should always be UTC', () => {
      expect(new Date().getTimezoneOffset()).to.equal(0);
    });
  });

  describe('formatTime', () => {
    it('returns empty string for undefined timestamp', () => {
      expect(formatTime(undefined, mockEnIntl)).to.equal('');
    });

    it('returns empty string for null timestamp', () => {
      expect(formatTime(null, mockEnIntl)).to.equal('');
    });

    it('returns "just now" for timestamps less than 15 seconds old', () => {
      const now = new Date();
      expect(formatTime(now, mockEnIntl)).to.equal('just now');

      const tenSecondsAgo = getRelativeDate(10);
      expect(formatTime(tenSecondsAgo, mockEnIntl)).to.equal('just now');
    });

    it('formats times between 15-60 seconds as "1 minute ago"', () => {
      const thirtySecondsAgo = getRelativeDate(30);
      expect(formatTime(thirtySecondsAgo, mockEnIntl)).to.equal('1 minute ago');
    });

    it('formats times in minutes correctly', () => {
      const twoMinutesAgo = getRelativeDate(2 * 60);
      expect(formatTime(twoMinutesAgo, mockEnIntl)).to.equal('2 minutes ago');

      const fiveMinutesAgo = getRelativeDate(5 * 60);
      expect(formatTime(fiveMinutesAgo, mockEnIntl)).to.equal('5 minutes ago');
    });

    it('formats times in hours correctly', () => {
      const oneHourAgo = getRelativeDate(60 * 60);
      expect(formatTime(oneHourAgo, mockEnIntl)).to.equal('1 hour ago');

      const threeHoursAgo = getRelativeDate(3 * 60 * 60);
      expect(formatTime(threeHoursAgo, mockEnIntl)).to.match(/^3 hours ago$/);
    });

    it('formats times from yesterday with time', () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const result = formatTime(yesterday, mockEnIntl);
      expect(result).to.include('yesterday');
      expect(result).to.match(/\d{2}[:]\d{2}$/);
    });

    it('formats times older than yesterday as yyyy-MM-dd HH:mm', () => {
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

      const result = formatTime(threeDaysAgo, mockEnIntl);
      expect(result).to.match(/^\d{4}-\d{2}-\d{2} \d{2}[.:]\d{2}$/);
    });

    it('respects the relative=false flag to always return absolute time', () => {
      const now = new Date();
      const result = formatTime(now, mockEnIntl, false);

      expect(result).to.match(/^\d{4}-\d{2}-\d{2} \d{2}[.:]\d{2}$/);
    });

    it('formats minutes in Swedish when locale is sv-SE', () => {
      const fiveMinutesAgo = getRelativeDate(5 * 60);
      expect(formatTime(fiveMinutesAgo, mockSwedishIntl)).to.equal('för 5 minuter sedan');

      const oneMinuteAgo = getRelativeDate(60);
      expect(formatTime(oneMinuteAgo, mockSwedishIntl)).to.equal('för 1 minut sedan');
    });
  });
});
