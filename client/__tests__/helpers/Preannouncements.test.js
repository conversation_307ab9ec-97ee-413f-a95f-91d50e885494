/* eslint-env mocha */
/* eslint-disable camelcase, no-unused-expressions, max-len */

import { assert } from 'chai';

import {
  getSupplierByIdFromTree,
  isContractLongerThanGivenMonths,
  getDiffInMonthsDays,
} from '../../app/helpers/Preannouncements';

describe('PreannouncementsHelpers', () => {
  const tree = {
    root: {
      suppliers: [
        {
          contract_start_date: '2022-03-01',
          contract_end_date: '2022-04-09',
          supplier_id: 'supplier_id_1',
          suppliers: [
            {
              contract_start_date: '2022-03-22',
              contract_end_date: '2021-08-29',
              supplier_id: 'supplier_id_12',
              suppliers: [
                {
                  contract_start_date: '2022-05-01',
                  contract_end_date: '2022-11-01',
                  supplier_id: 'supplier_id_121',
                  suppliers: [
                    {
                      contract_start_date: '2022-01-01',
                      contract_end_date: '2022-08-01',
                      supplier_id: 'supplier_id_1211',
                      suppliers: [],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          contract_start_date: '2021-08-29',
          contract_end_date: '2021-09-13',
          supplier_id: 'supplier_id_2',
          suppliers: [],
        },
        {
          contract_start_date: '2022-05-01',
          contract_end_date: '2022-10-31',
          supplier_id: 'supplier_id_3',
          suppliers: [],
        },
        {
          contract_start_date: '2022-05-01',
          contract_end_date: '2022-10-30',
          supplier_id: 'supplier_id_4',
          suppliers: [],
        },
        {
          contract_start_date: '2021-09-15',
          contract_end_date: '2022-03-13',
          supplier_id: 'supplier_id_5',
          suppliers: [],
        },
        {
          contract_start_date: '2021-09-15',
          contract_end_date: '2022-03-14',
          supplier_id: 'supplier_id_6',
          suppliers: [],
        },
        {
          contract_start_date: '2021-09-15',
          contract_end_date: '2022-03-15',
          supplier_id: 'supplier_id_7',
          suppliers: [],
        },
        {
          contract_start_date: '2022-07-01',
          contract_end_date: '2023-09-05',
          supplier_id: 'supplier_id_8',
          suppliers: [],
        },
        {
          contract_start_date: '2022-07-01',
          contract_end_date: '2022-07-01',
          supplier_id: 'supplier_id_9',
          suppliers: [],
        },
        {
          contract_start_date: '2022-07-01',
          contract_end_date: '2022-07-02',
          supplier_id: 'supplier_id_10',
          suppliers: [],
        },
        {
          contract_start_date: '2022-07-01',
          contract_end_date: '2022-06-30',
          supplier_id: 'supplier_id_11',
          suppliers: [],
        },
      ],
    },
  };

  describe('getSupplierByIdFromTree()', () => {
    it('get supplier by id 1', () => {
      const supplier = getSupplierByIdFromTree(tree, 'supplier_id_1');
      assert.equal(supplier.supplier_id, 'supplier_id_1');
    });
    it('get supplier by id 12', () => {
      const supplier = getSupplierByIdFromTree(tree, 'supplier_id_12');
      assert.equal(supplier.supplier_id, 'supplier_id_12');
    });
    it('get supplier by id 22', () => {
      const supplier = getSupplierByIdFromTree(tree, 'supplier_id_2');
      assert.equal(supplier.supplier_id, 'supplier_id_2');
    });
    it('get supplier by id 121', () => {
      const supplier = getSupplierByIdFromTree(tree, 'supplier_id_121');
      assert.equal(supplier.supplier_id, 'supplier_id_121');
    });
    it('get supplier by id 1211', () => {
      const supplier = getSupplierByIdFromTree(tree, 'supplier_id_1211');
      assert.equal(supplier.supplier_id, 'supplier_id_1211');
    });
    it('get supplier by id not existing', () => {
      const supplier = getSupplierByIdFromTree(tree, 'supplier_id_not_existing');
      assert.equal(supplier, null);
    });
    it('get supplier from null tree', () => {
      const supplier = getSupplierByIdFromTree(null, 'supplier_id_1');
      assert.equal(supplier, null);
    });
    it('get supplier by null id', () => {
      const supplier = getSupplierByIdFromTree(tree, null);
      assert.equal(supplier, null);
    });
  });

  describe('isContractLongerThanGivenMonths()', () => {
    it('contract is longer than 1 month', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_1', 1);
      assert.equal(result, true);
    });
    it('contract is shorter than 1 month', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_2', 1);
      assert.equal(result, false);
    });
    it('contract is equal to 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_3', 6);
      assert.equal(result, false);
    });
    it('contract is shorter than 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_4', 6);
      assert.equal(result, false);
    });
    it('contract is shorter than 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_5', 6);
      assert.equal(result, false);
    });
    it('contract is equal to 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_6', 6);
      assert.equal(result, false);
    });
    it('contract is longer than 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_7', 6);
      assert.equal(result, true);
    });
    it('contract is longer than 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_8', 6);
      assert.equal(result, true);
    });
    it('contract start and end dates are equal, given months = 0', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_9', 0);
      assert.equal(result, true);
    });
    it('contract start and end dates differ by 1 day, given months = 0', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_10', 0);
      assert.equal(result, true);
    });
    it('contract end date is less by 1 day than start date, given months = 0', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_11', 0);
      assert.equal(result, false);
    });
    it('contract is longer than 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_121', 6);
      assert.equal(result, true);
    });
    it('contract is longer than 5 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_121', 5);
      assert.equal(result, true);
    });
    it('contract is longer than 7 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_1211', 7);
      assert.equal(result, true);
    });
    it('contract is longer than 6 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_1211', 6);
      assert.equal(result, true);
    });
    it('contract is not longer than 8 months', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_1211', 8);
      assert.equal(result, false);
    });
    it('contract is longer than 6 months starting in year before and ending in year after', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_12', 6);
      assert.equal(result, true);
    });
    it('check contract length by not existing supplier id', () => {
      const result = isContractLongerThanGivenMonths(tree, 'supplier_id_not_existing', 6);
      assert.equal(result, null);
    });
  });

  describe('getDiffInMonthsDays', () => {
    it('2022-05-01 - 2022-11-01', () => {
      const result = getDiffInMonthsDays('2022-05-01', '2022-11-01');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 1);
    });
    it('2022-02-01 - 2022-08-01', () => {
      const result = getDiffInMonthsDays('2022-02-01', '2022-08-01');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 1);
    });
    it('2022-05-01 - 2022-10-31', () => {
      const result = getDiffInMonthsDays('2022-05-01', '2022-10-31');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 0);
    });
    it('2022-04-01 - 2022-10-01', () => {
      const result = getDiffInMonthsDays('2022-04-01', '2022-10-01');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 1);
    });
    it('2022-04-01 - 2022-09-30', () => {
      const result = getDiffInMonthsDays('2022-04-01', '2022-09-30');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 0);
    });
    it('2022-04-01 - 2022-09-29', () => {
      const result = getDiffInMonthsDays('2022-04-01', '2022-09-29');
      assert.equal(result.years, 0);
      assert.equal(result.months, 5);
      assert.equal(result.days, 29);
    });
    it('2021-09-01 - 2022-03-01', () => {
      const result = getDiffInMonthsDays('2021-09-01', '2022-03-01');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 1);
    });
    it('2021-09-15 - 2022-03-13', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-03-13');
      assert.equal(result.years, 0);
      assert.equal(result.months, 5);
      assert.equal(result.days, 27);
    });
    it('2021-09-15 - 2022-03-14', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-03-14');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 0);
    });
    it('2021-09-15 - 2022-03-15', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-03-15');
      assert.equal(result.years, 0);
      assert.equal(result.months, 6);
      assert.equal(result.days, 1);
    });
    it('2021-09-15 - 2022-09-13', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-09-13');
      assert.equal(result.years, 0);
      assert.equal(result.months, 11);
      assert.equal(result.days, 29);
    });
    it('2021-09-15 - 2022-09-14', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-09-14');
      assert.equal(result.years, 1);
      assert.equal(result.months, 0);
      assert.equal(result.days, 0);
    });
    it('2021-09-15 - 2022-09-15', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-09-15');
      assert.equal(result.years, 1);
      assert.equal(result.months, 0);
      assert.equal(result.days, 1);
    });
    it('2021-09-15 - 2022-10-14', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-10-14');
      assert.equal(result.years, 1);
      assert.equal(result.months, 1);
      assert.equal(result.days, 0);
    });
    it('2021-09-15 - 2022-10-15', () => {
      const result = getDiffInMonthsDays('2021-09-15', '2022-10-15');
      assert.equal(result.years, 1);
      assert.equal(result.months, 1);
      assert.equal(result.days, 1);
    });
    it('2022-07-01 - 2023-09-05', () => {
      const result = getDiffInMonthsDays('2022-07-01', '2023-09-05');
      assert.equal(result.years, 1);
      assert.equal(result.months, 2);
      assert.equal(result.days, 5);
    });
    it('2022-07-01 - 2022-06-30', () => {
      const result = getDiffInMonthsDays('2022-07-01', '2022-06-30');
      assert.equal(result.years, 0);
      assert.equal(result.months, 0);
      assert.equal(result.days, 0);
    });
    it('2022-07-01 - 2022-07-01', () => {
      const result = getDiffInMonthsDays('2022-07-01', '2022-07-01');
      assert.equal(result.years, 0);
      assert.equal(result.months, 0);
      assert.equal(result.days, 1);
    });
    it('2022-07-01 - 2022-07-02', () => {
      const result = getDiffInMonthsDays('2022-07-01', '2022-07-02');
      assert.equal(result.years, 0);
      assert.equal(result.months, 0);
      assert.equal(result.days, 2);
    });
  });
});
