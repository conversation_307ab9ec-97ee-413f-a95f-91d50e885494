/* eslint-env mocha */
/* eslint-disable camelcase, no-unused-expressions, max-len */

import chai, { expect } from 'chai';
import chaiExclude from 'chai-exclude';

import {
  annotateWithChildStatuses,
  annotateWithWorstStatus,
  sortTreeByWorstStatus,
  filterTreeByStatus,
  supplierContainsSameCompany,
  isAncestor,
  moveProjectSupplier,
} from '../../app/helpers/ProjectTree';

chai.use(chaiExclude);

class SupplierTreeBuilder {
  constructor(own_status) {
    this.tree = this._make_node(own_status);
    delete this.tree.company_name; // eslint-disable-line prefer-reflect
    delete this.tree.supplier_role;
    this.tree.unlinked_suppliers = [];
    this.annotators = [];
  }

  _make_node(own_status, name = 'donotcare', role = 'supplier') {
    return {
      company_name: name,
      own_status,
      supplier_role: role,
      suppliers: [],
      // assuming that all nodes here are unique by company_id
      company_id: Math.random()
        .toString(36)
        .substring(7),
    };
  }

  contractor(own_status, name = 'donotcare', role = 'main_supplier') {
    this.tree.suppliers.push(this._make_node(own_status, name, role));
    return this;
  }

  subcontractor(own_status, name = 'donotcare', role = 'supplier') {
    this.tree.suppliers[this.tree.suppliers.length - 1].suppliers.push(
      this._make_node(own_status, name, role)
    );
    return this;
  }

  subSubcontractor(own_status, name = 'donotcare') {
    const targetContractor = this.tree.suppliers[this.tree.suppliers.length - 1];
    const targetSubcontractor = targetContractor.suppliers[targetContractor.suppliers.length - 1];

    targetSubcontractor.suppliers.push(this._make_node(own_status, name));
    return this;
  }

  unlinkedSupplier(own_status, name = 'donotcare') {
    this.tree.unlinked_suppliers.push(this._make_node(own_status, name));
    return this;
  }

  unlinkedSubsupplier(own_status, name = 'donotcare') {
    const targetSupplier = this.tree.unlinked_suppliers[this.tree.unlinked_suppliers.length - 1];

    targetSupplier.suppliers.push(this._make_node(own_status, name));
    return this;
  }

  annotate(func) {
    this.annotators.push(func);
    return this;
  }

  build() {
    let { tree } = this;
    this.annotators.forEach(annotator => {
      tree = annotator(tree);
    });

    return tree;
  }
}

const supplierTreeFixture = (own_status = 'ok') => new SupplierTreeBuilder(own_status);

describe('ProjectTreeHelpers', () => {
  describe('annotateWithChildStatuses', () => {
    it('annotates statuses with zeroes when without children', () => {
      const tree = annotateWithChildStatuses({
        own_status: 'ok',
        suppliers: [],
      });

      expect(tree).to.deep.equal({
        own_status: 'ok',
        suppliers: [],
        children_status_counts: {
          stop: 0,
          investigate: 0,
          incomplete: 0,
          attention: 0,
          ok: 0,
        },
      });
    });

    it('annotates child status sums when with children', () => {
      const tree = annotateWithChildStatuses({
        own_status: 'ok',
        suppliers: [
          {
            own_status: 'stop',
            suppliers: [],
          },
        ],
      });

      expect(tree).to.deep.equal({
        own_status: 'ok',
        suppliers: [
          {
            own_status: 'stop',
            suppliers: [],
            children_status_counts: {
              stop: 0,
              investigate: 0,
              incomplete: 0,
              attention: 0,
              ok: 0,
            },
          },
        ],
        children_status_counts: {
          stop: 1,
          investigate: 0,
          incomplete: 0,
          attention: 0,
          ok: 0,
        },
      });
    });

    it('annotates all children with statuses', () => {
      let tree = supplierTreeFixture('investigate')
        .contractor('attention')
        .subcontractor('stop')
        .contractor('attention')
        .subcontractor('incomplete')
        .subcontractor('incomplete')
        .subSubcontractor('ok')
        .build();
      tree = annotateWithChildStatuses(tree);
      expect(tree)
        .excludingEvery(['company_id', 'supplier_role'])
        .to.deep.equal({
          own_status: 'investigate',
          unlinked_suppliers: [],
          suppliers: [
            {
              own_status: 'attention',
              company_name: 'donotcare',
              suppliers: [
                {
                  own_status: 'stop',
                  company_name: 'donotcare',
                  suppliers: [],
                  children_status_counts: {
                    stop: 0,
                    investigate: 0,
                    incomplete: 0,
                    attention: 0,
                    ok: 0,
                  },
                },
              ],
              children_status_counts: {
                stop: 1,
                investigate: 0,
                incomplete: 0,
                attention: 0,
                ok: 0,
              },
            },
            {
              own_status: 'attention',
              company_name: 'donotcare',
              suppliers: [
                {
                  own_status: 'incomplete',
                  company_name: 'donotcare',
                  suppliers: [],
                  children_status_counts: {
                    stop: 0,
                    investigate: 0,
                    incomplete: 0,
                    attention: 0,
                    ok: 0,
                  },
                },
                {
                  own_status: 'incomplete',
                  company_name: 'donotcare',
                  suppliers: [
                    {
                      own_status: 'ok',
                      company_name: 'donotcare',
                      suppliers: [],
                      children_status_counts: {
                        stop: 0,
                        investigate: 0,
                        incomplete: 0,
                        attention: 0,
                        ok: 0,
                      },
                    },
                  ],
                  children_status_counts: {
                    stop: 0,
                    investigate: 0,
                    incomplete: 0,
                    attention: 0,
                    ok: 1,
                  },
                },
              ],
              children_status_counts: {
                stop: 0,
                investigate: 0,
                incomplete: 2,
                attention: 0,
                ok: 1,
              },
            },
          ],
          children_status_counts: {
            stop: 1,
            investigate: 0,
            incomplete: 2,
            attention: 2,
            ok: 1,
          },
        });
    });

    it('annotates unlinked suppliers too', () => {
      let tree = supplierTreeFixture('investigate')
        .unlinkedSupplier('attention')
        .unlinkedSubsupplier('stop')
        .unlinkedSupplier('incomplete')
        .build();
      tree = annotateWithChildStatuses(tree);
      const EMPTY_COUNTS = {
        attention: 0,
        incomplete: 0,
        investigate: 0,
        ok: 0,
        stop: 0,
      };
      expect(tree)
        .excludingEvery(['company_id', 'supplier_role'])
        .to.deep.equal({
          children_status_counts: {
            attention: 1,
            incomplete: 1,
            investigate: 0,
            ok: 0,
            stop: 1,
          },
          own_status: 'investigate',
          suppliers: [],
          unlinked_suppliers: [
            {
              children_status_counts: {
                attention: 0,
                incomplete: 0,
                investigate: 0,
                ok: 0,
                stop: 1,
              },
              own_status: 'attention',
              company_name: 'donotcare',
              suppliers: [
                {
                  children_status_counts: EMPTY_COUNTS,
                  own_status: 'stop',
                  company_name: 'donotcare',
                  suppliers: [],
                },
              ],
            },
            {
              children_status_counts: EMPTY_COUNTS,
              own_status: 'incomplete',
              company_name: 'donotcare',
              suppliers: [],
            },
          ],
        });
    });

    it('skips null statuses', () => {
      const tree = annotateWithChildStatuses({
        own_status: 'ok',
        suppliers: [
          {
            own_status: null,
            suppliers: [],
          },
        ],
      });

      expect(tree).to.deep.equal({
        own_status: 'ok',
        suppliers: [
          {
            own_status: null,
            suppliers: [],
            children_status_counts: {
              stop: 0,
              investigate: 0,
              incomplete: 0,
              attention: 0,
              ok: 0,
            },
          },
        ],
        children_status_counts: {
          stop: 0,
          investigate: 0,
          incomplete: 0,
          attention: 0,
          ok: 0,
        },
      });
    });
  });

  describe('annotateWithWorstStatus', () => {
    it('annotates each node with worst_subtree_status attr', () => {
      let tree = supplierTreeFixture('ok').build();
      tree = annotateWithWorstStatus(tree);
      expect(tree)
        .excludingEvery('company_id')
        .to.deep.equal({
          own_status: 'ok',
          suppliers: [],
          unlinked_suppliers: [],
          worst_subtree_status: 'ok',
        });
    });

    it("annotates parent node with child status if it's worse", () => {
      let tree = supplierTreeFixture('ok')
        .contractor('attention')
        .contractor('stop')
        .build();
      tree = annotateWithWorstStatus(tree);
      expect(tree.worst_subtree_status).to.equal('stop');
    });

    it('annotates child nodes with worst statuses', () => {
      let tree = supplierTreeFixture('ok')
        .contractor('attention')
        .subcontractor('investigate')
        .contractor('stop')
        .build();
      tree = annotateWithWorstStatus(tree);
      expect(tree.suppliers[0].worst_subtree_status).to.equal('investigate');
    });

    it('annotates unlinked suppliers too', () => {
      let tree = supplierTreeFixture('ok')
        .unlinkedSupplier('stop')
        .build();
      tree = annotateWithWorstStatus(tree);
      expect(tree.unlinked_suppliers[0].worst_subtree_status).to.equal('stop');
    });
  });

  describe('sortTreeByWorstStatus', () => {
    it('sorts children of a node by worst status', () => {
      const tree = supplierTreeFixture('stop')
        .contractor('investigate')
        .contractor('ok')
        .contractor('stop')
        .annotate(annotateWithWorstStatus)
        .build();
      const expectedTree = supplierTreeFixture('stop')
        .contractor('stop')
        .contractor('investigate')
        .contractor('ok')
        .annotate(annotateWithWorstStatus)
        .build();
      expect(sortTreeByWorstStatus(tree))
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });

    it('sorts grandchildren by worst status', () => {
      const tree = supplierTreeFixture('stop')
        .contractor('ok')
        .subcontractor('investigate')
        .subcontractor('ok')
        .subcontractor('stop')
        .annotate(annotateWithWorstStatus)
        .build();
      const expectedTree = supplierTreeFixture('stop')
        .contractor('ok')
        .subcontractor('stop')
        .subcontractor('investigate')
        .subcontractor('ok')
        .annotate(annotateWithWorstStatus)
        .build();
      expect(sortTreeByWorstStatus(tree))
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });

    it('sorts unlinked suppliers too', () => {
      const tree = supplierTreeFixture('stop')
        .unlinkedSupplier('investigate')
        .unlinkedSupplier('ok')
        .unlinkedSupplier('stop')
        .annotate(annotateWithWorstStatus)
        .build();
      const expectedTree = supplierTreeFixture('stop')
        .unlinkedSupplier('stop')
        .unlinkedSupplier('investigate')
        .unlinkedSupplier('ok')
        .annotate(annotateWithWorstStatus)
        .build();
      expect(sortTreeByWorstStatus(tree))
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });

    it('breaks ties alphabetically', () => {
      const tree = supplierTreeFixture('stop')
        .contractor('ok', 'Company A')
        .contractor('ok', 'Company C')
        .contractor('ok', 'Company B')
        .annotate(annotateWithWorstStatus)
        .build();
      const expectedTree = supplierTreeFixture('stop')
        .contractor('ok', 'Company A')
        .contractor('ok', 'Company B')
        .contractor('ok', 'Company C')
        .annotate(annotateWithWorstStatus)
        .build();
      expect(sortTreeByWorstStatus(tree))
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });

    it('puts main contractors first', () => {
      const tree = supplierTreeFixture('stop')
        .contractor('ok', 'Company A')
        .contractor('investigate', 'Company B')
        .contractor('stop', 'Company C', 'supplier')
        .contractor('investigate', 'Company D', 'supplier')
        .annotate(annotateWithWorstStatus)
        .build();
      const expectedTree = supplierTreeFixture('stop')
        .contractor('investigate', 'Company B')
        .contractor('ok', 'Company A')
        .contractor('stop', 'Company C', 'supplier')
        .contractor('investigate', 'Company D', 'supplier')
        .annotate(annotateWithWorstStatus)
        .build();
      expect(sortTreeByWorstStatus(tree))
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });
  });

  describe('filterTreeByStatus', () => {
    it('drops nodes of lower and higher status', () => {
      const tree = supplierTreeFixture('stop')
        .contractor('ok')
        .contractor('investigate')
        .contractor('stop')
        .annotate(annotateWithChildStatuses)
        .build();
      const expectedTree = supplierTreeFixture('stop')
        .contractor('investigate')
        .annotate(annotateWithChildStatuses)
        .build();
      const filteredTree = annotateWithChildStatuses(filterTreeByStatus(tree, 'investigate'));
      expect(filteredTree)
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });

    it('keeps nodes if they have children with given status', () => {
      const tree = supplierTreeFixture('stop')
        .contractor('ok')
        .subcontractor('attention')
        .contractor('investigate')
        .contractor('stop')
        .annotate(annotateWithChildStatuses)
        .build();
      const expectedTree = supplierTreeFixture('stop')
        .contractor('ok')
        .subcontractor('attention')
        .annotate(annotateWithChildStatuses)
        .build();
      const filteredTree = annotateWithChildStatuses(filterTreeByStatus(tree, 'attention'));
      expect(filteredTree)
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });

    it('filters child nodes too', () => {
      const tree = supplierTreeFixture('attention')
        .contractor('ok')
        .subcontractor('ok')
        .subcontractor('attention')
        .annotate(annotateWithChildStatuses)
        .build();
      const expectedTree = supplierTreeFixture('attention')
        .contractor('ok')
        .subcontractor('attention')
        .annotate(annotateWithChildStatuses)
        .build();
      const filteredTree = annotateWithChildStatuses(filterTreeByStatus(tree, 'attention'));
      expect(filteredTree)
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });

    it('filters unlinked suppliers too', () => {
      const tree = supplierTreeFixture('attention')
        .unlinkedSupplier('ok')
        .unlinkedSubsupplier('ok')
        .unlinkedSubsupplier('attention')
        .annotate(annotateWithChildStatuses)
        .build();
      const expectedTree = supplierTreeFixture('attention')
        .unlinkedSupplier('ok')
        .unlinkedSubsupplier('attention')
        .annotate(annotateWithChildStatuses)
        .build();
      const filteredTree = annotateWithChildStatuses(filterTreeByStatus(tree, 'attention'));
      expect(filteredTree)
        .excludingEvery('company_id')
        .to.deep.equal(expectedTree);
    });
  });

  describe('containsSameOrg', () => {
    it("returns false when supplier doesn't contain same org", () => {
      const tree = {
        suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c3',
                suppliers: [],
              },
            ],
          },
          {
            supplier_id: 's2',
            company_id: 'c2',
            suppliers: [],
          },
        ],
      };
      expect(supplierContainsSameCompany(tree, 's2', 's1', 'linked')).to.be.false;
    });

    it('returns true when moved supplier org is already in target supplier', () => {
      const tree = {
        suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c2',
                suppliers: [],
              },
            ],
          },
          {
            supplier_id: 's2',
            company_id: 'c2',
            suppliers: [],
          },
        ],
      };
      expect(supplierContainsSameCompany(tree, 's2', 's1', 'linked')).to.be.true;
    });
    it('returns true when moved supplier org is in target orgs unlinked suppliers', () => {
      const tree = {
        suppliers: [],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c2',
                suppliers: [],
              },
            ],
          },
          {
            supplier_id: 's2',
            company_id: 'c2',
            suppliers: [],
          },
        ],
      };
      expect(supplierContainsSameCompany(tree, 's2', 's1', 'linked')).to.be.true;
    });

    it('returns true when org already exists at top-level', () => {
      const tree = {
        suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c2',
                suppliers: [],
              },
            ],
          },
          {
            supplier_id: 's2',
            company_id: 'c2',
            suppliers: [
              {
                supplier_id: 's4',
                company_id: 'c1',
                suppliers: [],
              },
            ],
          },
        ],
      };
      expect(supplierContainsSameCompany(tree, 's4', null, 'linked')).to.be.true;
    });
    it('returns false when org already exists at top-level but is unlinked', () => {
      const tree = {
        suppliers: [
          {
            supplier_id: 's2',
            company_id: 'c2',
            suppliers: [
              {
                supplier_id: 's4',
                company_id: 'c1',
                suppliers: [],
              },
            ],
          },
        ],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c2',
                suppliers: [],
              },
            ],
          },
        ],
      };
      expect(supplierContainsSameCompany(tree, 's4', null, 'linked')).to.be.false;
    });
    it('returns false when adding linked but unlinked org already exists at top-level', () => {
      const tree = {
        suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c2',
                suppliers: [],
              },
            ],
          },
        ],
        unlinked_suppliers: [
          {
            supplier_id: 's2',
            company_id: 'c2',
            suppliers: [
              {
                supplier_id: 's4',
                company_id: 'c1',
                suppliers: [],
              },
            ],
          },
        ],
      };
      expect(supplierContainsSameCompany(tree, 's4', null, 'unlinked')).to.be.false;
    });
  });

  describe('isAncestor', () => {
    it('returns false when supplier 1 is not an ancestor of supplier 2', () => {
      const tree = {
        suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's2',
                company_id: 'c2',
                suppliers: [],
              },
            ],
          },
          {
            supplier_id: 's3',
            company_id: 'c3',
            suppliers: [],
          },
        ],
      };
      expect(isAncestor(tree, 's3', 's2')).to.be.false;
    });
    it('returns true when supplier 1 is ancestor of supplier 3', () => {
      const tree = {
        suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's2',
                company_id: 'c2',
                suppliers: [
                  {
                    supplier_id: 's3',
                    company_id: 'c3',
                    suppliers: [],
                  },
                ],
              },
            ],
          },
        ],
      };
      expect(isAncestor(tree, 's1', 's3')).to.be.true;
    });

    it('returns true when unlinked supplier 1 is ancestor of supplier 3', () => {
      const tree = {
        suppliers: [],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            suppliers: [
              {
                supplier_id: 's2',
                company_id: 'c2',
                suppliers: [
                  {
                    supplier_id: 's3',
                    company_id: 'c3',
                    suppliers: [],
                  },
                ],
              },
            ],
          },
        ],
      };
      expect(isAncestor(tree, 's1', 's3')).to.be.true;
    });
  });

  describe('moveProjectSupplier', () => {
    it('moves non-toplevel unlinked supplier to top-level unlinked', () => {
      const tree = {
        suppliers: [],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            supplier_type: 'unlinked',
            suppliers: [
              {
                supplier_id: 's2',
                company_id: 'c2',
                supplier_type: 'linked',
                suppliers: [
                  {
                    supplier_id: 's3',
                    company_id: 'c3',
                    suppliers: [],
                  },
                ],
              },
            ],
          },
        ],
      };
      const newTree = moveProjectSupplier(tree, 's2', 'unlinked', null, 'unlinked');
      expect(newTree).to.deep.equal({
        suppliers: [],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            supplier_type: 'unlinked',
            suppliers: [],
          },
          {
            supplier_id: 's2',
            company_id: 'c2',
            supplier_type: 'unlinked',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c3',
                suppliers: [],
              },
            ],
          },
        ],
      });
    });

    it('moves toplevel unlinked suppliers to linked', () => {
      const tree = {
        suppliers: [],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            supplier_type: 'unlinked',
            suppliers: [
              {
                supplier_id: 's2',
                company_id: 'c2',
                supplier_type: 'linked',
                suppliers: [
                  {
                    supplier_id: 's3',
                    company_id: 'c3',
                    suppliers: [],
                  },
                ],
              },
            ],
          },
        ],
      };
      const newTree = moveProjectSupplier(tree, 's1', 'unlinked', null);
      expect(newTree).to.deep.equal({
        suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            supplier_type: 'linked',
            suppliers: [
              {
                supplier_id: 's2',
                company_id: 'c2',
                supplier_type: 'linked',
                suppliers: [
                  {
                    supplier_id: 's3',
                    company_id: 'c3',
                    suppliers: [],
                  },
                ],
              },
            ],
          },
        ],
        unlinked_suppliers: [],
      });
    });

    it('moves non-toplevel unlinked suppliers to linked', () => {
      const tree = {
        suppliers: [],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            company_id: 'c1',
            supplier_type: 'unlinked',
            suppliers: [
              {
                supplier_id: 's2',
                company_id: 'c2',
                supplier_type: 'linked',
                suppliers: [
                  {
                    supplier_id: 's3',
                    company_id: 'c3',
                    suppliers: [],
                  },
                ],
              },
            ],
          },
        ],
      };
      const newTree = moveProjectSupplier(tree, 's2', 'linked', null);
      expect(newTree).to.deep.equal({
        suppliers: [
          {
            supplier_id: 's2',
            supplier_type: 'linked',
            company_id: 'c2',
            suppliers: [
              {
                supplier_id: 's3',
                company_id: 'c3',
                suppliers: [],
              },
            ],
          },
        ],
        unlinked_suppliers: [
          {
            supplier_id: 's1',
            supplier_type: 'unlinked',
            company_id: 'c1',
            suppliers: [],
          },
        ],
      });
    });
  });
});
