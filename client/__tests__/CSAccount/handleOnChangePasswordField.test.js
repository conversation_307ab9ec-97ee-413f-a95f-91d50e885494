/* eslint-env jest */

// eslint-disable-next-line max-len
import { handleOnChangePasswordField } from '../../app/components/CSAccount/CSAccountActivatePageReducer';
import ActionTypes from '../../app/actions/ActionTypes';

describe('handleOnChangePasswordField', () => {
  let dispatchMock;

  beforeEach(() => {
    dispatchMock = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should dispatch CSACCOUNT_CHANGED_INPUT_PASSWORD action with the provided value', () => {
    const value = 'newPassword';

    handleOnChangePasswordField(value, {}, dispatchMock);

    expect(dispatchMock).toHaveBeenCalledWith({
      type: ActionTypes.CSACCOUNT_CHANGED_INPUT_PASSWORD,
      password: value,
    });
  });

  // eslint-disable-next-line max-len
  test('should dispatch CSACCOUNT_CHANGED_INPUT_PASSWORD action with an empty string if the provided value is undefined', () => {
    handleOnChangePasswordField(undefined, {}, dispatchMock);

    expect(dispatchMock).toHaveBeenCalledWith({
      type: ActionTypes.CSACCOUNT_CHANGED_INPUT_PASSWORD,
      password: '',
    });
  });

  // eslint-disable-next-line max-len
  test('should dispatch CSACCOUNT_CHANGED_INPUT_PASSWORD action with an empty string if the provided value is null', () => {
    handleOnChangePasswordField(null, {}, dispatchMock);

    expect(dispatchMock).toHaveBeenCalledWith({
      type: ActionTypes.CSACCOUNT_CHANGED_INPUT_PASSWORD,
      password: '',
    });
  });

  // eslint-disable-next-line max-len
  test('should dispatch CSACCOUNT_CHANGED_INPUT_PASSWORD action with an empty string if the provided value is an empty string', () => {
    handleOnChangePasswordField('', {}, dispatchMock);

    expect(dispatchMock).toHaveBeenCalledWith({
      type: ActionTypes.CSACCOUNT_CHANGED_INPUT_PASSWORD,
      password: '',
    });
  });
});
