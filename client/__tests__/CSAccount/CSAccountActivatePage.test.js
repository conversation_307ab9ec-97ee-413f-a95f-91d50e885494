/* eslint-env jest */

import React from 'react';
import { IntlProvider } from 'react-intl';
import { render } from '@testing-library/react';
// eslint-disable-next-line max-len
import CSAccountActivatePage from '../../app/components/CSAccount/CSAccountActivatePage';
import sv from '../../app/localizations/sv.json';
import { MemoryRouter } from 'react-router-dom';

// Mock the useReducer hook
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn(),
}));

// mock children components
jest.mock('../../app/components/CSAccount/CSAccountSavePasswordForm', () => () => (
  <div>CSAccountSavePasswordForm</div>
));

describe('CSAccountActivatePage test', () => {
  beforeEach(() => {
    // Reset the mock before each test
    jest.resetModules();
  });

  const renderWithReactIntlAndRouter = component =>
    render(
      <IntlProvider locale="sv" messages={sv}>
        <MemoryRouter>
          {component}
        </MemoryRouter>
      </IntlProvider>
    );

  it('should render initial state correctly', () => {
    // Mock useReducer to return passwordSaveSucceeded as false
    React.useReducer.mockReturnValue([{ passwordSaveSucceeded: false }, jest.fn()]);
    const { container } = renderWithReactIntlAndRouter(<CSAccountActivatePage />);

    expect(container.textContent).toContain(sv['CSAccountActivate.headerMainTitle']);
    expect(container.textContent).toContain(sv['CSAccountActivate.description']);
    expect(container.textContent).toContain(sv['CSAccountActivate.step1.header']);
    expect(container.textContent).toContain(sv['CSAccountActivate.step1.description']);
    expect(container.textContent).toContain(sv['CSAccountActivate.step2.header']);
    expect(container.textContent).toContain(sv['CSAccountActivate.step2.description']);
    expect(container.textContent).toContain(sv['CSAccountActivate.step3.header']);
    expect(container.textContent).toContain(sv['CSAccountActivate.step3.description']);
    expect(container.textContent).toContain('CSAccountSavePasswordForm');
    expect(container.textContent).toContain(sv['CSAccountActivate.legalNote']);

    expect(container.textContent).not.toContain(sv['CSAccountActivate.headerSuccessTitle']);
    expect(container.textContent).not.toContain(sv['CSAccountActivate.success']);

    expect(container.querySelector('img[src="icon-monitor.png"]')).toBeInTheDocument();
    expect(container.querySelector('img[src="icon-keyboard.png"]')).toBeInTheDocument();
    expect(container.querySelector('img[src="icon-password.png"]')).toBeInTheDocument();
  });

  it('should render <CSAccountActivateSuccess /> when passwordSaveSucceeded is set', () => {
    // Mock useReducer to return passwordSaveSucceeded as true
    React.useReducer.mockReturnValue([{ passwordSaveSucceeded: true }, jest.fn()]);
    const { container } = renderWithReactIntlAndRouter(<CSAccountActivatePage />);

    expect(container.textContent).toContain(sv['CSAccountActivate.headerSuccessTitle']);
    expect(container.textContent).toContain(sv['CSAccountActivate.success']);

    expect(container.textContent).not.toContain(sv['CSAccountActivate.description']);
    expect(container.textContent).not.toContain(sv['CSAccountActivate.step1.header']);
    expect(container.textContent).not.toContain(sv['CSAccountActivate.step1.description']);
    expect(container.textContent).not.toContain(sv['CSAccountActivate.step2.header']);
    expect(container.textContent).not.toContain(sv['CSAccountActivate.step2.description']);
    expect(container.textContent).not.toContain(sv['CSAccountActivate.step3.header']);
    expect(container.textContent).not.toContain(sv['CSAccountActivate.step3.description']);
    expect(container.textContent).not.toContain('CSAccountSavePasswordForm');
    expect(container.textContent).not.toContain(sv['CSAccountActivate.legalNote']);
  });
});
