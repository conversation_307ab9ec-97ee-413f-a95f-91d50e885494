/* eslint-env jest */

import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import CSAccountButton from '../../app/components/CSAccount/CSAccountButton';

describe('<CSAccountButton />', () => {
  const mockFn = jest.fn();

  it('should render correctly', () => {
    const { getByRole } = render(
      <IntlProvider locale="en">
        <CSAccountButton id="testButton" onClick={mockFn} disabled={false}>
          Test
        </CSAccountButton>
      </IntlProvider>
    );

    const button = getByRole('button', { name: /test/i });
    expect(button).toBeInTheDocument();
    expect(button).not.toBeDisabled();
  });


  it('should be disabled', () => {
    const { getByRole } = render(
      <IntlProvider locale="en">
        <CSAccountButton id="testButton" onClick={mockFn} disabled>
          Test
        </CSAccountButton>
      </IntlProvider>
    );
    const button = getByRole('button', { name: /test/i });
    expect(button).toBeDisabled();
  });

  it('should contain children - button label', () => {
    const { getByText } = render(
      <IntlProvider locale="en">
        <CSAccountButton id="testButton" onClick={mockFn} disabled={false}>
          Test
        </CSAccountButton>
      </IntlProvider>
    );

    expect(getByText('Test')).toBeInTheDocument();
  });


  it('should trigger onClick prop when clicked', () => {
    const { getByRole } = render(
      <IntlProvider locale="en">
        <CSAccountButton id="testButton" onClick={mockFn} disabled={false}>
          Test
        </CSAccountButton>
      </IntlProvider>
    );

    const button = getByRole('button', { name: /test/i });
    fireEvent.click(button);

    expect(mockFn).toHaveBeenCalled();
  });

  it('should render the chevron icon', () => {
    const { container } = render(
      <IntlProvider locale="en">
        <CSAccountButton id="testButton" onClick={mockFn} disabled={false}>
          Test
        </CSAccountButton>
      </IntlProvider>
    );

    const chevronIcon = container.querySelector('.fa-chevron-right');

    expect(chevronIcon).toBeInTheDocument();
  });
});
