/* eslint-env jest */

import React from 'react';
import { render } from '@testing-library/react';
import CSAccountPageHeader from '../../app/components/CSAccount/CSAccountPageHeader';

describe('CSAccountPageHeader', () => {
  // Test 1: Component is rendered
  it('renders without crashing', () => {
    render(<CSAccountPageHeader title="Test Title" />);
  });

  // Test 2: Prop 'title' is passed and used correctly
  it('uses the `title` prop correctly', () => {
    const { container } = render(<CSAccountPageHeader title="Test Title" />);
    const titleElement = container.querySelector('.master-page-title');

    expect(titleElement).toBeInTheDocument();
    expect(titleElement.textContent).toEqual('Test Title');
  });
});
