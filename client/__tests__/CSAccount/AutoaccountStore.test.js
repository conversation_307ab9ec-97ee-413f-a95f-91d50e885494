/* eslint-env jest */

import dispatcher from '../../app/Dispatcher';
import ActionTypes from '../../app/actions/ActionTypes';
import getAPIClient from '../../app/helpers/ApiClient';
import AutoaccountStore from '../../app/stores/AutoaccountStore';
import createAutoaccountFailed from '../../app/actions/actionCreators/AutoaccountCreateFailed';
import createAutoaccountSuccess from '../../app/actions/actionCreators/AutoaccountCreateSuccess';

// Default ApiClient mock with ok response
jest.mock('../../app/helpers/ApiClient', () => {
  const mockAPIClient = {
    post: jest.fn().mockResolvedValue({ data: { ok: true } }),
  };
  return jest.fn(() => mockAPIClient);
});

jest.mock('../../app/actions/actionCreators/AutoaccountCreateSuccess', () => jest.fn());
jest.mock('../../app/actions/actionCreators/AutoaccountCreateFailed', () => jest.fn());

describe('AutoaccountStore', () => {
  let store;

  beforeEach(() => {
    store = new AutoaccountStore(dispatcher);
    createAutoaccountSuccess.mockClear();
    createAutoaccountFailed.mockClear();
  });

  it('sets initial state', () => {
    const state = store.getInitialState();
    expect(state).toEqual({
      create_in_progress: false,
      errors: null,
    });
  });

  it('handles AUTOACCOUNT_CREATE_CLICKED action', () => {
    const initialState = store.getInitialState();
    const router = jest.fn();
    store[ActionTypes.AUTOACCOUNT_CREATE_CLICKED](initialState, { router });
    expect(initialState).toEqual({ errors: null, create_in_progress: true });
  });

  it('handles AUTOACCOUNT_CREATE_FAILED action', () => {
    const initialState = store.getInitialState();
    const action = { errors: { error: 'Test error' } };
    store[ActionTypes.AUTOACCOUNT_CREATE_FAILED](initialState, action);
    expect(initialState).toEqual({
      errors: { error: 'Test error' },
      create_in_progress: false,
    });
  });

  it('handles AUTOACCOUNT_CREATE_SUCCESS action and changes route', done => {
    const initialState = store.getInitialState();
    initialState.create_in_progress = true;
    const router = jest.fn();
    router.navigate = jest.fn();
    store[ActionTypes.AUTOACCOUNT_CREATE_SUCCESS](initialState, { router });
    expect(initialState).toEqual(store.getInitialState());
    // Wait for all promises to resolve
    setTimeout(() => {
      expect(router.navigate).toHaveBeenCalledWith('/activate-cs-account', {
        replace: true,
        state: { passwordSaveSucceeded: true },
      });
      // Checks if the state is reset to the initial state upon success
      done();
    }, 0);
  });

  it('createAutoaccount() should handle success properly', async () => {
    // Customize the mock implementation for this test case
    const expectedResponse = { ok: true };
    const mockAPIClient = {
      post: jest.fn().mockResolvedValue({ data: expectedResponse }),
    };
    // Override the getAPIClient function with the custom mockAPIClient
    getAPIClient.mockReturnValue(mockAPIClient);
    const router = jest.fn();
    await store.createAutoaccount(router);
    expect(createAutoaccountSuccess).toHaveBeenCalledTimes(1);
    expect(createAutoaccountFailed).toHaveBeenCalledTimes(0);
  });

  it('createAutoaccount() should handle fail properly', async () => {
    // Customize the mock implementation for this test case
    const expectedResponse = { ok: false, errors: {} };
    const mockAPIClient = {
      post: jest.fn().mockResolvedValue({ data: expectedResponse }),
    };
    // Override the getAPIClient function with the custom mockAPIClient
    getAPIClient.mockReturnValue(mockAPIClient);
    const router = jest.fn();
    await store.createAutoaccount(router);
    expect(createAutoaccountFailed).toHaveBeenCalledTimes(1);
    expect(createAutoaccountFailed).toHaveBeenCalledWith(expectedResponse.errors);
    expect(createAutoaccountSuccess).toHaveBeenCalledTimes(0);
  });

  it('createAutoaccount() should handle error properly', async () => {
    // Customize the mock implementation for this test case
    const error = new Error('Test error');
    const mockAPIClient = {
      post: jest.fn().mockRejectedValue(error),
    };
    // Override the getAPIClient function with the custom mockAPIClient
    getAPIClient.mockReturnValue(mockAPIClient);
    const router = jest.fn();
    await store.createAutoaccount(router);
    expect(createAutoaccountFailed).toHaveBeenCalledTimes(1);
    expect(createAutoaccountFailed).toHaveBeenCalledWith(error);
    expect(createAutoaccountSuccess).toHaveBeenCalledTimes(0);
  });

  it('createAutoaccount() should handle unknown error', async () => {
    // Customize the mock implementation for this test case
    const errors = { errors: { error: 'Unknown error' } };
    const mockAPIClient = {
      post: jest.fn().mockResolvedValue({ data: errors }),
    };
    // Override the getAPIClient function with the custom mockAPIClient
    getAPIClient.mockReturnValue(mockAPIClient);
    const router = jest.fn();
    await store.createAutoaccount(router);
    expect(createAutoaccountFailed).toHaveBeenCalledTimes(1);
    expect(createAutoaccountFailed).toHaveBeenCalledWith(errors);
    expect(createAutoaccountSuccess).toHaveBeenCalledTimes(0);
  });
});
