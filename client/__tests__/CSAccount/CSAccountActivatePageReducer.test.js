/* eslint-env jest */

import chai, { expect } from 'chai';
import chaiExclude from 'chai-exclude';
import ActionTypes from '../../app/actions/ActionTypes';
import {
  CSAccountActivatePageReducer,
  initialState,
} from '../../app/components/CSAccount/CSAccountActivatePageReducer';

chai.use(chaiExclude);

describe('CSAccountActivatePageReducer', () => {
  it('should handle CSACCOUNT_CHANGED_INPUT_PASSWORD action', () => {
    const action = {
      type: ActionTypes.CSACCOUNT_CHANGED_INPUT_PASSWORD,
      password: 'newPassword',
    };
    const nextState = CSAccountActivatePageReducer(initialState, action);
    expect(nextState.password).to.equal('newPassword');
    expect(nextState)
      .excludingEvery(['password'])
      .to.deep.equal(initialState);
  });

  it('should handle CSACCOUNT_PASSWORD_SAVE_STARTED action', () => {
    const action = {
      type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED,
    };
    const nextState = CSAccountActivatePageReducer(initialState, action);
    expect(nextState.passwordSaveStarted).to.equal(true);
    expect(nextState)
      .excludingEvery(['passwordSaveStarted'])
      .to.deep.equal(initialState);
  });

  it('should handle CSACCOUNT_PASSWORD_SAVE_SUCCEEDED action', () => {
    const action = {
      type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED,
    };
    const nextState = CSAccountActivatePageReducer(initialState, action);
    expect(nextState.passwordSaveSucceeded).to.equal(true);
    expect(nextState)
      .excludingEvery(['passwordSaveSucceeded'])
      .to.deep.equal(initialState);
  });

  it('should handle CSACCOUNT_PASSWORD_SAVE_VALIDATION_FAILED action', () => {
    const errors = { pwd_error: 'authentication failed' };
    const action = {
      type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_VALIDATION_FAILED,
      payload: errors,
    };
    const nextState = CSAccountActivatePageReducer(initialState, action);
    expect(nextState.hasValidationErrors).to.equal(true);
    expect(nextState.errors).to.deep.equal(errors);
    expect(nextState)
      .excludingEvery(['hasValidationErrors', 'errors'])
      .to.deep.equal(initialState);
  });

  it('should handle CSACCOUNT_PASSWORD_SAVE_FAILED action', () => {
    const errors = { error: 'Unknown error' };
    const action = {
      type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED,
      payload: errors,
    };
    const nextState = CSAccountActivatePageReducer(initialState, action);
    expect(nextState.passwordSaveFailed).to.equal(true);
    expect(nextState.errors).to.deep.equal(errors);
    expect(nextState)
      .excludingEvery(['passwordSaveFailed', 'errors'])
      .to.deep.equal(initialState);
  });

  it('should throw an error for unknown action types', () => {
    const unknownAction = { type: 'UNKNOWN_ACTION' };

    expect(() => CSAccountActivatePageReducer(initialState, unknownAction)).to.throw(
      Error,
      `Unknown action: ${unknownAction.type}`
    );
  });
});
