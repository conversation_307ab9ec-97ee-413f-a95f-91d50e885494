/* eslint-env jest */

import React from 'react';
import { render, screen } from '@testing-library/react';
import CSAccountContext, {
  useCSAccountContext,
} from '../../app/components/CSAccount/CSAccountContext';

describe('CSAccountContext.Provider', () => {
  it('should render children correctly', () => {
    const mockContextValue = { foo: 'bar' };

    render(
      <CSAccountContext.Provider value={mockContextValue}>
        <div>Child Component</div>
      </CSAccountContext.Provider>
    );

    expect(screen.getByText('Child Component')).toBeInTheDocument();
  });

  it('should pass the correct context value to consumers', () => {
    const mockContextValue = { foo: 'bar' };

    const ConsumerComponent = () => {
      const contextValue = useCSAccountContext();
      return <div>{contextValue.foo}</div>;
    };

    render(
      <CSAccountContext.Provider value={mockContextValue}>
        <ConsumerComponent />
      </CSAccountContext.Provider>
    );

    expect(screen.getByText('bar')).toBeInTheDocument();
  });
});
