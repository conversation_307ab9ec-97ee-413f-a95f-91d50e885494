/* eslint-env jest */

import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import CSAccountSavePasswordForm from '../../app/components/CSAccount/CSAccountSavePasswordForm';
import CSAccountMessages from '../../app/components/CSAccount/CSAccountMessages';
import {
  handleOnChangePasswordField,
  handleSaveButtonClick,
} from '../../app/components/CSAccount/CSAccountActivatePageReducer';
import { useCSAccountContext } from '../../app/components/CSAccount/CSAccountContext';
import sv from '../../app/localizations/sv.json';


jest.mock('../../app/components/CSAccount/CSAccountActivatePageReducer.js', () => ({
  handleOnChangePasswordField: jest.fn(),
  handleSaveButtonClick: jest.fn(),
}));

jest.mock('../../app/components/CSAccount/CSAccountContext.js', () => ({
  useCSAccountContext: jest.fn().mockReturnValue({
    state: {
      password: '',
      passwordSaveStarted: false,
      passwordSaveFailed: false,
      hasValidationErrors: false,
      errors: {},
    },
    dispatch: jest.fn(),
  }),
}));

describe('CSAccountSavePasswordForm', () => {
  it('should render itself with password field, button, forgot password link, help texts', () => {
    const { container, getByText } = render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );

    expect(getByText(sv['CSAccountActivate.passwordField'])).toBeInTheDocument();
    expect(getByText(sv['CSAccountActivate.button'])).toBeInTheDocument();
    expect(getByText('Behöver du hjälp? Kontakta')).toBeInTheDocument();

    const input = container.querySelector('input[id="save_cs_password_input"]');
    expect(input).toHaveAttribute('placeholder', sv['CSAccountActivate.passwordField.placeholder']);

    const emailLink = getByText('<EMAIL>');
    expect(emailLink).toBeInTheDocument();
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');

    const forgotLink = getByText(sv['CSAccountActivate.forgotPassword.title']);
    expect(forgotLink).toBeInTheDocument();
    expect(forgotLink).toHaveAttribute('href', sv['CSAccountActivate.forgotPassword.link']);
  });

  it('input field should be initially empty', () => {
    const { container } = render(
      <IntlProvider locale="en">
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );
    const input = container.querySelector('input[id="save_cs_password_input"]');
    expect(input).toHaveValue('');
  });

  it('should handle save button click', () => {
    const { getByText } = render(
      <IntlProvider locale="en">
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );
    fireEvent.click(getByText('Save'));
    expect(handleSaveButtonClick).toBeCalled();
  });

  it('should handle password input change', () => {
    const { container } = render(
      <IntlProvider locale="en">
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );
    const input = container.querySelector('input[id="save_cs_password_input"]');
    fireEvent.change(input, {
      target: { value: 'test' },
    });
    expect(handleOnChangePasswordField.mock.calls[0][0]).toBe('test');
  });

  it('should handle form submission', () => {
    const { getByRole } = render(
      <IntlProvider locale="en">
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );
    fireEvent.submit(getByRole('form', { name: /save password form/i }));
    expect(handleSaveButtonClick).toHaveBeenCalled();
  });

  it('should display validation error message when hasValidationErrors is true', () => {
    const mockCSAccountContext = {
      state: {
        password: '',
        passwordSaveStarted: false,
        passwordSaveFailed: false,
        hasValidationErrors: true,
        errors: { password: ['required field'] },
      },
      dispatch: jest.fn(),
    };
    useCSAccountContext.mockReturnValue(mockCSAccountContext);
    const { getByText } = render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );
    expect(getByText(sv['CSAccountActivate.validation.field.required'])).toBeInTheDocument();
  });

  it('should display password save failed error message when passwordSaveFailed is true', () => {
    const mockCSAccountContext = {
      state: {
        password: '',
        passwordSaveStarted: false,
        passwordSaveFailed: true,
        hasValidationErrors: false,
        errors: {},
      },
      dispatch: jest.fn(),
    };
    useCSAccountContext.mockReturnValue(mockCSAccountContext);
    const { getByText } = render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );
    expect(getByText(sv['CSAccountActivate.invalid'])).toBeInTheDocument();
  });

  // eslint-disable-next-line max-len
  it('should display active creditsafe account error message when passwordSaveFailed is true and errors.active_exists_error exists', () => {
    const mockCSAccountContext = {
      state: {
        password: '',
        passwordSaveStarted: false,
        passwordSaveFailed: true,
        hasValidationErrors: false,
        errors: {
          active_exists_error: {
            en: 'This is a active_exists_error en',
            sv: 'This is a active_exists_error sv',
          },
        },
      },
      dispatch: jest.fn(),
    };
    useCSAccountContext.mockReturnValue(mockCSAccountContext);
    const { getByText } = render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountSavePasswordForm messages={CSAccountMessages} />
      </IntlProvider>
    );
    expect(getByText(sv['CSAccountActivate.active_exists_error'])).toBeInTheDocument();
  });
});
