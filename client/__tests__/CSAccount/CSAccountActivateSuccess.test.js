/* eslint-env jest */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { MemoryRouter } from 'react-router-dom';
// eslint-disable-next-line max-len
import CSAccountActivateSuccess from '../../app/components/CSAccount/CSAccountActivateSuccess';


// Mock the useNavigate hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock the Message component
jest.mock('../../app/components/shared/Message.jsx', () => ({
  __esModule: true,
  default: ({ id }) => <span>{id}</span>,
}));

// Mock the CSAccountButton component
jest.mock('../../app/components/CSAccount/CSAccountButton', () => ({
  __esModule: true,
  default: ({ onClick, children }) => (
    <button onClick={onClick}>{children}</button>
  ),
}));

const renderWithIntl = (component) => {
  return render(
    <IntlProvider locale="en" messages={{}}>
      <MemoryRouter>
        {component}
      </MemoryRouter>
    </IntlProvider>
  );
};

describe('CSAccountActivateSuccess test', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the success message', () => {
    renderWithIntl(<CSAccountActivateSuccess />);

    expect(screen.getByText('cs_activation_success_message')).toBeInTheDocument();
  });

  it('renders the redirect button', () => {
    renderWithIntl(<CSAccountActivateSuccess />);

    expect(screen.getByText('Go to BOL service')).toBeInTheDocument();
  });

  it('calls router.navigate when redirect button is clicked', () => {
    renderWithIntl(<CSAccountActivateSuccess />);

    const redirectButton = screen.getByText('Go to BOL service');
    fireEvent.click(redirectButton);

    expect(mockNavigate).toHaveBeenCalledTimes(1);
    expect(mockNavigate).toHaveBeenCalledWith('/projects', { replace: true });
  });
});
