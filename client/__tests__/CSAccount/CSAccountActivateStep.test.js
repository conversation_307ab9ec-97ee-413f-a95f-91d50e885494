/* eslint-env jest */

import React from 'react';
import { render, screen } from '@testing-library/react';
import CSAccountActivateStep from '../../app/components/CSAccount/CSAccountActivateStep';

describe('CSAccountActivateStep', () => {
  const defaultProps = {
    stepNumber: '1',
    title: 'Test Title',
    description: 'Test Description',
  };

  it('renders the step number', () => {
    render(<CSAccountActivateStep {...defaultProps} />);
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('renders the title', () => {
    render(<CSAccountActivateStep {...defaultProps} />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('renders the description', () => {
    render(<CSAccountActivateStep {...defaultProps} />);
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });

  it('renders children when provided', () => {
    render(
      <CSAccountActivateStep {...defaultProps}>
        <div data-testid="child-element">Child Content</div>
      </CSAccountActivateStep>
    );
    expect(screen.getByTestId('child-element')).toBeInTheDocument();
    expect(screen.getByText('Child Content')).toBeInTheDocument();
  });

  it('renders with an icon when provided', () => {
    const propsWithIcon = {
      ...defaultProps,
      icon: 'test-icon',
    };
    render(<CSAccountActivateStep {...propsWithIcon} />);
    const iconElement = screen.getByTestId('step-icon');
    expect(iconElement).toBeInTheDocument();
    expect(iconElement).toHaveClass('img-icon');
  });

  it('does not render an icon when not provided', () => {
    render(<CSAccountActivateStep {...defaultProps} />);
    expect(screen.queryByTestId('img-icon')).not.toBeInTheDocument();
  });
});
