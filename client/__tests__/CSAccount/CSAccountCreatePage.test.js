/* eslint-env jest */

import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, fireEvent, act, cleanup } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import createAutoaccount from '../../app/actions/actionCreators/AutoaccountCreate';
import CSAccountCreatePage from '../../app/components/CSAccount/CSAccountCreatePage';
import { authStore, autoaccountStore } from '../../app/stores/Stores';
import StoreSubscription from '../../app/helpers/StoreSubscription';

jest.mock('../../app/stores/Stores', () => ({
  authStore: {
    getState: jest.fn(() => ({ auth_info: { userProfile: {} } })),
    addChangeListener: jest.fn(),
    removeChangeListener: jest.fn(),
    subscribe: jest.fn(),
    unsubscribe: jest.fn(),
  },
  autoaccountStore: {
    getState: jest.fn(() => ({ errors: {} })),
    addChangeListener: jest.fn(),
    removeChangeListener: jest.fn(),
    subscribe: jest.fn(),
    unsubscribe: jest.fn(),
  },
}));

let mockActivate;
let mockDeactivate;

jest.mock('../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => {
    mockActivate = jest.fn();
    mockDeactivate = jest.fn();
    return {
      activate: mockActivate,
      deactivate: mockDeactivate,
    };
  })
);

jest.mock('../../app/actions/actionCreators/AutoaccountCreate', () => jest.fn());

jest.mock('../../app/Config', () => ({ changeUserDetailsURL: 'mockChangeUserDetailsURL' }));

const mockIntl = {
  formatMessage: jest.fn((obj) => obj.defaultMessage),
};

describe('<CSAccountCreatePage />', () => {

  const mockRouter = { navigate: jest.fn() };

  const renderComponent = () => {
    return render(
      <IntlProvider locale="en" messages={{}}>
        <MemoryRouter>
          <CSAccountCreatePage intl={mockIntl} router={mockRouter} />
        </MemoryRouter>
      </IntlProvider>
    );
  };

  beforeEach(() => {
    authStore.getState.mockReturnValue({
      auth_info: {
        userProfile: {
          email: '<EMAIL>',
          phone: '**********',
        },
      },
    });
    autoaccountStore.getState.mockReturnValue({
      errors: null,
      create_in_progress: false,
    });
  });

  it('renders without crashing', () => {
    renderComponent();
    expect(screen.getByText('Create Creditsafe account main text')).toBeInTheDocument();
  });

  it('calls createAutoaccount when submit button is clicked', () => {
    renderComponent();
    const submitButton = screen.getByRole('button', { name: 'Create Creditsafe account' }); //screen.getByText('Create Creditsafe account');
    fireEvent.click(submitButton);
    expect(createAutoaccount).toHaveBeenCalled();
  });

  it('disables submit button when create is in progress', () => {
    autoaccountStore.getState.mockReturnValue({
      errors: null,
      create_in_progress: true,
    });
    renderComponent();
    const submitButton = screen.getByRole('button', { name: 'Create Creditsafe account' });
    expect(submitButton).toBeDisabled();
  });

  it('displays account already exists error when present', () => {
    autoaccountStore.getState.mockReturnValue({
      errors: {
        active_exists_error: ['Account already exists'],
      },
      create_in_progress: false,
    });
    renderComponent();
    expect(screen.getByText('There is already an active Creditsafe account for this organisation. Go to Home and then back to ID06 Bolagsdeklaration to continue.')).toBeInTheDocument();
  });

  it('displays pending account exists error when present', () => {
    autoaccountStore.getState.mockReturnValue({
      errors: {
        pending_exists_error: ['Pending account exists'],
      },
      create_in_progress: false,
    });
    renderComponent();
    expect(screen.getByText('There is pending Creditsafe account for this organisation. Go to Home and then back to ID06 Bolagsdeklaration to continue.')).toBeInTheDocument();
  });

  it('displays Creditsafe error when present', () => {
    autoaccountStore.getState.mockReturnValue({
      errors: {
        cs_error: {en: 'Creditsafe error en', sv: 'Creditsafe sv'},
      },
      create_in_progress: false,
    });
    renderComponent();
    expect(screen.getByText('Error message CS autoaccount')).toBeInTheDocument();
  });

  it('displays foreign company error when present', () => {
    autoaccountStore.getState.mockReturnValue({
      errors: {
        f_tax_error: ['Foreign company error'],
      },
      create_in_progress: false,
    });
    renderComponent();
    expect(screen.getByText('Error message CS foreign company')).toBeInTheDocument();
  });

  it('updates state when stores change', async () => {
    // Set up initial mock store states
    authStore.getState.mockReturnValue({
      auth_info: {
        userProfile: {
          email: '<EMAIL>',
          phone: '**********',
        },
      },
    });
    autoaccountStore.getState.mockReturnValue({
      errors: null,
      create_in_progress: false,
    });

    // Render the component
    act(() => {
      render(
        <IntlProvider messages={{}} locale="en">
          <MemoryRouter>
            <CSAccountCreatePage intl={mockIntl} router={mockRouter} />
          </MemoryRouter>
        </IntlProvider>
      );
    });

    // Update mock store states
    authStore.getState.mockReturnValue({
      auth_info: {
        userProfile: {
          email: '<EMAIL>',
          phone: '**********',
        },
      },
    });
    autoaccountStore.getState.mockReturnValue({
      errors: {
        cs_error: {en: 'New Creditsafe error en', sv: 'New Creditsafe error sv'},
      },
      create_in_progress: true,
    });

    // Simulate store changes by calling the subscription callbacks
    act(() => {
      StoreSubscription.mock.calls.forEach(([, callback]) => {
        callback();
      });
    });

    // Check updated state
    expect(screen.getByText('Error message CS autoaccount')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Create Creditsafe account' })).toBeDisabled();
  });

  it('should check if store subscriptions are being made properly', () => {
    StoreSubscription.mockClear();
    renderComponent();

    expect(StoreSubscription).toHaveBeenCalledTimes(2);
    expect(StoreSubscription).toHaveBeenNthCalledWith(1, authStore, expect.any(Function));
    expect(StoreSubscription).toHaveBeenNthCalledWith(2, autoaccountStore, expect.any(Function));
  });

  // eslint-disable-next-line max-len
  it('should check if store subscriptions are being activated on mount and deactivated on unmount', () => {
    renderComponent();
    expect(mockActivate).toHaveBeenCalledTimes(1);
    cleanup();
    expect(mockDeactivate).toHaveBeenCalledTimes(1);
  });
});
