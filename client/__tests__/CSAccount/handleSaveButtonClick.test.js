/* eslint-env jest */

import ActionTypes from '../../app/actions/ActionTypes';
// eslint-disable-next-line max-len
import { handleSaveButtonClick } from '../../app/components/CSAccount/CSAccountActivatePageReducer';
import getAPIClient from '../../app/helpers/ApiClient';

const dispatch = jest.fn();

// Default ApiClient mock with ok response
jest.mock('../../app/helpers/ApiClient', () => {
  const mockAPIClient = {
    post: jest.fn().mockResolvedValue({ data: { ok: true } }),
  };
  return jest.fn(() => mockAPIClient);
});

test('should dispatch CSACCOUNT_PASSWORD_SAVE_SUCCEEDED action on successful response', async () => {
  const state = { password: 'password123' };

  await handleSaveButtonClick(state, dispatch);

  expect(dispatch).toHaveBeenCalledWith({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED });
  expect(dispatch).toHaveBeenCalledWith({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED });
});

test('should dispatch CSACCOUNT_PASSWORD_SAVE_FAILED action on failed response', async () => {
  const state = { password: 'password123' };

  // Customize the mock implementation for this test case
  const mockAPIClient = {
    post: jest.fn().mockRejectedValue(new Error('Failed to save password')),
  };

  // Override the getAPIClient function with the custom mockAPIClient
  getAPIClient.mockReturnValue(mockAPIClient);

  await handleSaveButtonClick(state, dispatch);

  expect(dispatch).toHaveBeenCalledWith({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED });
  expect(dispatch).toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED,
    payload: { error: new Error('Failed to save password') },
  });
  expect(dispatch).not.toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED,
  });
});

// eslint-disable-next-line max-len
test('should dispatch CSACCOUNT_PASSWORD_SAVE_VALIDATION_FAILED action on response with password validation errors', async () => {
  const state = { password: '' };
  const validationErrors = { password: 'required field' };

  // Customize the mock implementation for this test case
  const mockAPIClient = {
    post: jest.fn().mockResolvedValue({ data: { errors: validationErrors } }),
  };

  // Override the getAPIClient function with the custom mockAPIClient
  getAPIClient.mockReturnValue(mockAPIClient);

  await handleSaveButtonClick(state, dispatch);

  expect(dispatch).toHaveBeenCalledWith({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED });
  expect(dispatch).toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_VALIDATION_FAILED,
    payload: validationErrors,
  });
  expect(dispatch).not.toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED,
  });
});

test('should dispatch CSACCOUNT_PASSWORD_SAVE_FAILED action on response with unknown error', async () => {
  const state = { password: 'password123' };

  // Customize the mock implementation for this test case
  const mockAPIClient = {
    post: jest.fn().mockResolvedValue({ data: { ok: false } }),
  };

  // Override the getAPIClient function with the custom mockAPIClient
  getAPIClient.mockReturnValue(mockAPIClient);

  await handleSaveButtonClick(state, dispatch);

  expect(dispatch).toHaveBeenCalledWith({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED });
  expect(dispatch).toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED,
    payload: { error: 'Unknown error' },
  });
  expect(dispatch).not.toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED,
  });
});

// eslint-disable-next-line max-len
test('should dispatch CSACCOUNT_PASSWORD_SAVE_FAILED action on response with HTTP 401 status', async () => {
  const state = { password: 'password123' };

  // Customize the mock implementation for this test case
  const mockAPIClient = {
    post: jest.fn().mockResolvedValue({ status: 401 }),
  };

  // Override the getAPIClient function with the custom mockAPIClient
  getAPIClient.mockReturnValue(mockAPIClient);

  await handleSaveButtonClick(state, dispatch);

  expect(dispatch).toHaveBeenCalledWith({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED });
  expect(dispatch).toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED,
    payload: { error: 'Unknown error' },
  });
  expect(dispatch).not.toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED,
  });
});

test('should dispatch CSACCOUNT_PASSWORD_SAVE_FAILED action on other error in data', async () => {
  const state = { password: 'password123' };
  const errorText = 'error text';

  // Customize the mock implementation for this test case
  const mockAPIClient = {
    post: jest.fn().mockResolvedValue({ data: { errors: errorText } }),
  };

  // Override the getAPIClient function with the custom mockAPIClient
  getAPIClient.mockReturnValue(mockAPIClient);

  await handleSaveButtonClick(state, dispatch);

  expect(dispatch).toHaveBeenCalledWith({ type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_STARTED });
  expect(dispatch).toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_FAILED,
    payload: errorText,
  });
  expect(dispatch).not.toHaveBeenCalledWith({
    type: ActionTypes.CSACCOUNT_PASSWORD_SAVE_SUCCEEDED,
  });
});
