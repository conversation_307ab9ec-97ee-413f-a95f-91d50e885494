/* eslint-env jest */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { IntlProvider, FormattedMessage } from 'react-intl';
import CSAccountCreateError from '../../app/components/CSAccount/CSAccountCreateError';
import sv from '../../app/localizations/sv.json';

describe('<CSAccountCreateError />', () => {
  it('renders without errors', () => {
    render(
      <IntlProvider locale="en">
        <CSAccountCreateError />
      </IntlProvider>
    );
  });

  it('should show foreign company error when showForeignCompanyError prop is true', () => {
    render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountCreateError showForeignCompanyError />
      </IntlProvider>
    );
    expect(screen.getByText(/Företaget måste registrera ett svenskt skattenummer hos ID06 för att få skapa ett/i)).toBeInTheDocument();
    expect(screen.queryByText(/Ett fel uppstod när ert Creditsafe-konto skulle skapas/i)).not.toBeInTheDocument();
  });

  // eslint-disable-next-line max-len
  it('should show autoaccount error when showAutoaccountError prop is true and other types of errors are false', () => {
    render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountCreateError
          showAutoaccountError
          showAccountAlreadyExistsError={false}
          showAccountPendingExistsError={false}
          showForeignCompanyError={false}
        />
      </IntlProvider>
    );
    expect(
      screen.queryByText((content, element) => {
        return (
          content.includes('Ett fel uppstod när ert Creditsafe-konto skulle skapas') &&
          content.includes('så hjälper vi dig vidare') &&
          element.querySelector('a[href="mailto:<EMAIL>"]')
        );
      })
    ).toBeInTheDocument();
  });


  // eslint-disable-next-line max-len
  it('should show autoaccount error message from CS when autoaccountErrorMsgFromCS prop is not null', () => {
    render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountCreateError showAutoaccountError autoaccountErrorMsgFromCS={{ en: 'error' }} />
      </IntlProvider>
    );
    expect(screen.getByText(/Felmeddelande från Creditsafe:/i)).toBeInTheDocument();
    expect(screen.getByText(/Ett fel uppstod när ert Creditsafe-konto skulle skapas/i)).toBeInTheDocument();
  });

  // eslint-disable-next-line max-len
  it('should show "account already exists" error when showAccountAlreadyExistsError prop is true', () => {
    render(
      <IntlProvider locale="sv" messages={sv}>
        <CSAccountCreateError showAccountAlreadyExistsError />
      </IntlProvider>
    );
    expect(screen.getByText(sv['createCSAccount.errorAccountAlreadyExists'])).toBeInTheDocument();
    expect(screen.queryByText(/Ett fel uppstod när ert Creditsafe-konto skulle skapas/i)).not.toBeInTheDocument();
  });

    // eslint-disable-next-line max-len
    it('should show "pending cs account exists" error when showAccountPendingExistsError prop is true', () => {
      render(
        <IntlProvider locale="sv" messages={sv}>
          <CSAccountCreateError showAccountPendingExistsError />
        </IntlProvider>
      );
      expect(screen.getByText(sv['createCSAccount.errorAccountPendingExists'])).toBeInTheDocument();
      expect(screen.queryByText((content, element) => {
        return (
          content.includes('Ett fel uppstod när ert Creditsafe-konto skulle skapas') &&
          content.includes('så hjälper vi dig vidare') &&
          element.querySelector('a[href="mailto:<EMAIL>"]')
        );
      })).not.toBeInTheDocument();
    });
});
