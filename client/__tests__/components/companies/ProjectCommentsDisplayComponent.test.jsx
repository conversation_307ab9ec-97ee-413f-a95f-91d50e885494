/* global jest, describe, it, expect, beforeEach, afterEach */
import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, fireEvent } from '@testing-library/react';
import ProjectCommentsDisplayComponent from
  '../../../app/components/companies/ProjectCommentsDisplayComponent';

// Mock messages for IntlProvider
const messages = {
  'comments.by': 'by',
  'comments.edit': 'edit',
  'comments.delete': 'delete',
  'comments.deleted': 'deleted',
  'comments.updated': 'updated',
  'comments.emptyList': 'There are no comments for this supplier in this project.',
  'comments.loadingFailed': 'Failed to load comments',
};

// Mock the action creators
jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsOpenDeleteConfirmation', () =>
  jest.fn()
);
jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsToggleEdit', () =>
  jest.fn()
);

import companyProjectCommentsOpenDeleteConfirmation
  from '../../../app/actions/actionCreators/CompanyProjectCommentsOpenDeleteConfirmation';
import companyProjectCommentsToggleEdit
  from '../../../app/actions/actionCreators/CompanyProjectCommentsToggleEdit';

describe('ProjectCommentsDisplayComponent', () => {
  const mockComments = [
    {
      id: '1',
      author: 'John Doe, Test Company Ltd',
      comment: 'This is a test comment',
      created_timestamp: '2023-01-01T12:00:00Z',
      updated_timestamp: null,
      deleted_timestamp: null,
      is_read: false,
      permissions: ['update', 'delete'],
    },
    {
      id: '2',
      author: 'Jane Smith, Another Company Inc',
      comment: 'This is an updated comment',
      created_timestamp: '2023-01-01T10:00:00Z',
      updated_timestamp: '2023-01-01T11:00:00Z',
      updater: 'Jane Smith',
      deleted_timestamp: null,
      is_read: true,
      permissions: [],
    },
    {
      id: '3',
      author: 'Bob Wilson, Deleted Company',
      comment: 'This comment was deleted',
      created_timestamp: '2023-01-01T09:00:00Z',
      deleted_timestamp: '2023-01-01T10:00:00Z',
      deleter: 'Admin User',
      is_read: true,
      permissions: [],
    },
  ];

  const mockStoreComments = [
    {
      loading: false,
      editing: false,
      errors: [],
      data: {
        id: '1',
        author: 'John Doe, Test Company Ltd',
        comment: 'This is a test comment',
        created_timestamp: '2023-01-01T12:00:00Z',
        updated_timestamp: null,
        deleted_timestamp: null,
        is_read: false,
        permissions: ['update', 'delete'],
      },
    },
  ];

  const renderComponent = (props = {}, locale = 'en') => {
    const defaultProps = {
      comments: mockComments,
      readOnly: false,
      ...props,
    };

    // Suppress prop-types warnings in tests by providing default messages
    const testMessages = {
      ...messages,
      // Add any missing message keys that might be used
    };

    return render(
      <IntlProvider locale={locale} messages={testMessages} defaultLocale="en">
        <ProjectCommentsDisplayComponent {...defaultProps} />
      </IntlProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers().setSystemTime(new Date('2023-01-02T00:00:00Z'));

    // Suppress console.error for prop-types warnings in tests
    jest.spyOn(console, 'error').mockImplementation((message, ...args) => {
      if (typeof message === 'string' && message.includes('Failed prop type')) {
        return; // Suppress prop-types warnings
      }
      // Call the original console.error for other messages
      console.error.mockRestore();
      console.error(message, ...args);
      jest.spyOn(console, 'error').mockImplementation(console.error);
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    // Restore console.error
    if (console.error.mockRestore) {
      console.error.mockRestore();
    }
  });

  describe('Read-only mode', () => {
    it('should render comments in read-only mode without interactive elements', () => {
      renderComponent({ readOnly: true });

      // Should show comment content
      expect(screen.getByText('This is a test comment')).toBeInTheDocument();
      expect(screen.getByText('This is an updated comment')).toBeInTheDocument();

      // Should NOT show edit/delete buttons
      expect(screen.queryByText('edit')).not.toBeInTheDocument();
      expect(screen.queryByText('delete')).not.toBeInTheDocument();
      expect(screen.queryByRole('link')).not.toBeInTheDocument();
    });

    it('should extract and display company names from author in read-only mode', () => {
      renderComponent({ readOnly: true });

      // Should show company names extracted from author
      expect(screen.getByText(/Test Company Ltd/)).toBeInTheDocument();
      expect(screen.getByText(/Another Company Inc/)).toBeInTheDocument();
      expect(screen.getByText(/Deleted Company/)).toBeInTheDocument();
    });

    it('should not show unread indicators in read-only mode', () => {
      renderComponent({ readOnly: true });

      // Should not show the unread indicator (fa-circle new-indicator)
      const unreadIndicators = document.querySelectorAll('.fa-circle.new-indicator');
      expect(unreadIndicators).toHaveLength(0);
    });

    it('should use comments-print-view class in read-only mode', () => {
      const { container } = renderComponent({ readOnly: true });

      // Should use print-view class instead of table styling
      expect(container.querySelector('.comments-print-view')).toBeInTheDocument();
      expect(container.querySelector('.pseudo-table')).not.toBeInTheDocument();
      expect(container.querySelector('.table-striped')).not.toBeInTheDocument();
    });

    it('should display timestamps in read-only mode', () => {
      renderComponent({ readOnly: true });

      // Should show formatted timestamps (ISO format: 2023-01-01 12:00)
      expect(screen.getByText(/2023-01-01 12:00.*Test Company Ltd/)).toBeInTheDocument();
      expect(screen.getAllByText(/2023-01-01 10:00/)).toHaveLength(2); // One in metadata, one in deleted text
      expect(screen.getByText(/2023-01-01 09:00.*Deleted Company/)).toBeInTheDocument();
    });

    it('should handle deleted comments in read-only mode', () => {
      renderComponent({ readOnly: true });

      // Should show deleted comment message
      expect(screen.getByText(/deleted/)).toBeInTheDocument();
    });

    it('should handle comments without company names in read-only mode', () => {
      const commentsWithoutCompany = [
        {
          id: '1',
          author: 'John Doe', // No comma, no company
          comment: 'Comment without company',
          created_timestamp: '2023-01-01T12:00:00Z',
          is_read: true,
          permissions: [],
        },
      ];

      renderComponent({ comments: commentsWithoutCompany, readOnly: true });

      // Should still render the comment
      expect(screen.getByText('Comment without company')).toBeInTheDocument();
      // Should show timestamp but no company name
      expect(screen.getByText(/2023-01-01 12:00/)).toBeInTheDocument();
    });
  });

  describe('Interactive mode', () => {
    it('should render comments in interactive mode with edit/delete buttons', () => {
      renderComponent({ readOnly: false });

      // Should show edit/delete buttons for comments with permissions
      expect(screen.getByText('edit')).toBeInTheDocument();
      expect(screen.getByText('delete')).toBeInTheDocument();
    });

    it('should show unread indicators in interactive mode', () => {
      renderComponent({ readOnly: false });

      // Should show the unread indicator for unread comments
      const unreadIndicators = document.querySelectorAll('.fa-circle.new-indicator');
      expect(unreadIndicators.length).toBeGreaterThan(0);
    });

    it('should use table styling in interactive mode', () => {
      const { container } = renderComponent({ readOnly: false });

      // Should use table styling
      expect(container.querySelector('.pseudo-table.table-striped')).toBeInTheDocument();
      expect(container.querySelector('.comments-print-view')).not.toBeInTheDocument();
    });

    it('should handle edit button clicks in interactive mode', () => {
      renderComponent({ readOnly: false });

      const editButton = screen.getByText('edit').closest('a');
      fireEvent.click(editButton);

      expect(companyProjectCommentsToggleEdit).toHaveBeenCalledWith('1', true);
    });

    it('should handle delete button clicks in interactive mode', () => {
      renderComponent({ readOnly: false });

      const deleteButton = screen.getByText('delete').closest('a');
      fireEvent.click(deleteButton);

      expect(companyProjectCommentsOpenDeleteConfirmation).toHaveBeenCalledWith('1');
    });
  });

  describe('Data structure handling', () => {
    it('should handle store-format comments (with data wrapper)', () => {
      renderComponent({ comments: mockStoreComments, readOnly: true });

      // Should render comment from data wrapper
      expect(screen.getByText('This is a test comment')).toBeInTheDocument();
      expect(screen.getByText(/Test Company Ltd/)).toBeInTheDocument();
    });

    it('should handle direct comment objects', () => {
      renderComponent({ comments: mockComments, readOnly: true });

      // Should render direct comment objects
      expect(screen.getByText('This is a test comment')).toBeInTheDocument();
      expect(screen.getByText(/Test Company Ltd/)).toBeInTheDocument();
    });
  });

  describe('Empty state', () => {
    it('should show empty message when no comments', () => {
      renderComponent({ comments: [], readOnly: true });

      expect(screen.getByText('There are no comments for this supplier in this project.')).toBeInTheDocument();
    });

    it('should show empty message when comments is undefined', () => {
      // Override the default props to explicitly pass undefined
      const props = { readOnly: true };
      const defaultProps = {
        comments: undefined, // Explicitly undefined
        readOnly: false,
        ...props,
      };

      render(
        <IntlProvider locale="en" messages={messages} defaultLocale="en">
          <ProjectCommentsDisplayComponent {...defaultProps} />
        </IntlProvider>
      );

      expect(screen.getByText('There are no comments for this supplier in this project.')).toBeInTheDocument();
    });
  });
});
