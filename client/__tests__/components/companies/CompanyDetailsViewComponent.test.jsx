import React from 'react';
import { IntlProvider } from 'react-intl';
import { render } from '@testing-library/react';
// eslint-disable-next-line max-len
import CompanyDetailsViewComponent from '../../../app/components/companies/CompanyDetailsViewComponent';
import en from '../../../app/localizations/en.json';
import sv from '../../../app/localizations/sv.json';
import {companyViewStore} from "../../../app/stores/Stores";

jest.mock('../../../app/stores/Stores', () => ({
  authStore: {
    getState: jest.fn(() => ({
      auth_info: {
        topMenuParameters: {
          allRepresentations: ['active-org-id'],
          selectedRepresentationIndex: 0,
        },
      },
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
  companyViewStore: {
    getState: jest.fn(() => ({
      company_loading: false,
      company_loaded: true,
      company_failed: false,
      error: null,

      permissions: ['can_view_comments', 'view_related_projects'],
      company_id: 'f392-test',
      name: 'Test org',
      gov_org_ids: null,
      country: 'SE',
      status: null,
      is_reliable_partner: null,
      terminated: null,
      langCode: null,
      has_subsidiaries: null,
      subsidiaries: [],
      has_combined_report: null,
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
  companyStore: {
    getState: jest.fn(() => ({
      loaded: true,
      loading: false,
      failed: false,
      filter: jest.fn(),
      companies: ['Test org'],
      companyById: {},
      loadMore: false,
      reqs: [],
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
  subscriptionStore: {
    getState: jest.fn(() => ({
      subscription: null,
      subscriptionLoading: false,
      subscriptionError: null,
      subscriptionCreated: false,
      subscriptionCancelled: false,
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
}));

let mockActivate;
let mockDeactivate;

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => {
    mockActivate = jest.fn();
    mockDeactivate = jest.fn();
    return {
      activate: mockActivate,
      deactivate: mockDeactivate,
    };
  })
);

describe('CompanyDetailsViewComponentTest', () => {
  beforeEach(() => {
    // Reset the mock before each test
    jest.resetModules();
  });

  const renderWithReactIntl = (component, locale = 'sv') =>
    render(
      <IntlProvider locale={locale} messages={locale === 'sv' ? sv : en}>
        {component}
      </IntlProvider>
    );

  it('should contain and render sv tab title translation correctly', () => {
    const {container} = renderWithReactIntl(
      <CompanyDetailsViewComponent
        formatting={'modal'}
        companyId={'test-id'}
        companyName={'test-name'}
      />
    )
    expect(container.querySelector('.comments-tab')).toBeInTheDocument();
    expect(container.textContent).toContain('Kommentarer');
  });

  it('should contain and render en tab title translation correctly', () => {
    const {container} = renderWithReactIntl(
      <CompanyDetailsViewComponent
        formatting={'modal'}
        companyId={'test-id'}
        companyName={'test-name'}
      />, 'en'
    );

    expect(container.querySelector('.comments-tab')).toBeInTheDocument();
    expect(container.textContent).toContain('Comments');
  });

  it('should not contain the comments tab without the view permission', () => {
    companyViewStore.getState.mockImplementation(() => ({
      company_loading: false,
      company_loaded: true,
      company_failed: false,
      error: null,

      permissions: ['view_related_projects'],
      company_id: 'f392-test',
      name: 'Test org',
      gov_org_ids: null,
      country: 'SE',
      status: null,
      is_reliable_partner: null,
      terminated: null,
      langCode: null,
      has_subsidiaries: null,
      subsidiaries: [],
      has_combined_report: null,
    }));
    const {container} = renderWithReactIntl(
      <CompanyDetailsViewComponent
        formatting={'modal'}
        companyId={'test-id'}
        companyName={'test-name'}
      />
    )
    expect(container.querySelector('.comments-tab')).not.toBeInTheDocument();
    expect(container.textContent).not.toContain('Kommentarer');
  });
});
