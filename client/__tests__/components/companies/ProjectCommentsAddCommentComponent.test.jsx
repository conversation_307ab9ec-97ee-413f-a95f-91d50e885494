/* global jest, describe, it, expect, beforeEach */
import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectCommentsAddCommentComponent from
  '../../../app/components/companies/ProjectCommentsAddCommentComponent';
import { companyViewStore } from '../../../app/stores/Stores';
import companyProjectCommentsAdd from
  '../../../app/actions/actionCreators/CompanyProjectCommentsAdd';

// Mock the StoreSubscription
let mockActivate;
let mockDeactivate;

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => {
    mockActivate = jest.fn();
    mockDeactivate = jest.fn();
    return {
      activate: mockActivate,
      deactivate: mockDeactivate,
    };
  })
);

jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsAdd', () =>
  jest.fn().mockResolvedValue(true)
);

describe('ProjectCommentsAddCommentComponent', () => {
  const mockComment = {
    loading: false,
    editing: false,
    errors: [],
    data: {
      id: '1',
      author: 'Test User',
      comment: 'This is a test comment',
      created_timestamp: '2023-01-01T12:00:00Z',
      updated_timestamp: null,
      deleted_timestamp: null,
      is_read: false,
      permissions: ['update', 'delete'],
    },
  };

  const renderComponent = (state = {}, props = {}, locale = 'en') => {
    const defaultState = {
      supplier_id: 'test-supplier-123',
      comment_add_in_progress: false,
      comment_add_errors: null,
      ...state,
    };
    jest.clearAllMocks();
    
    companyViewStore.getState = jest.fn(() => defaultState);

    const utils = render(
      <IntlProvider locale={locale}>
        <ProjectCommentsAddCommentComponent {...props}/>
      </IntlProvider>
    );
    
    return {
      ...utils,
      textarea: screen.getByRole('textbox'),
      getSaveButton: () => screen.queryByRole('button', { name: /save/i }),
      getCancelButton: () => document.querySelector('a[href="#"]'),
      getHelpText: () => screen.getByText(/comments are only visible to the main contractor/i),
      getPlaceholder: () => screen.queryByPlaceholderText(/write a comment about the supplier/i),
    };
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with initial state', () => {
    const { textarea, getHelpText, getPlaceholder } = renderComponent();
    
    expect(textarea).toBeInTheDocument();
    expect(getPlaceholder()).toBeInTheDocument();
    expect(getHelpText()).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /save/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /cancel/i })).not.toBeInTheDocument();
  });

  it('shows save and cancel buttons when textarea is focused', () => {
    const { textarea } = renderComponent();
    
    fireEvent.focus(textarea);
    
    expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
    expect(document.querySelector('a[href="#"]')).toHaveTextContent('cancel');
  });

  it('enables save button only when there is content', () => {
    const { textarea } = renderComponent();
    
    fireEvent.focus(textarea);
    const saveButton = screen.getByRole('button', { name: /save/i });
    
    // Initially disabled
    expect(saveButton).toBeDisabled();
    
    // Type some text
    fireEvent.input(textarea, { target: { value: 'Test comment' } });
    
    // Should be enabled with content
    expect(saveButton).not.toBeDisabled();
    
    // Clear the text
    fireEvent.input(textarea, { target: { value: '   ' } });
    
    // Should be disabled with whitespace only
    expect(saveButton).toBeDisabled();
  });

  it('calls companyProjectCommentsAdd with correct parameters on form submit', async () => {
    const { textarea } = renderComponent();
    const testComment = 'This is a test comment';
    
    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: testComment } });
    fireEvent.click(screen.getByRole('button', { name: /save/i }));
    
    await waitFor(() => {
      expect(companyProjectCommentsAdd).toHaveBeenCalledWith(
        'test-supplier-123',
        testComment.trim()
      );
    });
  });

  it('clears the input after successful submission', async () => {
    const { textarea } = renderComponent();
    const testComment = 'This is a test comment';
    
    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: testComment } });
    fireEvent.click(screen.getByRole('button', { name: /save/i }));
    
    await waitFor(() => {
      expect(textarea.value).toBe('');
    });
  });

  it('clears the input when cancel is clicked', () => {
    const { textarea } = renderComponent();
    const testComment = 'This is a test comment';
    
    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: testComment } });
    
    // Verify text is there
    expect(textarea.value).toBe(testComment);
    
    // Click cancel
    fireEvent.click(screen.getByRole('button', { name: /cancel/i }));
    
    // Text should be cleared
    expect(textarea.value).toBe('');
  });

  it('disables the textarea and save button when comment_add_in_progress is true', () => {
    const { textarea } = renderComponent({ 
      comment_add_in_progress: true 
    });
    
    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: 'Test comment' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    
    expect(textarea).toBeDisabled();
    expect(saveButton).toBeDisabled();
  });

  it('displays error messages when comment_add_errors is present', () => {
    const errorMessage = 'This is an error message';
    renderComponent({ 
      comment_add_errors: [{ id: 'test.error', defaultMessage: errorMessage }] 
    });
    
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('handles store subscription on mount and unmount', () => {
    const { unmount } = renderComponent();
    
    // Check if StoreSubscription was created and activated
    expect(mockActivate).toHaveBeenCalledTimes(1);
    
    // Check if deactivate is called on unmount
    unmount();
    expect(mockDeactivate).toHaveBeenCalledTimes(1);
  });

  it('disables the submit button when in edit mode and input is unchanged', () => {
    renderComponent({ 
      comment_map: {
        [mockComment.data.id]: {
          ...mockComment,
          editing: true,
        }
      },
    }, { editCommentId: mockComment.data.id, editCommentText: mockComment.data.comment });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    
    expect(saveButton).toBeDisabled();
  });

  it('enabled the submit button when in edit mode and input is changed', () => {
    const { textarea } = renderComponent({ 
      comment_map: {
        [mockComment.data.id]: {
          ...mockComment,
          editing: true,
        }
      },
    }, { editCommentId: mockComment.data.id, editCommentText: mockComment.data.comment });
    
    fireEvent.focus(textarea);
    fireEvent.input(textarea, { target: { value: 'New content' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    
    expect(saveButton).not.toBeDisabled();
  });

  it('disables the textarea and submit button in edit mode when comment is loading', () => {
    renderComponent({ 
      comment_map: {
        [mockComment.data.id]: {
          ...mockComment,
          editing: true,
          loading: true,
        }
      },
    }, { editCommentId: mockComment.data.id, editCommentText: mockComment.data.comment });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    const textarea = screen.getByRole('textbox');
    
    expect(saveButton).toBeDisabled();
    expect(textarea).toBeDisabled();
  });
});
