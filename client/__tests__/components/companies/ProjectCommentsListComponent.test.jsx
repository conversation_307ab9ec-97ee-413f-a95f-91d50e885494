/* global jest, describe, it, expect, beforeEach, afterEach */
import React from 'react';
import { IntlProvider } from 'react-intl';
import { render, screen, fireEvent } from '@testing-library/react';
import ProjectCommentsListComponent from
  '../../../app/components/companies/ProjectCommentsListComponent';
import { companyViewStore } from '../../../app/stores/Stores';

jest.mock('../../../app/actions/actionCreators/CompanyProjectCommentsCloseDeleteConfirmation', () =>
  jest.fn()
);
import companyProjectCommentsCloseDeleteConfirmation
  from '../../../app/actions/actionCreators/CompanyProjectCommentsCloseDeleteConfirmation';

jest.mock('../../../app/stores/Stores', () => ({
  companyViewStore: {
    getState: jest.fn(() => ({
      comments_loading: false,
      comments_loaded: false,
      comments_failed: false,
      comments: [],
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
}));

let mockActivate;
let mockDeactivate;

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => {
    mockActivate = jest.fn();
    mockDeactivate = jest.fn();
    return {
      activate: mockActivate,
      deactivate: mockDeactivate,
    };
  })
);

describe('ProjectCommentsListComponent', () => {
  const mockComments = [
    {
      loading: false,
      editing: false,
      errors: [],
      data: {
        id: '1',
        author: 'Test User',
        comment: 'This is a test comment',
        created_timestamp: '2023-01-01T12:00:00Z',
        updated_timestamp: null,
        deleted_timestamp: null,
        is_read: false,
        permissions: ['update', 'delete'],
      },
    },
    {
      loading: false,
      editing: false,
      errors: [],
      data: {
        id: '2',
        author: 'Another User',
        comment: 'This is an updated comment',
        created_timestamp: '2023-01-01T10:00:00Z',
        updated_timestamp: '2023-01-01T11:00:00Z',
        updater: 'Updater User',
        deleted_timestamp: null,
        is_read: true,
        permissions: [],
      },
    },
    {
      loading: false,
      editing: false,
      errors: [],
      data: {
        id: '3',
        author: 'Deleted User',
        comment: 'This comment was deleted',
        created_timestamp: '2023-01-01T09:00:00Z',
        deleted_timestamp: '2023-01-01T10:00:00Z',
        deleter: 'Admin User',
        is_read: true,
        permissions: [],
      },
    },
  ];

  const renderComponent = async (state = {}, locale='en') => {
    jest.clearAllMocks();
    companyViewStore.getState.mockImplementation(() => ({
      comments_loading: false,
      comments_loaded: false,
      comments_failed: false,
      comments: [],
      ...state,
    }));
    const utils = render(
      <IntlProvider locale={locale}>
        <ProjectCommentsListComponent />
      </IntlProvider>
    );
    return {
      ...utils,
    };
  };

  beforeEach(() => {
    jest.resetModules();
    jest.useFakeTimers().setSystemTime(new Date('2023-01-02T00:00:00Z'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('shows loading spinner when comments are loading', async () => {
    const {container} = await renderComponent({ comments_loading: true });

    expect(container.querySelector('.fa-spinner')).toBeInTheDocument();
  });

  it('shows error message when loading fails', async () => {
    await renderComponent({ comments_failed: true });

    expect(screen.getByText('Failed to load comments')).toBeInTheDocument();
  });

  it('shows empty message when there are no comments', async () => {
    await renderComponent({ comments: [] });

    expect(screen.getByText(/There are no comments for this supplier in this project/))
      .toBeInTheDocument();
  });

  it('displays comments with correct information', async () => {
    const {container} = await renderComponent({
      comments: [mockComments[0]],
    });

    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('This is a test comment')).toBeInTheDocument();
    expect(screen.getByText('edit')).toBeInTheDocument();
    expect(screen.getByText('delete')).toBeInTheDocument();
    expect(container.querySelector('.fa-spinner')).not.toBeInTheDocument();
    expect(container.querySelector('.form-control')).not.toBeInTheDocument();
  });

  it('shows updated indicator when comment was updated', async () => {
    await renderComponent({
      comments: [mockComments[1]],
    });

    expect(screen.getByText('updated')).toBeInTheDocument();
  });

  it('shows deleted state when comment was deleted', async () => {
    await renderComponent({
      comments: [mockComments[2]],
    });

    expect(screen.getByText(/deleted 2023-01-01 10:00 by Admin User/i)).toBeInTheDocument();
  })

  it('hides edit/delete buttons when user lacks permissions', async () => {
    await renderComponent({ 
      comments: [ {
        ...mockComments[0],
        data: {
          ...mockComments[0].data,
          permissions: [],
        },
      }]
    });

    expect(screen.queryByText('edit')).not.toBeInTheDocument();
    expect(screen.queryByText('delete')).not.toBeInTheDocument();
  });

  it('shows confirmation modal when attempting deletion', async () => {
    await renderComponent({ 
      comment_delete_id: mockComments[0].data.id,
      supplier_id: mockComments[0].data.supplier_id,
    });

    expect(screen.getByText('Are you sure you want to delete this comment?')).toBeInTheDocument();
    expect(screen.getByText('delete')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('closes delete confirmation modal when escape key is pressed', async () => {
    await renderComponent({
      comments: [mockComments[0]],
      comment_delete_id: mockComments[0].data.id,
      supplier_id: mockComments[0].data.supplier_id,
    });
    fireEvent.keyUp(document, { key: 'Escape', code: 'Escape'});

    expect(companyProjectCommentsCloseDeleteConfirmation).toHaveBeenCalled();
  });

  it('shows error message if delete fails', async () => {
    await renderComponent({ 
      comment_delete_failed: true,
    });
    expect(screen.getByText('Failed to delete comment.')).toBeInTheDocument();
  });

  it('shows loading spinner if loading is true for a comment', async () => {
    const {container} = await renderComponent({ 
      comments: [ {
        ...mockComments[0],
        loading: true,
      }],
    });
    expect(container.querySelector('.fa-spinner')).toBeInTheDocument();
  });

  it('shows an edit box if editing is true for a comment', async () => {
    const {container} = await renderComponent({ 
      comments: [ {
        ...mockComments[0],
        editing: true,
      }],
      comment_map: {
        [mockComments[0].data.id]: {
          ...mockComments[0],
          editing: true,
        },
      },
    });
    expect(container.querySelector('.fa-spinner')).not.toBeInTheDocument();
    expect(container.querySelector('textarea')).toBeInTheDocument();
  });

  it('shows a disabled edit box if editing and loading is true for a comment', async () => {
    const {container} = await renderComponent({ 
      comments: [ {
        ...mockComments[0],
        editing: true,
        loading: true,
      }],
      comment_map: {
        [mockComments[0].data.id]: {
          ...mockComments[0],
          editing: true,
          loading: true,
        },
      },
    });
    expect(container.querySelector('textarea')).toBeInTheDocument();
    expect(container.querySelector('textarea').disabled).toBe(true);
  });
});
