/* eslint-env jest */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import GeneralErrorPage from '../../app/components/GeneralErrorPage';
import { generalErrorStore } from '../../app/stores/Stores';

beforeEach(() => {
  generalErrorStore.getState.mockReturnValue({
    title: 'exampleError.title',
    details: { en: 'Initial error details' },
  });
});

afterEach(() => {
  jest.clearAllMocks();
});

jest.mock('../../app/stores/Stores', () => ({
  generalErrorStore: {
    getState: jest.fn(),
    addListener: jest.fn(),
    removeListener: jest.fn(),
  },
}));

jest.mock('../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation((store, callback) => {
    return {
      activate: jest.fn(() => store.addListener(callback)),
      deactivate: jest.fn(() => store.removeListener(callback)),
    };
  })
);

const renderWithIntl = (component) => {
  return render(
    <IntlProvider
      locale="en"
      messages={{'exampleError.title': 'This is an error'}}
    >
      {component}
    </IntlProvider>
  );
};

describe.only('GeneralErrorPage', () => {
  it('renders initial state correctly', () => {
    renderWithIntl(<GeneralErrorPage />);

    expect(screen.getByText('This is an error')).toBeInTheDocument();
    expect(screen.getByText('Initial error details')).toBeInTheDocument();
  });
});
