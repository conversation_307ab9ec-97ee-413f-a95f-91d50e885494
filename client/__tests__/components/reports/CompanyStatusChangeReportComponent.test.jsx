/* eslint-env jest */
import React from 'react';
import { IntlProvider } from 'react-intl';
import {MemoryRouter, Route, Routes} from 'react-router-dom';
import { render, fireEvent } from '@testing-library/react';
import en from '../../../app/localizations/en.json';
import CompanyStatusChangeReportComponent
  from '../../../app/components/reports/CompanyStatusChangeReportComponent';
import {featureActive} from '../../../app/helpers/FeatureFlags';
import {SELECTED_MITTID06_ORG_ID_STORAGE_KEY} from '../../../app/Constants';

jest.mock('../../../app/helpers/ApiClient', () => ({
  getActiveOrganisationId: jest.fn(() => 'mockedOrgId'),
  buildApiURL: jest.fn(() => 'mockedApiURL'),
}));

jest.mock('../../../app/helpers/FeatureFlags', () => ({
  featureActive: jest.fn(),
}));

jest.mock('../../../app/stores/Stores', () => ({
  authStore: {
    getState: jest.fn(() => ({
      authState: 'SIGNED_IN',
      auth_info: {
        topMenuParameters: {
          allRepresentations: ['active-org-id'],
          selectedRepresentationIndex: 0,
          selectedRepresentedOrganizationId: 'active-org-id',
        },
      },
    })),
    addChangeListener: jest.fn(),
    addListener: jest.fn(),
    removeChangeListener: jest.fn(),
  },
  statusChangeReportStore: {
    getState: jest.fn(() => ({
      loading: false,
      failed: false,
      report: 'test',
      reportById: {
        reportId: {
          statuses: [],
          companies: [{
            company_id: 'test-id',
            projects: [{
              id: 'project-id',
              names: 'project-name',
              project_ids: ['project-id']
            }]
          }]
        }
      },
    }))
  },
  addChangeListener: jest.fn(),
  addListener: jest.fn(),
  removeChangeListener: jest.fn(),
}));

let mockActivate;
let mockDeactivate;

jest.mock('../../../app/helpers/StoreSubscription', () =>
  jest.fn().mockImplementation(() => {
    mockActivate = jest.fn();
    mockDeactivate = jest.fn();
    return {
      activate: mockActivate,
      deactivate: mockDeactivate,
    };
  })
);

const openSpy = jest.spyOn(window, 'open').mockImplementation(() => {});

describe('CompanyStatusChangeReportComponentTest', () => {
  const renderWithReactIntl = () =>
    render(
      <IntlProvider locale='en' messages={en}>
        <MemoryRouter initialEntries={['/companies/reportId/active-org-id']}>
          <Routes>
            <Route
              path='/companies/:itemId/:orgId'
              element={<CompanyStatusChangeReportComponent />}
            />
          </Routes>
        </MemoryRouter>
      </IntlProvider>
    );
  it('should open the new tab with the change report url at legacy', () => {
    const { getByText } = renderWithReactIntl();
    fireEvent.click(getByText('project-name'));
    expect(openSpy).toHaveBeenCalledWith('/#/projects/project-id', '_blank');
  });

  it('should open the new tab with the change report url with bol prefix at mitt id06', () => {
    window.localStorage.setItem(SELECTED_MITTID06_ORG_ID_STORAGE_KEY, 'active-org-id');
    featureActive.mockImplementation(f => f === 'core_mitt_id06');

    const { getByText } = renderWithReactIntl();
    fireEvent.click(getByText('project-name'));
    expect(openSpy).toHaveBeenCalledWith('/bol#/projects/project-id', '_blank');
  });
});
