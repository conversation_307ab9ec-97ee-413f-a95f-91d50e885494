/* eslint-env jest */

import React from 'react';
import { IntlProvider } from 'react-intl';
import { MemoryRouter } from 'react-router-dom';
import { render } from '@testing-library/react';
import en from '../../../app/localizations/en.json';
import CompanyReportRowComponent
  from '../../../app/components/reports/CompanyReportRowComponent';
import {featureActive} from '../../../app/helpers/FeatureFlags';

jest.mock('../../../app/helpers/ApiClient', () => ({
  getActiveOrganisationId: jest.fn(() => 'mockedOrgId'),
  buildApiURL: jest.fn(() => 'mockedApiURL'),
}));

jest.mock('../../../app/helpers/FeatureFlags', () => ({
  featureActive: jest.fn(),
}));

describe('CompanyReportRowComponentTest', () => {
  const renderWithReactIntl = component =>
    render(
      <IntlProvider locale='en' messages={en}>
        <MemoryRouter>
          {component}
        </MemoryRouter>
      </IntlProvider>
    );
  it('should have the correct company details url at legacy', () => {

    const { container} = renderWithReactIntl(
      <CompanyReportRowComponent
        company_resource_id='org-id'
        company_id='company-id'
        company_status='ok'
        name='Testing AB'
        report_available={true}
      />
    );
    expect(container.querySelector('a[href="/#/companies/org-id"]')).toBeInTheDocument();
  })

  it('should have the bol prefixed company details url at mitt id06', () => {
    featureActive.mockImplementation(f => f === 'core_mitt_id06');
    const { container} = renderWithReactIntl(
      <CompanyReportRowComponent
        company_resource_id='org-id'
        company_id='company-id'
        company_status='ok'
        name='Testing AB'
        report_available={true}
      />
    );
    expect(container.querySelector('a[href="/bol#/companies/org-id"]')).toBeInTheDocument();
  })
})
