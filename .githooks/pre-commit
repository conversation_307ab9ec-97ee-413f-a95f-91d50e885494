#!/bin/sh

SERVER_SUBFOLDER="server/"

run_make_commands() {
    CURRENT_DIR=$(pwd)
    
    # Change to the specified subfolder
    cd "$SERVER_SUBFOLDER"
    
    MAKE_COMMANDS=(
        "make autopep8"
        "make flake8"
        "make mypy"
        "make bandit"
    )
    
    for cmd in "${MAKE_COMMANDS[@]}"; do
        echo "Running: $cmd"
        $cmd
        
        # Stop if any command fails
        if [ $? -ne 0 ]; then
            cd "$CURRENT_DIR"
            exit 1
        fi
    done
    cd "$CURRENT_DIR"
}

if [[ -n $(git diff) ]]; then
    echo "Error: You have unstaged changes. Commit aborted."
    echo "Please stage your changes with 'git add' or stash them before committing."
    # Optionally, show the unstaged files:
    echo "\nUnstaged files:"
    git diff --name-only
    exit 1
fi

run_make_commands

if [[ -n $(git diff) ]]; then
    echo "Error: You have unstaged changes. Commit aborted."
    echo "Please stage your changes with 'git add' or stash them before committing."
    # Optionally, show the unstaged files:
    echo "\nUnstaged files:"
    git diff --name-only
    exit 1
fi
# If we reach here, there are no staged changes
echo "No staged changes detected, continuing with commit..."

